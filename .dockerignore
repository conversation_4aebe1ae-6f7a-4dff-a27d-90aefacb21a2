# Docker ignore file for DSPy Multi-Agent System API

# Git and version control
.git
.gitignore
.gitattributes

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Testing and coverage
.tox/
.coverage
.pytest_cache/
.coverage.*
htmlcov/
.nox/
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/
.readthedocs.yml
*.md
!README.md

# Logs and temporary files
*.log
logs/
*.tmp
*.temp

# Data directories (will be mounted as volumes)
data/
cache/
monitoring/
checkpoints/

# Docker files (except the ones we need)
Dockerfile.*
docker-compose.*.yml
.dockerignore

# Development and testing files
test_*.py
tests/
demo_*.py
check_*.py
inspect_*.py
simple_*.py

# Configuration files with sensitive data
.env.*
config.local.yaml
config.prod.yaml

# Build artifacts
*.tar.gz
*.zip
*.rar

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Database files (will be in volumes)
*.db
*.db-shm
*.db-wal
*.sqlite
*.sqlite3

# Training and model files
training_data_export_*.json
workflow_result_*.json
*.pkl
*.pickle

# Large files and archives
*.tar
*.gz
*.bz2
*.xz

# Temporary Python files
*.pyc
*.pyo
*.pyd
.Python

# Node.js (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backup files
*.bak
*.backup
*.old

# API keys and secrets (should be in environment variables)
api_keys.txt
secrets.txt
.secrets
