#!/bin/bash

# DSPy Multi-Agent System API Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "Docker and Docker Compose are available"
}

# Check environment file
check_env() {
    if [ ! -f .env ]; then
        log_warning ".env file not found. Creating from template..."
        cp .env.example .env
        log_warning "Please edit .env file with your API keys before continuing."
        log_info "Required: OPENAI_API_KEY"
        log_info "Optional: SERPER_API_KEY, API_KEY"
        read -p "Press Enter after editing .env file..."
    fi
    
    # Check for required environment variables
    source .env
    
    if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "sk-proj-your-openai-api-key-here" ]; then
        log_error "OPENAI_API_KEY is not set in .env file"
        exit 1
    fi
    
    log_success "Environment configuration validated"
}

# Build and start services
deploy() {
    local mode=$1
    
    log_info "Starting deployment in $mode mode..."
    
    # Pull latest images
    log_info "Pulling latest images..."
    if [ "$mode" = "production" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml pull
    else
        docker-compose pull
    fi
    
    # Build application image
    log_info "Building application image..."
    docker-compose build --no-cache
    
    # Start services
    log_info "Starting services..."
    if [ "$mode" = "production" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    else
        docker-compose up -d
    fi
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # Check health
    check_health
}

# Check service health
check_health() {
    log_info "Checking service health..."
    
    # Check API health
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/api/v1/health &> /dev/null; then
            log_success "API is healthy!"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "API health check failed after $max_attempts attempts"
            docker-compose logs dspy-api
            exit 1
        fi
        
        log_info "Attempt $attempt/$max_attempts - waiting for API..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    # Show service status
    docker-compose ps
}

# Show deployment information
show_info() {
    local mode=$1
    
    log_success "Deployment completed successfully!"
    echo
    log_info "Service URLs:"
    echo "  📚 API Documentation: http://localhost:8000/docs"
    echo "  🔍 Health Check: http://localhost:8000/api/v1/health"
    echo "  📊 Detailed Health: http://localhost:8000/api/v1/health/detailed"
    echo "  🔌 WebSocket: ws://localhost:8000/api/v1/ws/workflows/{workflow_id}"
    
    if [ "$mode" = "production" ]; then
        echo "  🌐 Nginx Proxy: http://localhost"
    fi
    
    echo
    log_info "Useful commands:"
    echo "  📋 View logs: docker-compose logs -f"
    echo "  🔄 Restart: docker-compose restart"
    echo "  🛑 Stop: docker-compose down"
    echo "  📊 Status: docker-compose ps"
}

# Cleanup function
cleanup() {
    log_info "Stopping services..."
    docker-compose down
    log_success "Services stopped"
}

# Main script
main() {
    echo "🚀 DSPy Multi-Agent System API Deployment"
    echo "=========================================="
    
    # Parse arguments
    local mode="development"
    local action="deploy"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--production)
                mode="production"
                shift
                ;;
            -d|--development)
                mode="development"
                shift
                ;;
            --stop)
                action="stop"
                shift
                ;;
            --restart)
                action="restart"
                shift
                ;;
            --logs)
                action="logs"
                shift
                ;;
            --status)
                action="status"
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  -p, --production    Deploy in production mode"
                echo "  -d, --development   Deploy in development mode (default)"
                echo "  --stop              Stop services"
                echo "  --restart           Restart services"
                echo "  --logs              Show logs"
                echo "  --status            Show status"
                echo "  -h, --help          Show this help"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Execute action
    case $action in
        deploy)
            check_docker
            check_env
            deploy $mode
            show_info $mode
            ;;
        stop)
            cleanup
            ;;
        restart)
            log_info "Restarting services..."
            docker-compose restart
            check_health
            log_success "Services restarted"
            ;;
        logs)
            docker-compose logs -f
            ;;
        status)
            docker-compose ps
            ;;
    esac
}

# Handle Ctrl+C
trap cleanup INT

# Run main function
main "$@"
