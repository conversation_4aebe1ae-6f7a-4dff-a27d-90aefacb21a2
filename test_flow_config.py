#!/usr/bin/env python3
"""Test script to check what config is passed to AdvancedCoordinationFlow"""

import sys
sys.path.insert(0, 'src')

def test_flow_config():
    """Test the config passed to AdvancedCoordinationFlow"""
    print("🔍 Testing AdvancedCoordinationFlow Config Passing")
    print("=" * 60)
    
    # Step 1: Load config using main.py method
    from main import load_config_file
    config_data = load_config_file()
    
    print("Step 1: Config from load_config_file()")
    eval_config = config_data.get('evaluation', {})
    eval_enabled = eval_config.get('enabled', False)
    print(f"   evaluation.enabled: {eval_enabled} (type: {type(eval_enabled)})")
    print()
    
    # Step 2: Simulate what happens in main() and run_workflow()
    print("Step 2: Simulating main() -> run_workflow() flow")
    
    # From main()
    args_enhanced_flow = False  # Simulate --enhanced-flow not passed
    use_enhanced_flow = (
        args_enhanced_flow or 
        config_data.get('ENHANCED_FLOW', False) or 
        config_data.get('enhanced_flow', {}).get('enabled', False)
    )
    
    print(f"   use_enhanced_flow: {use_enhanced_flow}")
    print(f"   ENHANCED_FLOW: {config_data.get('ENHANCED_FLOW', False)}")
    print(f"   enhanced_flow.enabled: {config_data.get('enhanced_flow', {}).get('enabled', False)}")
    print()
    
    # Step 3: Check if config_data is modified between load and pass
    print("Step 3: Config integrity check")
    eval_config_after = config_data.get('evaluation', {})
    eval_enabled_after = eval_config_after.get('enabled', False)
    
    if eval_enabled == eval_enabled_after:
        print("✅ Config unchanged between load and flow creation")
    else:
        print("❌ Config was modified!")
        print(f"   Original: {eval_enabled}")
        print(f"   Current: {eval_enabled_after}")
    print()
    
    # Step 4: Test what the flow constructor actually receives
    print("Step 4: Testing what AdvancedCoordinationFlow receives")
    
    # Create a custom AdvancedCoordinationFlow class to intercept the config
    import uuid
    from orchestration.flows.advanced_coordination_flow import Flow, WorkflowState
    
    class TestAdvancedCoordinationFlow:
        def __init__(self, config):
            print(f"   🔍 AdvancedCoordinationFlow.__init__ called")
            print(f"   🔍 Config type: {type(config)}")
            print(f"   🔍 Config keys: {list(config.keys()) if isinstance(config, dict) else 'Not a dict'}")
            
            if isinstance(config, dict):
                eval_section = config.get('evaluation', 'MISSING')
                print(f"   🔍 evaluation section: {eval_section}")
                
                if isinstance(eval_section, dict):
                    eval_enabled_in_flow = eval_section.get('enabled', 'MISSING')
                    print(f"   🔍 evaluation.enabled in flow: {eval_enabled_in_flow} (type: {type(eval_enabled_in_flow)})")
                else:
                    print(f"   ❌ evaluation section is not a dict: {type(eval_section)}")
            else:
                print(f"   ❌ Config is not a dict!")
    
    # Test flow creation
    try:
        test_flow = TestAdvancedCoordinationFlow(config_data)
        print("✅ Flow creation test completed")
    except Exception as e:
        print(f"❌ Flow creation test failed: {e}")
    
    print()
    print("🎯 Summary:")
    print(f"   Config loads with evaluation.enabled = {eval_enabled}")
    print(f"   Config should be passed to flow unchanged")
    print(f"   use_enhanced_flow = {use_enhanced_flow}")

if __name__ == "__main__":
    test_flow_config() 