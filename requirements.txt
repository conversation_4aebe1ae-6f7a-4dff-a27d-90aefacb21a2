# Core AI/ML Framework Dependencies
dspy>=2.6.0
crewai[tools]>=0.120.0
langchain>=0.3.0
langchain-community>=0.3.0
langchain-openai>=0.2.0
openai>=1.70.0

# Vector Database Dependencies
faiss-cpu>=1.11.0
chromadb>=0.5.0
qdrant-client>=1.14.0
# pymilvus>=2.4.0  # Optional - uncomment if using Milvus

# Embedding Service Dependencies
sentence-transformers>=3.0.0
tiktoken>=0.9.0
cohere>=5.0.0  # Optional

# Search and Knowledge Dependencies
duckduckgo-search>=8.0.0
wikipedia>=1.4.0

# Web scraping and analysis dependencies
requests>=2.32.0
beautifulsoup4>=4.13.0
chardet>=5.2.0
lxml>=5.4.0

# Data Processing and Analytics
pandas>=2.2.0
numpy>=2.2.0
matplotlib>=3.10.0
seaborn>=0.13.0

# System Monitoring Dependencies
psutil>=7.0.0

# Multimodal Processing Dependencies
Pillow>=11.0.0
PyPDF2>=3.0.0
# pypdfium2>=4.0.0  # Alternative PDF processor

# Configuration and Utilities
pyyaml>=6.0
pydantic>=2.11.0
python-dateutil>=2.9.0

# Database Dependencies
aiosqlite>=0.20.0

# Development and Testing Dependencies
pytest>=8.3.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0

# Optional Performance Dependencies
# uvloop>=0.19.0  # For async performance on Linux/macOS
# orjson>=3.9.0   # Fast JSON processing

# Production Deployment Dependencies (Optional)
# uvicorn>=0.30.0
# fastapi>=0.110.0
# gunicorn>=22.0.0 