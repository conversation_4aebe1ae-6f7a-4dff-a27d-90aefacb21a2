#!/usr/bin/env python3
"""
Simple WebSocket test for the API validation.
"""

import asyncio
import websockets
import json


async def test_websocket():
    """Test WebSocket connection and basic functionality."""
    uri = "ws://localhost:8000/api/v1/ws/workflows/test-workflow-123?api_key=dev-api-key-12345"
    
    try:
        print("🔌 Connecting to WebSocket...")
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected successfully")
            
            # Wait for initial message or timeout
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Received: {message}")
            except asyncio.TimeoutError:
                print("⏰ No initial message received (timeout)")
            
            # Send a test message
            test_message = {"type": "ping", "data": "test"}
            await websocket.send(json.dumps(test_message))
            print(f"📤 Sent: {test_message}")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Response: {response}")
            except asyncio.TimeoutError:
                print("⏰ No response received (timeout)")
                
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")


if __name__ == "__main__":
    print("🧪 WebSocket API Test")
    print("=" * 30)
    asyncio.run(test_websocket())
