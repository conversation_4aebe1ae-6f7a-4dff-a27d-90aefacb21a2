#!/usr/bin/env python3
"""Debug script to test tools directly"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_web_search_tool():
    """Test WebSearchTool directly."""
    print("🔧 Testing WebSearchTool...")
    
    try:
        from tools.search.web_search_tool import WebSearchTool
        
        tool = WebSearchTool()
        print("✅ WebSearchTool imported and created")
        
        # Test with a simple query
        query = "artificial intelligence benefits"
        print(f"🔍 Testing query: '{query}'")
        
        result = tool._run(query, max_results=3)
        
        print(f"📊 Result length: {len(result)} characters")
        print(f"📝 Result type: {type(result)}")
        print(f"📄 First 300 chars:\n{result[:300]}...")
        
        if len(result) < 50:
            print("❌ Result too short - tool may not be working properly")
            return False
        else:
            print("✅ WebSearchTool appears to be working")
            return True
            
    except Exception as e:
        print(f"❌ WebSearchTool failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_document_tool():
    """Test DocumentRetrievalTool directly."""
    print("\n🔧 Testing DocumentRetrievalTool...")
    
    try:
        from tools.processors.document_retrieval_tool import DocumentRetrievalTool
        
        tool = DocumentRetrievalTool()
        print("✅ DocumentRetrievalTool imported and created")
        
        # Test with a simple URL
        url = "https://httpbin.org/html"
        print(f"🔍 Testing URL: '{url}'")
        
        result = tool._run(url, max_length=500)
        
        print(f"📊 Result length: {len(result)} characters")
        print(f"📝 Result type: {type(result)}")
        print(f"📄 First 200 chars:\n{result[:200]}...")
        
        if len(result) < 50:
            print("❌ Result too short - tool may not be working properly")
            return False
        else:
            print("✅ DocumentRetrievalTool appears to be working")
            return True
            
    except Exception as e:
        print(f"❌ DocumentRetrievalTool failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_tool():
    """Test DataAnalysisTool directly."""
    print("\n🔧 Testing DataAnalysisTool...")
    
    try:
        from tools.processors.data_analysis_tool import DataAnalysisTool
        
        tool = DataAnalysisTool()
        print("✅ DataAnalysisTool imported and created")
        
        # Test with sample data
        data = "Artificial intelligence is revolutionizing many industries. Machine learning algorithms are becoming more sophisticated. Deep learning neural networks can process vast amounts of data."
        print(f"🔍 Testing data analysis...")
        
        result = tool._run(data, analysis_type="summary")
        
        print(f"📊 Result length: {len(result)} characters")
        print(f"📝 Result type: {type(result)}")
        print(f"📄 First 300 chars:\n{result[:300]}...")
        
        if len(result) < 50:
            print("❌ Result too short - tool may not be working properly")
            return False
        else:
            print("✅ DataAnalysisTool appears to be working")
            return True
            
    except Exception as e:
        print(f"❌ DataAnalysisTool failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tool tests."""
    print("🔍 TOOL DEBUGGING SESSION")
    print("="*50)
    
    results = []
    
    # Test each tool
    results.append(("WebSearchTool", test_web_search_tool()))
    results.append(("DocumentRetrievalTool", test_document_tool()))
    results.append(("DataAnalysisTool", test_analysis_tool()))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TOOL TEST SUMMARY")
    print("="*50)
    
    passed = 0
    for tool_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {tool_name}")
        if success:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tools working properly")
    
    if passed == len(results):
        print("\n✅ All tools working - the issue may be in the workflow integration")
    else:
        print(f"\n❌ {len(results) - passed} tools failing - this explains the empty results")
    
    print("\n💡 Next steps:")
    if passed < len(results):
        print("  • Fix the failing tools first")
        print("  • Check network connectivity")
        print("  • Verify dependencies are installed")
    else:
        print("  • Check CrewAI agent task configurations")
        print("  • Verify tool integration in workflows")
        print("  • Check for data passing between agents")

if __name__ == "__main__":
    main() 