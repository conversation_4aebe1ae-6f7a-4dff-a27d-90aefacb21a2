# DSPy Multi-Agent System API Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# API Configuration
# =============================================================================
API_KEY=your-secure-api-key-here
API_PORT=8000
LOG_LEVEL=info
WORKERS=1

# =============================================================================
# AI Service API Keys (Required)
# =============================================================================
# OpenAI API Key (Required for LLM functionality)
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Serper API Key (Optional - for enhanced web search)
SERPER_API_KEY=your-serper-api-key-here

# =============================================================================
# Redis Configuration (Optional - SQLite fallback available)
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# =============================================================================
# File Upload Configuration
# =============================================================================
MAX_FILE_SIZE=10485760
UPLOAD_DIR=/app/data/uploads

# =============================================================================
# Production Configuration (Optional)
# =============================================================================
# Nginx ports (only used with production profile)
NGINX_PORT=80
NGINX_SSL_PORT=443

# =============================================================================
# Development Configuration
# =============================================================================
# Set to true for development mode
DEBUG_MODE=false

# =============================================================================
# Example Values for Development
# =============================================================================
# For development, you can use these example values:
# API_KEY=dev-api-key-12345
# OPENAI_API_KEY=sk-proj-your-actual-openai-key
# SERPER_API_KEY=your-actual-serper-key
# LOG_LEVEL=debug
