# DSPy Multi-Agent System API Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# API Configuration
# =============================================================================
API_KEY=your-secure-api-key-here
API_PORT=8000
LOG_LEVEL=info
WORKERS=1

# =============================================================================
# AI Service API Keys (Required)
# =============================================================================
# OpenAI API Key (Required for LLM functionality)
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Serper API Key (Optional - for enhanced web search)
SERPER_API_KEY=your-serper-api-key-here

# =============================================================================
# Redis Configuration (Optional - SQLite fallback available)
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# =============================================================================
# Vector Database Configuration
# =============================================================================
# Choose vector database: chroma (default) or qdrant (production)
VECTOR_DB_TYPE=chroma

# ChromaDB Configuration (Default - Good for development)
VECTOR_DB_IMAGE=chromadb/chroma:latest
VECTOR_DB_PORT=8001
VECTOR_DB_INTERNAL_PORT=8000
CHROMA_HOST=vector-db
CHROMA_PORT=8000
CHROMA_TELEMETRY=true

# Qdrant Configuration (Production Alternative)
# To use Qdrant, set: VECTOR_DB_TYPE=qdrant and VECTOR_DB_IMAGE=qdrant/qdrant:latest
# VECTOR_DB_IMAGE=qdrant/qdrant:latest
# VECTOR_DB_PORT=6333
# VECTOR_DB_INTERNAL_PORT=6333
# QDRANT_HOST=vector-db
# QDRANT_PORT=6333
# QDRANT_API_KEY=your-qdrant-api-key
# QDRANT_LOG_LEVEL=INFO

# =============================================================================
# File Upload Configuration
# =============================================================================
MAX_FILE_SIZE=10485760
UPLOAD_DIR=/app/data/uploads



# =============================================================================
# Development Configuration
# =============================================================================
# Set to true for development mode
DEBUG_MODE=false

# =============================================================================
# Example Values for Development
# =============================================================================
# For development, you can use these example values:
# API_KEY=dev-api-key-12345
# OPENAI_API_KEY=sk-proj-your-actual-openai-key
# SERPER_API_KEY=your-actual-serper-key
# LOG_LEVEL=debug
