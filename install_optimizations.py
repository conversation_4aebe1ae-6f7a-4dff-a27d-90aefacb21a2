#!/usr/bin/env python3
"""
Installation and testing script for Enhanced Flow API Performance Optimizations.

This script:
1. Installs required dependencies
2. Tests the optimized components
3. Runs performance benchmarks
4. Validates the improvements
"""

import asyncio
import subprocess
import sys
import time
import json
from pathlib import Path

def install_dependencies():
    """Install required dependencies for optimizations."""
    print("🔧 Installing optimization dependencies...")
    
    try:
        # Install aiosqlite for async database operations
        subprocess.check_call([sys.executable, "-m", "pip", "install", "aiosqlite>=0.20.0"])
        print("   ✅ aiosqlite installed successfully")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Failed to install dependencies: {e}")
        return False

async def test_async_storage():
    """Test the async session storage performance."""
    print("🗄️  Testing async session storage...")
    
    try:
        from src.api.utils.async_session_storage import AsyncSessionStorage
        
        storage = AsyncSessionStorage(db_path="test_performance.db")
        
        # Test session creation
        start_time = time.time()
        success = await storage.create_session("test_session", {"test": "data"})
        creation_time = time.time() - start_time
        
        if success:
            print(f"   ✅ Session creation: {creation_time*1000:.2f}ms")
        else:
            print("   ❌ Session creation failed")
            return False
        
        # Test session retrieval
        start_time = time.time()
        session_data = await storage.get_session("test_session")
        retrieval_time = time.time() - start_time
        
        if session_data:
            print(f"   ✅ Session retrieval: {retrieval_time*1000:.2f}ms")
        else:
            print("   ❌ Session retrieval failed")
            return False
        
        # Test workflow storage
        start_time = time.time()
        workflow_success = await storage.store_workflow(
            "test_workflow", "test_session", "pending", {"test": "workflow"}
        )
        workflow_time = time.time() - start_time
        
        if workflow_success:
            print(f"   ✅ Workflow storage: {workflow_time*1000:.2f}ms")
        else:
            print("   ❌ Workflow storage failed")
            return False
        
        # Cleanup
        await storage.close()
        Path("test_performance.db").unlink(missing_ok=True)
        
        print("   🎉 Async storage tests passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Async storage test failed: {e}")
        return False

async def test_component_manager():
    """Test the component manager initialization."""
    print("🔧 Testing component manager...")
    
    try:
        from src.api.services.component_manager import ComponentManager
        
        manager = ComponentManager()
        
        # Test component initialization
        start_time = time.time()
        await manager.initialize_all_components()
        init_time = time.time() - start_time
        
        print(f"   ✅ Component initialization: {init_time:.2f}s")
        
        # Test component retrieval
        start_time = time.time()
        system = manager.get_multi_agent_system()
        retrieval_time = time.time() - start_time
        
        if system:
            print(f"   ✅ Component retrieval: {retrieval_time*1000:.2f}ms")
        else:
            print("   ❌ Component retrieval failed")
            return False
        
        # Test health check
        health = await manager.health_check()
        if health.get("initialized"):
            print("   ✅ Component health check passed")
        else:
            print("   ❌ Component health check failed")
            return False
        
        print("   🎉 Component manager tests passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Component manager test failed: {e}")
        return False

async def test_optimized_workflow_service():
    """Test the optimized workflow service."""
    print("⚡ Testing optimized workflow service...")
    
    try:
        from src.api.services.workflow_service import get_optimized_workflow_service
        
        service = await get_optimized_workflow_service()
        
        # Test workflow creation
        start_time = time.time()
        workflow_id = await service.start_workflow(
            question="Test question for performance",
            session_id="test_session_perf",
            workflow_type="standard"
        )
        creation_time = time.time() - start_time
        
        if workflow_id:
            print(f"   ✅ Workflow creation: {creation_time*1000:.2f}ms")
        else:
            print("   ❌ Workflow creation failed")
            return False
        
        # Test status retrieval
        start_time = time.time()
        status = await service.get_workflow_status(workflow_id)
        status_time = time.time() - start_time
        
        if status:
            print(f"   ✅ Status retrieval: {status_time*1000:.2f}ms")
        else:
            print("   ❌ Status retrieval failed")
            return False
        
        print("   🎉 Optimized workflow service tests passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Optimized workflow service test failed: {e}")
        return False

async def run_performance_benchmark():
    """Run performance benchmarks to measure improvements."""
    print("📊 Running performance benchmarks...")
    
    try:
        from src.api.services.workflow_service import get_optimized_workflow_service
        
        service = await get_optimized_workflow_service()
        
        # Benchmark workflow creation
        print("   🔄 Benchmarking workflow creation...")
        creation_times = []
        
        for i in range(10):
            start_time = time.time()
            workflow_id = await service.start_workflow(
                question=f"Benchmark question {i}",
                session_id=f"benchmark_session_{i}",
                workflow_type="standard"
            )
            creation_time = time.time() - start_time
            creation_times.append(creation_time)
        
        avg_creation_time = sum(creation_times) / len(creation_times)
        min_creation_time = min(creation_times)
        max_creation_time = max(creation_times)
        
        print(f"   📈 Workflow Creation Benchmark:")
        print(f"      Average: {avg_creation_time*1000:.2f}ms")
        print(f"      Min: {min_creation_time*1000:.2f}ms")
        print(f"      Max: {max_creation_time*1000:.2f}ms")
        
        # Benchmark status retrieval
        print("   🔄 Benchmarking status retrieval...")
        status_times = []
        
        for i in range(10):
            start_time = time.time()
            # Use the last created workflow for status checks
            status = await service.get_workflow_status(workflow_id)
            status_time = time.time() - start_time
            status_times.append(status_time)
        
        avg_status_time = sum(status_times) / len(status_times)
        min_status_time = min(status_times)
        max_status_time = max(status_times)
        
        print(f"   📈 Status Retrieval Benchmark:")
        print(f"      Average: {avg_status_time*1000:.2f}ms")
        print(f"      Min: {min_status_time*1000:.2f}ms")
        print(f"      Max: {max_status_time*1000:.2f}ms")
        
        # Performance assessment
        if avg_creation_time < 0.1:  # < 100ms
            print("   🎉 EXCELLENT: Workflow creation under 100ms!")
        elif avg_creation_time < 0.2:  # < 200ms
            print("   ✅ GOOD: Workflow creation under 200ms")
        else:
            print("   ⚠️  NEEDS IMPROVEMENT: Workflow creation over 200ms")
        
        if avg_status_time < 0.02:  # < 20ms
            print("   🎉 EXCELLENT: Status retrieval under 20ms!")
        elif avg_status_time < 0.05:  # < 50ms
            print("   ✅ GOOD: Status retrieval under 50ms")
        else:
            print("   ⚠️  NEEDS IMPROVEMENT: Status retrieval over 50ms")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Performance benchmark failed: {e}")
        return False

async def main():
    """Main installation and testing function."""
    print("🚀 Enhanced Flow API Performance Optimization Installation")
    print("=" * 60)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed. Exiting.")
        return False
    
    print()
    
    # Step 2: Test async storage
    if not await test_async_storage():
        print("❌ Async storage tests failed. Exiting.")
        return False
    
    print()
    
    # Step 3: Test component manager
    if not await test_component_manager():
        print("❌ Component manager tests failed. Exiting.")
        return False
    
    print()
    
    # Step 4: Test optimized workflow service
    if not await test_optimized_workflow_service():
        print("❌ Optimized workflow service tests failed. Exiting.")
        return False
    
    print()
    
    # Step 5: Run performance benchmarks
    if not await run_performance_benchmark():
        print("❌ Performance benchmarks failed. Exiting.")
        return False
    
    print()
    print("=" * 60)
    print("🎉 ALL OPTIMIZATIONS INSTALLED AND TESTED SUCCESSFULLY!")
    print("=" * 60)
    print()
    print("📋 Next Steps:")
    print("1. Start the optimized API server:")
    print("   python -m src.api.main")
    print()
    print("2. Monitor performance metrics:")
    print("   curl http://localhost:8000/api/v1/performance")
    print()
    print("3. Check health status:")
    print("   curl http://localhost:8000/api/v1/health/detailed")
    print()
    print("4. Read the performance guide:")
    print("   cat PERFORMANCE_OPTIMIZATION_README.md")
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed with error: {e}")
        sys.exit(1)
