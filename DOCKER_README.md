# DSPy Multi-Agent System API - Docker Deployment

This guide covers deploying the DSPy Multi-Agent System API using Docker and Docker Compose.

## 🚀 Quick Start

### Prerequisites

- Docker 20.10+ and Docker Compose 2.0+
- OpenAI API key (required)
- Serper API key (optional, for enhanced web search)

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd test-dspy
cp .env.example .env
```

### 2. Configure Environment

Edit `.env` file with your API keys:

```bash
# Required
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Optional but recommended
SERPER_API_KEY=your-serper-api-key-here
API_KEY=your-secure-api-key-here
```

### 3. Start the Application

```bash
# Development mode (ChromaDB)
docker-compose up -d

# Production mode (optimized)
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 4. Access the API

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/v1/health
- **WebSocket**: ws://localhost:8000/api/v1/ws/workflows/{workflow_id}

## 📋 Configuration

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `OPENAI_API_KEY` | ✅ | - | OpenAI API key for LLM functionality |
| `SERPER_API_KEY` | ❌ | - | Serper API key for enhanced web search |
| `API_KEY` | ❌ | `dev-api-key-12345` | API authentication key |
| `API_PORT` | ❌ | `8000` | Port for the API server |
| `LOG_LEVEL` | ❌ | `info` | Logging level (debug, info, warning, error) |
| `WORKERS` | ❌ | `1` | Number of worker processes |
| `REDIS_HOST` | ❌ | `redis` | Redis hostname |
| `REDIS_PORT` | ❌ | `6379` | Redis port |
| `REDIS_PASSWORD` | ❌ | - | Redis password (if required) |
| `VECTOR_DB_TYPE` | ❌ | `chroma` | Vector database type (chroma/qdrant) |
| `VECTOR_DB_PORT` | ❌ | `8001` | Vector database external port |
| `MAX_FILE_SIZE` | ❌ | `10485760` | Maximum file upload size (bytes) |

### Docker Compose Profiles

- **Default**: API + Redis + ChromaDB
- **Production**: API + Redis + Vector DB (with production optimizations)
- **Qdrant**: API + Redis + Qdrant (high-performance vector database)

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DSPy API      │    │     Redis       │    │  Vector DB      │
│   Container     │───▶│   Container     │    │ (Chroma/Qdrant) │
│   Port 8000     │    │   Port 6379     │    │   Port 8001     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Note**: This is a backend service designed to be called by other backend components. No reverse proxy is included as it will be handled at the infrastructure level.

### Vector Database Options

- **ChromaDB** (Default): File-based, perfect for development and small-scale production
- **Qdrant** (Production): High-performance, scalable vector database for production workloads

## 🔧 Deployment Options

### Development Deployment

```bash
# Start with default configuration
docker-compose up -d

# View logs
docker-compose logs -f dspy-api

# Stop services
docker-compose down
```

### Production Deployment

```bash
# Start with Nginx reverse proxy
docker-compose --profile production up -d

# Scale API instances (if needed)
docker-compose up -d --scale dspy-api=3

# Update and restart
docker-compose pull
docker-compose up -d --force-recreate
```

### Vector Database Options

```bash
# Default: ChromaDB (good for development)
docker-compose up -d

# Production: Qdrant (high-performance)
docker-compose -f docker-compose.yml -f docker-compose.qdrant.yml up -d

# Production optimized with Qdrant
docker-compose -f docker-compose.yml -f docker-compose.qdrant.yml -f docker-compose.prod.yml up -d
```

### Custom Configuration

```bash
# Use custom compose file
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Override environment variables
API_PORT=9000 WORKERS=4 docker-compose up -d
```

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Check container health
docker-compose ps

# API health check
curl http://localhost:8000/api/v1/health

# Detailed health check
curl http://localhost:8000/api/v1/health/detailed
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# Follow API logs
docker-compose logs -f dspy-api

# View Redis logs
docker-compose logs redis

# Execute commands in container
docker-compose exec dspy-api bash
```

### Data Persistence

Data is persisted in Docker volumes:

- `dspy_data`: Application data (databases, uploads)
- `dspy_logs`: Application logs
- `dspy_cache`: Cache files
- `dspy_checkpoints`: Model checkpoints
- `redis_data`: Redis data

### Backup and Restore

```bash
# Backup volumes
docker run --rm -v dspy_data:/data -v $(pwd):/backup alpine tar czf /backup/dspy_data_backup.tar.gz -C /data .

# Restore volumes
docker run --rm -v dspy_data:/data -v $(pwd):/backup alpine tar xzf /backup/dspy_data_backup.tar.gz -C /data
```

## 🔒 Security Considerations

### Production Security

1. **Change default API key**:
   ```bash
   API_KEY=$(openssl rand -hex 32)
   ```

2. **Use Redis password**:
   ```bash
   REDIS_PASSWORD=$(openssl rand -hex 16)
   ```

3. **Enable HTTPS** (add SSL certificates to nginx.conf)

4. **Firewall configuration**:
   - Only expose necessary ports (80, 443)
   - Restrict Redis access to internal network

### Network Security

```bash
# Create custom network
docker network create dspy-secure-network

# Use in compose file
networks:
  default:
    external:
      name: dspy-secure-network
```

## 🚨 Troubleshooting

### Common Issues

1. **Container won't start**:
   ```bash
   docker-compose logs dspy-api
   ```

2. **API key errors**:
   - Verify `OPENAI_API_KEY` is set correctly
   - Check API key permissions and quotas

3. **Redis connection issues**:
   - Application will fallback to SQLite
   - Check Redis container status: `docker-compose ps redis`

4. **Port conflicts**:
   ```bash
   # Use different port
   API_PORT=8080 docker-compose up -d
   ```

5. **Memory issues**:
   ```bash
   # Increase container memory limits
   docker-compose up -d --memory=2g
   ```

### Performance Tuning

```bash
# Increase workers for production
WORKERS=4 docker-compose up -d

# Optimize for high load
docker-compose -f docker-compose.yml -f docker-compose.performance.yml up -d
```

## 📚 Additional Resources

- [API Documentation](./API_README.md)
- [Performance Optimization](./PERFORMANCE_OPTIMIZATION_README.md)
- [Architecture Design](./architecture_design.md)

## 🆘 Support

For issues and questions:
1. Check the logs: `docker-compose logs`
2. Verify configuration: `docker-compose config`
3. Test connectivity: `curl http://localhost:8000/api/v1/health`
