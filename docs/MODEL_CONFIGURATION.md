# Multi-Model Configuration Guide

## 🚀 **OpenAI GPT-4.1 Series Integration**

This system now supports the latest OpenAI GPT-4.1 series models with specialized configurations for different agent types and cost optimization features.

## 📊 **Cost-Effective Primary Configuration (Default)**

The system is configured with the **Primary Configuration** that balances performance and cost:

```yaml
llm:
  # Specialized models for different roles
  teacher_model: "gpt-4.1-mini"      # DSPy optimization teacher ($0.40/$1.60)
  task_manager_model: "gpt-4.1-nano"  # Task coordination ($0.10/$0.40)
  researcher_model: "gpt-4.1-mini"    # Web research ($0.40/$1.60)
  librarian_model: "gpt-4.1-nano"     # Document retrieval ($0.10/$0.40)
  data_processor_model: "gpt-4.1-mini" # Data analysis ($0.40/$1.60)
  writer_model: "gpt-4.1-mini"        # Final synthesis ($0.40/$1.60)
  
  # Cost optimization features
  enable_caching: true         # 75% savings on cached inputs
  use_batch_api: false         # 50% additional savings for non-realtime
  cache_system_prompts: true   # Cache common system prompts
  cache_training_examples: true # Cache DSPy training examples
```

## 🎯 **Model Selection Rationale**

### **GPT-4.1-mini (Primary Workhorse)**
- **Context**: 1M tokens (vs 128K for GPT-4o)
- **Performance**: 87.5% MMLU, 84.1% IFEval (instruction following)
- **Cost**: $0.40/$1.60 per 1M tokens (83% cheaper than GPT-4o)
- **Speed**: Nearly 2x faster than GPT-4o
- **Use Cases**: DSPy teacher, researcher, data processor, writer

### **GPT-4.1-nano (Speed & Cost)**
- **Context**: 1M tokens
- **Performance**: 80.1% MMLU, 74.5% IFEval  
- **Cost**: $0.10/$0.40 per 1M tokens (33% cheaper than GPT-4o-mini)
- **Speed**: Fastest model available (<5 seconds first token)
- **Use Cases**: Task manager, librarian (simple coordination tasks)

## 💰 **Cost Optimization Features**

### **1. Prompt Caching (75% savings)**
- Automatic caching of system prompts and training examples
- 75% discount on cached inputs (up from 50% previously)
- Especially beneficial for DSPy optimization

### **2. Context Window Efficiency**
- 1M token context windows across all models
- Better long-context comprehension
- No additional cost for long context usage

### **3. Batch API Option**
- Additional 50% discount for non-realtime processing
- Set `use_batch_api: true` for background tasks
- Ideal for DSPy training and large document processing

## 🔧 **Configuration Methods**

### **1. YAML Configuration (config.yaml)**
```yaml
llm:
  model_name: "gpt-4.1-mini"  # Default fallback
  teacher_model: "gpt-4.1-mini"
  task_manager_model: "gpt-4.1-nano"
  researcher_model: "gpt-4.1-mini"
  librarian_model: "gpt-4.1-nano"
  data_processor_model: "gpt-4.1-mini"
  writer_model: "gpt-4.1-mini"
  enable_caching: true
  use_batch_api: false
```

### **2. Environment Variables**
```bash
# Specialized models
export TEACHER_MODEL="gpt-4.1-mini"
export TASK_MANAGER_MODEL="gpt-4.1-nano"
export RESEARCHER_MODEL="gpt-4.1-mini"
export LIBRARIAN_MODEL="gpt-4.1-nano"
export DATA_PROCESSOR_MODEL="gpt-4.1-mini"
export WRITER_MODEL="gpt-4.1-mini"

# Cost optimization
export ENABLE_CACHING="true"
export USE_BATCH_API="false"
export CACHE_SYSTEM_PROMPTS="true"
export CACHE_TRAINING_EXAMPLES="true"
```

### **3. Programmatic Access**
```python
from src.infrastructure.config.settings import get_model_for_agent, create_model_config_for_component

# Get model for specific agent
teacher_model = get_model_for_agent("teacher")
researcher_model = get_model_for_agent("researcher")

# Get full config for component
dspy_config = create_model_config_for_component("dspy")
crewai_config = create_model_config_for_component("crewai")
```

## 🚀 **High-Performance Configuration (Optional)**

For maximum quality when cost is less of a concern:

```yaml
llm:
  teacher_model: "gpt-4.1"       # $2.00/$8.00 per 1M tokens
  task_manager_model: "gpt-4.1-mini"
  researcher_model: "gpt-4.1"    # Best for complex research
  librarian_model: "gpt-4.1-mini"
  data_processor_model: "gpt-4.1" # Best for complex analysis
  writer_model: "gpt-4.1"        # Best for synthesis
```

## 📈 **Performance Improvements Over Previous Setup**

### **From gpt-4o-mini to gpt-4.1-mini:**
- ✅ **Context**: 128K → 1M tokens (+8x)
- ✅ **Instruction Following**: +10.5% absolute improvement
- ✅ **Coding**: 52.9% vs 18.2% on Aider diff benchmark
- ✅ **Speed**: Nearly 2x faster
- ✅ **Cost**: Similar pricing but much better performance

### **From gpt-4o-mini to gpt-4.1-nano:**
- ✅ **Speed**: Fastest model available
- ✅ **Cost**: 33% cheaper ($0.10/$0.40 vs $0.15/$0.60)
- ✅ **Context**: 128K → 1M tokens (+8x)
- ✅ **Performance**: 80.1% MMLU (comparable to previous)

## 🔄 **Migration Guide**

### **Automatic Migration**
The system automatically uses the new models. No code changes required for basic usage.

### **Testing the New Configuration**
```bash
# Test DSPy with new teacher model
python test_dspy.py

# Test DSPy optimization with new models
python test_dspy_optimization.py

# Test full system with new model configuration
python test_system.py
```

### **Monitoring Costs**
1. Enable OpenAI usage tracking in your dashboard
2. Monitor token usage with new caching benefits
3. Consider enabling Batch API for non-realtime tasks

## 🛡️ **Best Practices**

### **1. Model Selection**
- Use **gpt-4.1-nano** for simple coordination tasks
- Use **gpt-4.1-mini** for complex reasoning and processing
- Use **gpt-4.1** only when maximum quality is required

### **2. Cost Optimization**
- Always enable caching for production
- Use Batch API for background processing
- Cache system prompts and training examples
- Monitor usage patterns to optimize model selection

### **3. Performance Monitoring**
- Track response times with different models
- Monitor quality metrics across agent types
- A/B test model configurations for specific use cases

## 🔍 **Troubleshooting**

### **Model Not Found Errors**
If you get model not found errors, ensure you're using the latest OpenAI Python client:
```bash
pip install openai --upgrade
```

### **Configuration Not Loading**
Check that your config.yaml file is in the root directory and properly formatted.

### **Performance Issues**
- Verify caching is enabled
- Check that you're using appropriate models for each task
- Monitor token usage to identify bottlenecks

## 📚 **Model Specifications Reference**

| Model | Context | Input Cost | Output Cost | Best For |
|-------|---------|------------|-------------|----------|
| gpt-4.1 | 1M | $2.00/1M | $8.00/1M | Complex reasoning, max quality |
| gpt-4.1-mini | 1M | $0.40/1M | $1.60/1M | Most tasks, best value |
| gpt-4.1-nano | 1M | $0.10/1M | $0.40/1M | Simple tasks, speed critical |

All models support:
- 75% caching discount
- 50% Batch API discount
- 1M token context windows
- Enhanced instruction following
- Better long-context comprehension

---

**Note**: This configuration uses the May 2025 OpenAI model lineup for optimal cost-performance balance in production environments. 