#!/usr/bin/env python3
"""
Demo script showing DSPy workflow step-by-step
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import dspy
from optimization.dspy.qa_modules import MultiAgentQAModule

def demonstrate_dspy_workflow():
    """Demonstrate step-by-step DSPy workflow."""
    
    print("🔥 DSPy Multi-Agent Workflow Demonstration")
    print("="*60)
    
    # Check API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY not set. Please set it first.")
        return
    
    # Step 1: Initialize DSPy
    print("\n📡 Step 1: Initializing DSPy with gpt-4.1-mini...")
    lm = dspy.LM(model='gpt-4.1-mini', max_tokens=1000)
    dspy.configure(lm=lm)
    print("   ✅ DSPy configured with language model")
    
    # Step 2: Create QA System
    print("\n🤖 Step 2: Creating MultiAgentQAModule...")
    print("   - Research Module: ✅ (web search reasoning)")
    print("   - Library Module: ⏭️ (skipped for demo)")
    print("   - Analysis Module: ⏭️ (skipped for demo)")
    print("   - Synthesis Module: ✅ (final answer generation)")
    
    qa_system = MultiAgentQAModule(
        use_research=True,  # Enable web research
        use_library=False,   # Skip library for faster demo
        use_analysis=False   # Skip analysis for faster demo
    )
    print("   ✅ QA system ready")
    
    # Step 3: Process Question
    question = "What are the main advantages of electric vehicles over gasoline cars?"
    print(f"\n🤔 Step 3: Processing Question...")
    print(f"   Question: {question}")
    print("   ⏳ DSPy is thinking... (this takes ~15-30 seconds)")
    
    # Step 4: Get Result
    print("\n🧠 Step 4: DSPy Internal Processing...")
    print("   1. Research Module: Analyzing question context")
    print("   2. Research Module: Generating search-aware reasoning")
    print("   3. Synthesis Module: Combining research insights")
    print("   4. Synthesis Module: Generating final answer with confidence")
    
    try:
        result = qa_system(question=question)
        
        # Step 5: Display Results
        print(f"\n🎯 Step 5: Results Generated!")
        print("="*60)
        print(f"📝 ANSWER:")
        print(f"{result.final_answer}")
        print(f"\n📊 CONFIDENCE:")
        print(f"{result.confidence}")
        print(f"\n📚 EVIDENCE/REASONING:")
        print(f"{result.evidence}")
        
        # Step 6: Explain What Happened
        print(f"\n🔍 Step 6: What DSPy Did Behind The Scenes:")
        print("="*60)
        print("   ✅ Auto-generated optimized prompts for each module")
        print("   ✅ Used chain-of-thought reasoning")
        print("   ✅ Managed context between modules")
        print("   ✅ Applied learned patterns from training")
        print("   ✅ Provided structured output with confidence")
        print("   ✅ Used gpt-4.1-mini efficiently ($0.40/$1.60 per 1M tokens)")
        
        print(f"\n💡 Key DSPy Benefits in This Example:")
        print("   • Automatic prompt optimization (no manual prompt engineering)")
        print("   • Modular reasoning (research → synthesis)")
        print("   • Consistent structured outputs")
        print("   • Built-in confidence scoring")
        print("   • Cost-effective model usage")
        
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        print("   💡 Make sure OPENAI_API_KEY is set correctly")

if __name__ == "__main__":
    demonstrate_dspy_workflow() 