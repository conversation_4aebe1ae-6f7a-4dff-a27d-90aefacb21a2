#!/usr/bin/env python3
"""
Demo: Training Data Collection System

This demonstrates the core training data collection functionality
that addresses your question about the 500-example dataset.
"""

import asyncio
import sys
sys.path.append('.')
from src.optimization.dspy.training_data_collector import TrainingDataCollector, FeedbackType

async def demo_training_data():
    print('🚀 TRAINING DATA COLLECTION DEMO')
    print('='*50)
    print('This shows how the system collects data during operation...')
    print()
    
    # Initialize collector with lower threshold for demo
    collector = TrainingDataCollector(
        db_path='demo_training.db',
        min_examples_for_optimization=5  # Lower threshold for demo
    )
    
    # Show initial stats
    stats = await collector.get_dataset_stats()
    print(f'📊 INITIAL STATE:')
    print(f'   • Examples: {stats["total_examples"]}')
    print(f'   • Avg Quality: {stats["avg_quality"]:.2f}')
    print(f'   • Ready for optimization: {stats["ready_for_optimization"]}')
    print()
    
    # Simulate collecting training examples from real usage
    print('🤖 SIMULATING PRODUCTION QUERIES...')
    queries = [
        'What is renewable energy?',
        'How does machine learning work?',
        'Compare solar vs wind power',
        'Explain quantum computing',
        'What are the benefits of electric vehicles?'
    ]
    
    for i, query in enumerate(queries):
        print(f'   Query {i+1}: {query}')
        
        # This simulates what happens after each real user query
        example_id = await collector.collect_training_example(
            session_id=f'demo_session_{i}',
            original_query=query,
            optimized_query=f'Provide comprehensive analysis of: {query}',
            context={'demo': True, 'query_optimized': True},
            workflow_result={
                'success': True,
                'final_answer': f'Comprehensive answer about {query.lower()}',
                'execution_metrics': {'total_time': 2.5 + i*0.3},
                'quality_metrics': {'answer_quality_score': 0.75 + i*0.05},
                'detailed_results': {
                    'research_findings': [{'source': 'web', 'content': f'research about {query}'}],
                    'library_findings': [{'source': 'docs', 'content': f'library info on {query}'}],
                    'analysis_findings': [{'source': 'analysis', 'content': f'analysis of {query}'}]
                }
            }
        )
        
        # Simulate user feedback (would be real user clicks/ratings in production)
        if i < 4:  # Most examples get positive feedback
            await collector.add_feedback(
                example_id=example_id,
                feedback_type=FeedbackType.EXPLICIT_POSITIVE,
                feedback_score=0.8 + i*0.05,
                comments=f'Great answer for question {i+1}!'
            )
            print(f'   ✅ Collected with positive feedback (score: {0.8 + i*0.05:.2f})')
        else:
            await collector.add_feedback(
                example_id=example_id,
                feedback_type=FeedbackType.QUALITY_SCORE,
                feedback_score=0.6,
                comments='Could be more detailed'
            )
            print(f'   📝 Collected with moderate feedback (score: 0.6)')
    
    print()
    
    # Show final stats
    final_stats = await collector.get_dataset_stats()
    print(f'📊 FINAL DATASET STATUS:')
    print(f'   • Total examples: {final_stats["total_examples"]}')
    print(f'   • Average quality: {final_stats["avg_quality"]:.2f}')
    print(f'   • Success rate: {final_stats["success_rate"]:.1%}')
    print(f'   • Examples with feedback: {final_stats["examples_with_feedback"]}')
    print(f'   • Ready for optimization: {final_stats["ready_for_optimization"]}')
    print()
    
    if final_stats['ready_for_optimization']:
        print('🚀 OPTIMIZATION TRIGGERED!')
        print('   The system would now:')
        print('   1. Extract high-quality training examples')
        print('   2. Run MIPROv2 optimization on prompts')
        print('   3. Update agent instructions')
        print('   4. Improve answer quality automatically')
    else:
        remaining = 5 - final_stats['total_examples']
        print(f'📈 PROGRESS UPDATE:')
        print(f'   • Need {remaining} more examples for optimization')
        print(f'   • In production: automatic with real user queries')
    
    print()
    print('🎯 KEY INSIGHT FOR YOUR QUESTION:')
    print('   • The 500-example threshold is DYNAMIC')
    print('   • System collects data from every real query')
    print('   • User feedback improves training quality')
    print('   • Optimization happens automatically when ready')
    print('   • No manual dataset creation required!')
    print()
    print('✅ This is exactly how production ML systems work!')

if __name__ == '__main__':
    asyncio.run(demo_training_data()) 