HTTP Status Codes
200 - Success
201 - Created
400 - Bad Request
401 - Unauthorized
403 - Forbidden
404 - Not Found
422 - Validation Error
429 - Rate Limit Exceeded
500 - Internal Server Error
503 - Service Unavailable
Error Response Format
{
  "error": "validation_error",
  "message": "Invalid question format",
  "details": {
    "field": "question",
    "code": "QUESTION_TOO_SHORT",
    "min_length": 10
  },
  "timestamp": "2025-06-02T15:30:00Z",
  "request_id": "req_abc123def456"
}


File Upload API
Upload File
POST /api/v1/files/upload
Request (multipart/form-data):

Field
Type
Required
Description
file
file
✅
File to upload
file_name
string
❌
Optional custom filename
session_id
string
✅
Session ID


Example Request:

curl -X POST -F "file=@/path/to/file.txt" -F "file_name=custom_name.txt" https://api.example.com/api/v1/files/upload

Response:

{
  "success": true,
  "file_id": "file_abc123def456",
  "file_name": "custom_name.txt",
  "timestamp": "2025-06-02T15:30:00Z"
}

Ha hiba:

{
  "error": "validation_error",
  "message": "File size exceeds limit",
  "details": {
    "field": "file",
    "code": "FILE_SIZE_EXCEEDED",
    "max_size": 10485760
  },
  "timestamp": "2025-06-02T15:30:00Z",
}
Get uploaded files for session
GET /api/v1/files/{session_id}
Path Parameters:

session_id (string, required): Unique session identifier

Response:

{
  "files": [
    {
      "file_id": "file_abc123def456",
      "file_name": "custom_name.txt",
      "upload_time": "2025-06-02T15:30:00Z",
      "processed": false
    },
    {
      "file_id": "file_xyz789abc123",
      "file_name": "another_file.pdf",
      "upload_time": "2025-06-02T15:35:00Z",
      "processed": true
    }
  ]
}


Question Answering API
Simple Question Answering
POST /api/v1/questions/simple
Request Body:

{
  "question": "What are the latest developments in renewable energy?",
  "session_id": "sess_abc123def456"
}

Request Parameters: | Field | Type | Required | Description | |-------|------|----------|-------------| | question | string | ✅ | The question to ask (10-1000 characters) | | language | string | ❌ | Response language (ISO 639-1 code) | | session_id | string | ✅ | Session ID |

Response:

{
  "success": true,
  "workflow_id": "wf_abc123def456",
  "timestamp": "2025-06-02T15:30:00Z"
}

Ha hiba:

{
  "success": false,
  "error": "validation_error",
  "message": "Invalid question format",
  "timestamp": "2025-06-02T15:30:00Z"
}


Advanced Question Answering
POST /api/v1/questions/advanced
Request Body:

{
  "question": "Analyze the economic impact of artificial intelligence on employment in the next decade",
  "session_id": "sess_abc123def456",
  "config": {
    "enable_optimization": true,
    "max_iterations": 5,
    "vector_knowledge_enabled": true,
    "parallel_execution": true,
    "quality_threshold": 0.8,
    "specialist_config": {
      "include_predictions": true
    }
  },
  "workflow_type": "enhanced",
}

Request Parameters: | Field | Type | Required | Description | |-------|------|----------|-------------| | question | string | ✅ | Complex question (10-2000 characters) | | session_id | string | ✅ | Session ID | | config | object | ❌ | Advanced configuration options | | workflow_type | string | ❌ | standard or enhanced |

Configuration Options: | Field | Type | Default | Description | |-------|------|---------|-------------| | enable_optimization | boolean | true | Use DSPy optimization | | max_iterations | integer | 5 | Maximum agent iterations | | vector_knowledge_enabled | boolean | true | Use vector knowledge base | | parallel_execution | boolean | true | Parallel specialist execution | | quality_threshold | number | 0.7 | Minimum quality score |

Response:

{
  "success": true,
  "workflow_id": "wf_abc123def456",
  "timestamp": "2025-06-02T15:30:00Z"
}

Ha hiba:

{
  "success": false,
  "error": "validation_error",
  "message": "Invalid question format",
  "timestamp": "2025-06-02T15:30:00Z"
}


Workflow Management
Get Workflow Status
GET /api/v1/workflows/{workflow_id}/status
Path Parameters:

workflow_id (string, required): Unique workflow identifier

Response:

{
  "workflow_id": "wf_abc123def456",
  "status": "executing",
  "current_phase": "analysis",
  "final_answer": "",
  "progress": {
    "completion_percentage": 65.0,
    "active_agents": ["data_processor", "librarian"],
    "completed_steps": ["planning", "research"],
    "current_step": "data_analysis",
    "estimated_remaining_time": 69.3
  },
  "agents": {
    "task_manager": {
      "status": "completed",
      "progress": 100.0,
      "output": "Execution plan created with 5 steps",
      "execution_time": 8.4
    },
    "researcher": {
      "status": "completed",
      "progress": 100.0,
      "output": "Found 5 relevant sources on AI employment impact",
      "execution_time": 45.2
    },
    "librarian": {
      "status": "working",
      "progress": 80.0,
      "current_task": "Analyzing economic research papers",
      "estimated_completion": "2025-06-02T15:32:00Z"
    },
    "data_processor": {
      "status": "working",
      "progress": 45.0,
      "current_task": "Economic modeling and trend analysis",
      "estimated_completion": "2025-06-02T15:33:00Z"
    },
    "writer": {
      "status": "pending",
      "progress": 0.0,
      "current_task": "Waiting for specialist results"
    }
  },
  "metrics": {
    "elapsed_time": 89.2,
    "estimated_total_time": 158.5,
    "cpu_usage": 45.2,
    "memory_usage": 67.8
  },
  "real_time_updates": {
    "websocket_url": "wss://api/ws/workflows/wf_abc123def456",
  }
}
Cancel Workflow
DELETE /api/v1/workflows/{workflow_id}
Path Parameters:

workflow_id (string, required): Unique workflow identifier

Response:

{
  "success": true,
  "workflow_id": "wf_abc123def456",
  "status": "cancelled",
  "message": "Workflow cancelled successfully",
  "cancellation_time": "2025-06-02T15:31:00Z",
}


System Health
Health Check
GET /api/v1/health
Basic system health check endpoint.

Response:

{
  "status": "healthy",
  "timestamp": "2025-06-02T15:30:00Z",
  "version": "2.0.0",
  "environment": "production",
  "components": {
    "multi_agent_system": {
      "status": "healthy",
      "response_time": 45.2,
      "last_check": "2025-06-02T15:29:55Z"
    },
    "vector_database": {
      "status": "healthy",
      "connections": 15,
      "response_time": 12.3,
      "size": "2.3GB"
    },
    "metrics_collector": {
      "status": "healthy",
      "buffer_size": 245,
      "last_flush": "2025-06-02T15:29:30Z"
    },
    "dspy_optimizer": {
      "status": "healthy",
      "training_examples": 567,
      "last_optimization": "2025-05-24T10:30:00Z"
    }
  },
  "system_info": {
    "uptime": 86400,
    "memory_usage": "2.1GB",
    "cpu_usage": 45.2,
    "active_workflows": 3,
    "total_queries_processed": 12547
  }
}


WebSocket Connections
Real-time Workflow Updates
WS /api/v1/ws/workflows/{workflow_id}
Connection URL:

wss://.../api/v1/ws/workflows/wf_abc123def456

Authentication:

const socket = new WebSocket('wss://api.dspy-system.com/api/v1/ws/workflows/wf_abc123def456', {
  headers: {
    'Authorization': 'Bearer your-api-key-here'
  }
});

Incoming Message Types:

Status Update:

{
  "type": "status_update",
  "workflow_id": "wf_abc123def456",
  "timestamp": "2025-06-02T15:31:00Z",
  "data": {
    "current_phase": "research",
    "progress": 45.0,
    "active_agents": ["researcher", "librarian"],
    "latest_output": "Found 5 relevant sources on renewable energy trends",
    "estimated_completion": "2025-06-02T15:33:00Z"
  }
}

Agent Update:

{
  "type": "agent_update",
  "workflow_id": "wf_abc123def456",
  "timestamp": "2025-06-02T15:31:15Z",
  "data": {
    "agent_id": "researcher",
    "status": "completed",
    "progress": 100.0,
    "output": "Research phase completed with 5 high-quality sources",
    "execution_time": 45.2,
    "next_agent": "data_processor"
  }
}

**Final result streaming**
```json
{
  "type": "final_result",
  "workflow_id": "wf_abc123def456",
  "timestamp": "2025-06-02T15:32:38Z",
  "data": {
    "chunk": "Based on comprehensive analysis..."
  }
}
```



Workflow Complete:

{
  "type": "workflow_complete",
  "workflow_id": "wf_abc123def456",
  "timestamp": "2025-06-02T15:32:38Z",
  "data": {
    "status": "completed",
    "final_answer": "Based on comprehensive analysis...",
    "execution_time": 158.47,
    "quality_score": 0.91
  }
}

Error Notification:

{
  "type": "error",
  "workflow_id": "wf_abc123def456",
  "timestamp": "2025-06-02T15:31:30Z",
  "data": {
    "error_type": "agent_timeout",
    "agent_id": "researcher",
    "message": "Research agent timed out, retrying with different approach",
    "recoverable": true,
    "retry_attempt": 1
  }
}
