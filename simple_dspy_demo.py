#!/usr/bin/env python3
"""Simple DSPy workflow demonstration"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    print("🔥 Simple DSPy Workflow Demo")
    print("="*40)
    
    # Check API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ No OPENAI_API_KEY found")
        return
    
    try:
        import dspy
        from optimization.dspy.qa_modules import MultiAgentQAModule
        
        print("📡 Configuring DSPy...")
        lm = dspy.LM(model='gpt-4.1-mini')
        dspy.configure(lm=lm)
        print("✅ DSPy ready")
        
        print("\n🤖 Creating QA system...")
        qa = MultiAgentQAModule(use_research=False, use_library=False, use_analysis=False)
        print("✅ QA system ready")
        
        print("\n🤔 Asking: 'What is artificial intelligence?'")
        result = qa(question="What is artificial intelligence?")
        
        print(f"\n🎯 DSPy Answer:")
        print(f"{result.final_answer[:300]}...")
        
        print(f"\n📊 Confidence:")
        print(f"{result.confidence[:150]}...")
        
        print("\n✅ Demo complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main() 