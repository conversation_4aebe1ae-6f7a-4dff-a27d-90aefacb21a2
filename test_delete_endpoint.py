#!/usr/bin/env python3
"""
Test script specifically for the DELETE workflow endpoint.

This validates that the workflow cancellation functionality works correctly.
"""

import asyncio
import aiohttp
import json
import time
from pathlib import Path


class DeleteEndpointTester:
    """Test client specifically for DELETE endpoint validation."""
    
    def __init__(self, base_url="http://localhost:8000", api_key="dev-api-key-12345"):
        self.base_url = base_url
        self.api_key = api_key
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            headers={"Authorization": f"Bearer {self.api_key}"}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def test_health_check(self):
        """Test if API is running."""
        print("🏥 Testing API health...")
        
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ API is healthy: {data.get('status', 'unknown')}")
                    return True
                else:
                    print(f"   ❌ API health check failed: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"   ❌ Cannot connect to API: {e}")
            return False
    
    async def create_test_workflow(self):
        """Create a test workflow to cancel."""
        print("🚀 Creating test workflow...")
        
        try:
            payload = {
                "question": "This is a test question for cancellation testing",
                "session_id": "delete_test_session",
                "language": "en"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/questions/simple",
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    workflow_id = data.get("workflow_id")
                    print(f"   ✅ Test workflow created: {workflow_id}")
                    return workflow_id
                else:
                    data = await response.json()
                    print(f"   ❌ Failed to create workflow: {response.status} - {data}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Error creating workflow: {e}")
            return None
    
    async def test_delete_endpoint(self, workflow_id):
        """Test the DELETE /workflows/{workflow_id} endpoint."""
        print(f"🗑️  Testing DELETE endpoint for workflow {workflow_id}...")
        
        try:
            # Test DELETE request
            async with self.session.delete(
                f"{self.base_url}/api/v1/workflows/{workflow_id}"
            ) as response:
                data = await response.json()
                
                if response.status == 200:
                    print(f"   ✅ DELETE endpoint works: {data.get('message', 'Success')}")
                    print(f"   📊 Response data: {json.dumps(data, indent=2)}")
                    return True
                elif response.status == 404:
                    print(f"   ⚠️  Workflow not found (expected if workflow completed quickly)")
                    return True
                elif response.status == 409:
                    print(f"   ⚠️  Cannot cancel workflow (already completed/failed): {data.get('detail', 'Unknown')}")
                    return True
                else:
                    print(f"   ❌ DELETE failed: {response.status} - {data}")
                    return False
                    
        except Exception as e:
            print(f"   ❌ DELETE request error: {e}")
            return False
    
    async def test_delete_nonexistent_workflow(self):
        """Test DELETE on non-existent workflow."""
        print("🔍 Testing DELETE on non-existent workflow...")
        
        fake_workflow_id = "nonexistent-workflow-12345"
        
        try:
            async with self.session.delete(
                f"{self.base_url}/api/v1/workflows/{fake_workflow_id}"
            ) as response:
                data = await response.json()
                
                if response.status == 404:
                    print(f"   ✅ Correctly returns 404 for non-existent workflow")
                    return True
                else:
                    print(f"   ❌ Unexpected response: {response.status} - {data}")
                    return False
                    
        except Exception as e:
            print(f"   ❌ Error testing non-existent workflow: {e}")
            return False
    
    async def test_delete_with_reason(self, workflow_id):
        """Test DELETE with cancellation reason."""
        print(f"📝 Testing DELETE with reason for workflow {workflow_id}...")
        
        try:
            payload = {
                "reason": "Testing cancellation with reason"
            }
            
            async with self.session.delete(
                f"{self.base_url}/api/v1/workflows/{workflow_id}",
                json=payload
            ) as response:
                data = await response.json()
                
                if response.status in [200, 404, 409]:
                    print(f"   ✅ DELETE with reason works: {response.status}")
                    return True
                else:
                    print(f"   ❌ DELETE with reason failed: {response.status} - {data}")
                    return False
                    
        except Exception as e:
            print(f"   ❌ DELETE with reason error: {e}")
            return False
    
    async def check_workflow_status_after_delete(self, workflow_id):
        """Check workflow status after deletion attempt."""
        print(f"📊 Checking workflow status after DELETE...")
        
        try:
            async with self.session.get(
                f"{self.base_url}/api/v1/workflows/{workflow_id}/status"
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    status = data.get("status")
                    print(f"   📈 Workflow status: {status}")
                    
                    if status == "cancelled":
                        print(f"   ✅ Workflow successfully cancelled")
                        return True
                    elif status in ["completed", "failed"]:
                        print(f"   ⚠️  Workflow finished before cancellation")
                        return True
                    else:
                        print(f"   ⚠️  Workflow still in status: {status}")
                        return True
                elif response.status == 404:
                    print(f"   ⚠️  Workflow not found (may have been cleaned up)")
                    return True
                else:
                    data = await response.json()
                    print(f"   ❌ Status check failed: {response.status} - {data}")
                    return False
                    
        except Exception as e:
            print(f"   ❌ Status check error: {e}")
            return False


async def main():
    """Main test function for DELETE endpoint."""
    print("🧪 DELETE Endpoint Validation Test")
    print("=" * 50)
    
    async with DeleteEndpointTester() as tester:
        # Check if API is running
        if not await tester.test_health_check():
            print("❌ API is not running. Start it with: python run_api.py")
            return
        
        print()
        
        # Test 1: DELETE non-existent workflow
        success1 = await tester.test_delete_nonexistent_workflow()
        print()
        
        # Test 2: Create workflow and try to delete it
        workflow_id = await tester.create_test_workflow()
        if workflow_id:
            print()
            success2 = await tester.test_delete_endpoint(workflow_id)
            print()
            success3 = await tester.check_workflow_status_after_delete(workflow_id)
            print()
        else:
            success2 = False
            success3 = False
        
        # Test 3: Create another workflow and delete with reason
        workflow_id2 = await tester.create_test_workflow()
        if workflow_id2:
            print()
            success4 = await tester.test_delete_with_reason(workflow_id2)
            print()
        else:
            success4 = False
        
        # Summary
        print("📋 Test Results Summary:")
        print(f"   DELETE non-existent workflow: {'✅' if success1 else '❌'}")
        print(f"   DELETE existing workflow: {'✅' if success2 else '❌'}")
        print(f"   Status check after DELETE: {'✅' if success3 else '❌'}")
        print(f"   DELETE with reason: {'✅' if success4 else '❌'}")
        
        all_passed = success1 and success2 and success3 and success4
        print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")


if __name__ == "__main__":
    print("Starting DELETE endpoint validation...")
    print("Make sure the API server is running: python run_api.py")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
