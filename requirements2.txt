aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aioredis==2.0.1
aiosignal==1.3.2
aiosqlite==0.21.0
alembic==1.15.2
annotated-types==0.7.0
anyio==4.9.0
appdirs==1.4.4
asgiref==3.8.1
asttokens==3.0.0
async-timeout==5.0.1
asyncer==0.0.8
attrs==25.3.0
auth0-python==4.9.0
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.4
blinker==1.9.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.1.8
cloudpickle==3.1.1
cohere==5.15.0
coloredlogs==15.0.1
colorlog==6.9.0
contourpy==1.3.2
crewai==0.120.1
crewai-tools==0.45.0
cryptography==45.0.2
cycler==0.12.1
dataclasses-json==0.6.7
datasets==3.6.0
decorator==5.2.1
Deprecated==1.2.18
deprecation==2.1.0
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
docker==7.1.0
docstring_parser==0.16
dspy>=2.6.0
duckduckgo_search==8.0.2
durationpy==0.10
embedchain==0.1.128
et_xmlfile==2.0.0
executing==2.2.0
faiss-cpu==1.11.0
fastapi==0.115.9
fastavro==1.11.1
filelock==3.18.0
flatbuffers==25.2.10
fonttools==4.58.0
frozenlist==1.6.0
fsspec==2025.3.0
google-auth==2.40.1
googleapis-common-protos==1.70.0
gptcache==0.1.44
greenlet==3.2.2
grpcio==1.71.0
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.31.4
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
iniconfig==2.1.0
instructor==1.8.2
ipython==9.2.0
ipython_pygments_lexers==1.1.1
jedi==0.19.2
Jinja2==3.1.6
jiter==0.8.2
joblib==1.5.0
json5==0.12.0
json_repair==0.45.1
jsonpatch==1.33
jsonpickle==4.0.5
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
kubernetes==32.0.1
lancedb==0.22.0
langchain==0.3.25
langchain-cohere==0.3.5
langchain-community==0.3.24
langchain-core==0.3.60
langchain-experimental==0.3.4
langchain-openai==0.2.14
langchain-text-splitters==0.3.8
langsmith==0.3.42
litellm==1.68.0
lxml==5.4.0
magicattr==0.1.6
Mako==1.3.10
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.101
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
multidict==6.4.4
multiprocess==0.70.16
mypy_extensions==1.1.0
networkx==3.4.2
nodeenv==1.9.1
numpy==2.2.6
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
oauthlib==3.2.2
onnxruntime==1.22.0
openai==1.75.0
openpyxl==3.1.5
opentelemetry-api==1.33.1
opentelemetry-exporter-otlp-proto-common==1.33.1
opentelemetry-exporter-otlp-proto-grpc==1.33.1
opentelemetry-exporter-otlp-proto-http==1.33.1
opentelemetry-instrumentation==0.54b1
opentelemetry-instrumentation-asgi==0.54b1
opentelemetry-instrumentation-fastapi==0.54b1
opentelemetry-proto==1.33.1
opentelemetry-sdk==1.33.1
opentelemetry-semantic-conventions==0.54b1
opentelemetry-util-http==0.54b1
optuna==4.3.0
orjson==3.10.18
overrides==7.7.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pdfminer.six==20250327
pdfplumber==0.11.6
pexpect==4.9.0
pillow==11.2.1
pluggy==1.6.0
portalocker==2.10.1
posthog==3.25.0
primp==0.15.0
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==5.29.4
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.4
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
PyJWT==2.10.1
pyparsing==3.2.3
pypdf==5.5.0
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyright==1.1.401
pysbd==0.3.4
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-mock==3.14.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pytube==15.0.0
pytz==2025.2
pyvis==0.3.2
PyYAML==6.0.2
qdrant-client==1.14.2
redis==6.1.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.25.0
rsa==4.9.1
safetensors==0.5.3
schema==0.7.7
scikit-learn==1.6.1
scipy==1.15.3
seaborn==0.13.2
sentence-transformers==4.1.0
setuptools==80.8.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
stack-data==0.6.3
starlette==0.45.3
sympy==1.14.0
tabulate==0.9.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
tomli==2.2.1
tomli_w==1.2.0
torch==2.7.0
tqdm==4.67.1
traitlets==5.14.3
transformers==4.52.3
triton==3.3.0
typer==0.15.4
types-requests==2.32.0.20250515
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
ujson==5.10.0
urllib3==2.4.0
uv==0.7.6
uvicorn==0.34.2
uvloop==0.21.0
watchfiles==1.0.5
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
wikipedia==1.4.0
wrapt==1.17.2
xxhash==3.5.0
yarl==1.20.0
zipp==3.21.0
zstandard==0.23.0
