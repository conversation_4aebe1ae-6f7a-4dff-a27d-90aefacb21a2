version: '3.8'

# Qdrant Vector Database Override
# Usage: docker-compose -f docker-compose.yml -f docker-compose.qdrant.yml up -d

services:
  dspy-api:
    environment:
      # Override vector database configuration for Qdrant
      - VECTOR_DB_TYPE=qdrant
      - QDRANT_HOST=vector-db
      - QDRANT_PORT=6333

  vector-db:
    # Override to use Qdrant instead of ChromaDB
    image: qdrant/qdrant:latest
    ports:
      - "${QDRANT_PORT:-6333}:6333"
      - "${QDRANT_GRPC_PORT:-6334}:6334"
    environment:
      # Qdrant-specific configuration
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=${QDRANT_LOG_LEVEL:-INFO}
      - QDRANT__STORAGE__STORAGE_PATH=/qdrant/storage
    volumes:
      - vector_db_data:/qdrant/storage
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
