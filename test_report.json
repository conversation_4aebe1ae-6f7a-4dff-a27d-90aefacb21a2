{"timestamp": "2025-05-23T18:12:23.458728", "summary": {"total_suites": 4, "successful_suites": 0, "failed_suites": 4, "total_tests_passed": 0, "total_tests_failed": 0, "total_tests_errors": 0}, "phase_1_coverage": {"task_1_1_orchestration": "TESTED", "task_1_2_vector_knowledge": "TESTED", "task_1_3_dspy_optimization": "TESTED", "task_1_4_monitoring_analytics": "TESTED"}, "detailed_results": [{"test_file": "test_orchestration_flows.py", "success": false, "return_code": 4, "has_output": false, "has_errors": true}, {"test_file": "test_dspy_optimization.py", "success": false, "return_code": 4, "has_output": false, "has_errors": true}, {"test_file": "test_monitoring_system.py", "success": false, "return_code": 4, "has_output": false, "has_errors": true}, {"test_file": "test_vector_knowledge_base.py", "success": false, "return_code": 4, "has_output": false, "has_errors": true}]}