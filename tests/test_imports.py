"""
Basic import tests to verify Phase 1 implementations can be imported successfully.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_orchestration_imports():
    """Test orchestration flow imports."""
    try:
        from orchestration.flows.state_management import (
            TaskResult, AgentMetrics, WorkflowState, StateManager
        )
        from orchestration.flows.flow_monitoring import (
            FlowMonitor, MonitoringConfig, PerformanceMetrics
        )
        from orchestration.flows.advanced_coordination_flow import (
            AdvancedCoordinationFlow
        )
        print("✅ Orchestration imports successful")
        return True
    except Exception as e:
        print(f"❌ Orchestration imports failed: {e}")
        return False

def test_dspy_optimization_imports():
    """Test DSPy optimization imports."""
    try:
        from optimization.dspy.base_optimizer import (
            OptimizationConfig, OptimizationResult, BaseOptimizer,
            BootstrapOptimizer, RandomSearchOptimizer, get_optimizer
        )
        from optimization.dspy.mipro_v2_optimizer import (
            MIPROv2Config, MIPROv2Optimizer, InstructionGenerator, AdaptiveSampler
        )
        print("✅ DSPy optimization imports successful")
        return True
    except Exception as e:
        print(f"❌ DSPy optimization imports failed: {e}")
        return False

def test_monitoring_imports():
    """Test monitoring system imports."""
    try:
        from infrastructure.monitoring.metrics_collector import (
            MetricType, AlertLevel, MetricValue, Alert, MetricsStorage,
            MetricsCollector, TimerContext, get_metrics_collector
        )
        from infrastructure.monitoring.analytics_dashboard import (
            AnalyticsDashboard, DashboardConfig
        )
        print("✅ Monitoring system imports successful")
        return True
    except Exception as e:
        print(f"❌ Monitoring system imports failed: {e}")
        return False

def test_vector_knowledge_imports():
    """Test vector knowledge base imports."""
    try:
        from infrastructure.storage.enterprise_vector_db import (
            VectorDBType, SearchResult, EnterpriseVectorDatabase
        )
        from infrastructure.storage.enterprise_embedding_service import (
            EmbeddingProvider, EnterpriseEmbeddingService
        )
        from infrastructure.storage.knowledge_graph_manager import (
            KnowledgeGraphManager
        )
        from infrastructure.storage.multimodal_processor import (
            ContentType, ProcessedContent, MultimodalProcessor
        )
        print("✅ Vector knowledge base imports successful")
        return True
    except Exception as e:
        print(f"❌ Vector knowledge base imports failed: {e}")
        return False

def test_all_phase1_imports():
    """Test all Phase 1 component imports."""
    print("🔍 Testing Phase 1 Implementation Imports...")
    
    results = []
    results.append(test_orchestration_imports())
    results.append(test_dspy_optimization_imports())
    results.append(test_monitoring_imports())
    results.append(test_vector_knowledge_imports())
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 Import Results: {success_count}/{total_count} successful")
    
    if success_count == total_count:
        print("🎉 All Phase 1 implementations can be imported successfully!")
        return True
    else:
        print("⚠️ Some imports failed. Check the error messages above.")
        return False

if __name__ == "__main__":
    success = test_all_phase1_imports()
    sys.exit(0 if success else 1) 