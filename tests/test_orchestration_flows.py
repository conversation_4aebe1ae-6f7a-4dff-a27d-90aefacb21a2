"""
Tests for Advanced Multi-Agent Orchestration Engine (Task 1.1).

Tests CrewAI Flow integration, advanced coordination, state management, and monitoring.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Mock dependencies
sys.modules['crewai'] = MagicMock()

from orchestration.flows.advanced_coordination_flow import AdvancedCoordinationFlow
from orchestration.flows.state_management import WorkflowState, TaskResult, AgentMetrics, StateManager
from orchestration.flows.flow_monitoring import FlowMonitor, MonitoringConfig, PerformanceMetrics


class TestStateManagement:
    """Test state management components."""
    
    def test_task_result_creation(self):
        """Test TaskResult model creation and validation."""
        result = TaskResult(
            task_id="test-123",
            agent_id="agent-search",
            result_data={"answer": "test answer"},
            success=True,
            execution_time=1.5
        )
        
        assert result.task_id == "test-123"
        assert result.agent_id == "agent-search"
        assert result.success is True
        assert result.execution_time == 1.5
        assert isinstance(result.timestamp, datetime)
        assert result.error_message is None
    
    def test_task_result_with_error(self):
        """Test TaskResult with error information."""
        result = TaskResult(
            task_id="test-456",
            agent_id="agent-knowledge",
            result_data={},
            success=False,
            execution_time=0.5,
            error_message="Connection timeout"
        )
        
        assert result.success is False
        assert result.error_message == "Connection timeout"
    
    def test_agent_metrics_creation(self):
        """Test AgentMetrics model creation."""
        metrics = AgentMetrics(
            agent_id="agent-math",
            total_tasks=10,
            successful_tasks=8,
            avg_execution_time=2.3,
            last_activity=datetime.now()
        )
        
        assert metrics.agent_id == "agent-math"
        assert metrics.success_rate == 0.8
        assert metrics.total_tasks == 10
    
    def test_workflow_state_creation(self):
        """Test WorkflowState model creation."""
        state = WorkflowState(
            session_id="session-789",
            current_stage="processing",
            complexity_score=7.5
        )
        
        assert state.session_id == "session-789"
        assert state.current_stage == "processing"
        assert state.complexity_score == 7.5
        assert len(state.task_results) == 0
        assert len(state.agent_metrics) == 0
    
    def test_state_manager_initialization(self):
        """Test StateManager initialization."""
        manager = StateManager()
        
        assert manager.active_sessions == {}
        assert manager.session_history == []
        assert manager.performance_cache == {}
    
    def test_state_manager_create_session(self):
        """Test creating a new workflow session."""
        manager = StateManager()
        session_id = manager.create_session("complex")
        
        assert session_id in manager.active_sessions
        state = manager.active_sessions[session_id]
        assert state.current_stage == "initialized"
        assert state.complexity_score > 0
    
    def test_state_manager_add_task_result(self):
        """Test adding task results to workflow state."""
        manager = StateManager()
        session_id = manager.create_session("standard")
        
        result = TaskResult(
            task_id="task-1",
            agent_id="search-agent",
            result_data={"data": "test"},
            success=True,
            execution_time=1.0
        )
        
        manager.add_task_result(session_id, result)
        state = manager.get_session_state(session_id)
        
        assert len(state.task_results) == 1
        assert state.task_results[0].task_id == "task-1"
    
    def test_state_manager_update_stage(self):
        """Test updating workflow stage."""
        manager = StateManager()
        session_id = manager.create_session("parallel")
        
        manager.update_stage(session_id, "routing")
        state = manager.get_session_state(session_id)
        
        assert state.current_stage == "routing"
    
    def test_state_manager_session_cleanup(self):
        """Test session cleanup functionality."""
        manager = StateManager()
        session_id = manager.create_session("test")
        
        # Add some data
        result = TaskResult(
            task_id="cleanup-test",
            agent_id="test-agent", 
            result_data={},
            success=True,
            execution_time=0.1
        )
        manager.add_task_result(session_id, result)
        
        # Complete session
        manager.complete_session(session_id)
        
        assert session_id not in manager.active_sessions
        assert len(manager.session_history) == 1


class TestFlowMonitoring:
    """Test flow monitoring components."""
    
    def test_monitoring_config_creation(self):
        """Test MonitoringConfig creation with defaults."""
        config = MonitoringConfig()
        
        assert config.enable_real_time_monitoring is True
        assert config.performance_threshold_ms == 5000
        assert config.error_rate_threshold == 0.1
        assert config.alert_cooldown_minutes == 5
    
    def test_performance_metrics_creation(self):
        """Test PerformanceMetrics model."""
        metrics = PerformanceMetrics(
            avg_response_time=1500.0,
            success_rate=0.95,
            error_count=2,
            active_agents=5
        )
        
        assert metrics.avg_response_time == 1500.0
        assert metrics.success_rate == 0.95
        assert metrics.error_count == 2
    
    def test_flow_monitor_initialization(self):
        """Test FlowMonitor initialization."""
        monitor = FlowMonitor()
        
        assert monitor.config.enable_real_time_monitoring is True
        assert monitor.performance_history == []
        assert monitor.active_alerts == []
        assert monitor.last_alert_time == {}
    
    def test_flow_monitor_record_execution(self):
        """Test recording execution metrics."""
        monitor = FlowMonitor()
        
        monitor.record_execution("test-task", 1200.0, True, "search-agent")
        
        assert len(monitor.execution_history) == 1
        record = monitor.execution_history[0]
        assert record["task_id"] == "test-task"
        assert record["duration"] == 1200.0
        assert record["success"] is True
    
    def test_flow_monitor_get_performance_metrics(self):
        """Test getting current performance metrics."""
        monitor = FlowMonitor()
        
        # Add some execution records
        monitor.record_execution("task1", 1000.0, True, "agent1")
        monitor.record_execution("task2", 2000.0, True, "agent2") 
        monitor.record_execution("task3", 1500.0, False, "agent1")
        
        metrics = monitor.get_current_performance()
        
        assert metrics.avg_response_time == 1500.0
        assert metrics.success_rate == 2/3
        assert metrics.error_count == 1
    
    def test_flow_monitor_alert_generation(self):
        """Test alert generation for performance issues."""
        config = MonitoringConfig(performance_threshold_ms=1000)
        monitor = FlowMonitor(config)
        
        # Record slow execution that should trigger alert
        monitor.record_execution("slow-task", 2000.0, True, "slow-agent")
        
        alerts = monitor.check_alerts()
        
        assert len(alerts) > 0
        alert = alerts[0]
        assert "performance" in alert["message"].lower()
        assert alert["level"] == "warning"
    
    def test_flow_monitor_alert_cooldown(self):
        """Test alert cooldown functionality."""
        config = MonitoringConfig(
            performance_threshold_ms=1000,
            alert_cooldown_minutes=1
        )
        monitor = FlowMonitor(config)
        
        # First slow execution - should generate alert
        monitor.record_execution("task1", 2000.0, True, "agent1")
        alerts1 = monitor.check_alerts()
        assert len(alerts1) > 0
        
        # Second slow execution immediately - should not generate new alert (cooldown)
        monitor.record_execution("task2", 2000.0, True, "agent2")
        alerts2 = monitor.check_alerts()
        
        # Should still have alerts, but no new ones due to cooldown
        assert len(monitor.active_alerts) >= len(alerts1)


class TestAdvancedCoordinationFlow:
    """Test advanced coordination flow components."""
    
    @pytest.fixture
    def mock_agents(self):
        """Mock agent instances for testing."""
        agents = {}
        for agent_type in ["search", "knowledge", "react_search", "react_knowledge"]:
            mock_agent = Mock()
            mock_agent.agent_id = f"{agent_type}_agent"
            mock_agent.execute = AsyncMock(return_value={
                "answer": f"test answer from {agent_type}",
                "confidence": 0.8
            })
            agents[agent_type] = mock_agent
        return agents
    
    @pytest.fixture 
    def coordination_flow(self, mock_agents):
        """Create AdvancedCoordinationFlow instance with mock agents."""
        return AdvancedCoordinationFlow(
            search_specialist=mock_agents["search"],
            knowledge_specialist=mock_agents["knowledge"],
            react_search_specialist=mock_agents["react_search"],
            react_knowledge_specialist=mock_agents["react_knowledge"]
        )
    
    def test_coordination_flow_initialization(self, coordination_flow):
        """Test AdvancedCoordinationFlow initialization."""
        assert coordination_flow.search_specialist is not None
        assert coordination_flow.knowledge_specialist is not None
        assert coordination_flow.react_search_specialist is not None
        assert coordination_flow.react_knowledge_specialist is not None
        assert coordination_flow.state_manager is not None
        assert coordination_flow.flow_monitor is not None
    
    def test_analyze_complexity_simple(self, coordination_flow):
        """Test complexity analysis for simple queries."""
        simple_query = "What is the capital of France?"
        complexity = coordination_flow.analyze_complexity(simple_query)
        
        assert isinstance(complexity, (int, float))
        assert 0 <= complexity <= 10
        # Simple queries should have lower complexity
        assert complexity < 5
    
    def test_analyze_complexity_complex(self, coordination_flow):
        """Test complexity analysis for complex queries."""
        complex_query = """
        Compare and contrast the economic impacts of renewable energy adoption 
        versus traditional fossil fuel dependency across different geographical 
        regions, considering factors like job creation, environmental costs, 
        technological infrastructure requirements, and long-term sustainability 
        implications for both developed and developing nations.
        """
        complexity = coordination_flow.analyze_complexity(complex_query)
        
        assert isinstance(complexity, (int, float))
        assert 0 <= complexity <= 10
        # Complex queries should have higher complexity
        assert complexity > 6
    
    def test_route_workflow_simple(self, coordination_flow):
        """Test workflow routing for simple complexity."""
        query = "What is 2 + 2?"
        workflow_type = coordination_flow.route_workflow(query)
        
        assert workflow_type in ["standard", "parallel", "complex"]
        # Simple queries often route to standard workflow
        assert workflow_type == "standard"
    
    def test_route_workflow_complex(self, coordination_flow):
        """Test workflow routing for high complexity."""
        complex_query = """
        Analyze the multi-faceted implications of quantum computing on cybersecurity,
        including cryptographic vulnerabilities, defensive strategies, timeline 
        considerations, and potential solutions across different industry sectors.
        """
        workflow_type = coordination_flow.route_workflow(complex_query)
        
        assert workflow_type in ["standard", "parallel", "complex"]
        # Complex queries should route to complex workflow
        assert workflow_type == "complex"
    
    @pytest.mark.asyncio
    async def test_execute_standard_workflow(self, coordination_flow):
        """Test standard workflow execution."""
        query = "What is the weather today?"
        
        result = await coordination_flow.execute_standard_workflow(query)
        
        assert "answer" in result
        assert "metadata" in result
        assert result["metadata"]["workflow_type"] == "standard"
        assert "session_id" in result["metadata"]
    
    @pytest.mark.asyncio
    async def test_execute_parallel_workflow(self, coordination_flow):
        """Test parallel workflow execution."""
        query = "Tell me about artificial intelligence and machine learning"
        
        result = await coordination_flow.execute_parallel_workflow(query)
        
        assert "answer" in result
        assert "metadata" in result
        assert result["metadata"]["workflow_type"] == "parallel"
        assert "agent_results" in result["metadata"]
    
    @pytest.mark.asyncio
    async def test_execute_complex_workflow(self, coordination_flow):
        """Test complex multi-stage workflow execution."""
        query = """
        Provide a comprehensive analysis of climate change impacts on global agriculture,
        including regional variations, adaptation strategies, and policy recommendations.
        """
        
        result = await coordination_flow.execute_complex_workflow(query)
        
        assert "answer" in result
        assert "metadata" in result
        assert result["metadata"]["workflow_type"] == "complex"
        assert "stages" in result["metadata"]
        assert len(result["metadata"]["stages"]) > 1
    
    @pytest.mark.asyncio
    async def test_main_flow_execution(self, coordination_flow):
        """Test main flow execution with automatic routing."""
        query = "Explain the theory of relativity"
        
        result = await coordination_flow.main_flow(query)
        
        assert "answer" in result
        assert "metadata" in result
        assert "session_id" in result["metadata"]
        assert "workflow_type" in result["metadata"]
        assert "total_duration" in result["metadata"]
    
    def test_error_handling(self, coordination_flow):
        """Test error handling in coordination flow."""
        # Test with invalid input
        with pytest.raises((ValueError, TypeError)):
            coordination_flow.analyze_complexity(None)
        
        with pytest.raises((ValueError, TypeError)):
            coordination_flow.route_workflow("")


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"]) 