"""
Tests for Production Monitoring and Analytics System (Task 1.4).

Tests metrics collection, storage, analytics dashboard, and monitoring components.
"""

import pytest
import tempfile
import shutil
import sqlite3
import json
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from pathlib import Path
import base64

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Mock optional dependencies with better compatibility
mock_psutil = MagicMock()
mock_psutil.cpu_percent.return_value = 25.0
mock_psutil.virtual_memory.return_value = MagicMock(percent=50.0)
mock_psutil.disk_usage.return_value = MagicMock(percent=75.0)
mock_psutil.pids.return_value = [1, 2, 3]
sys.modules['psutil'] = mock_psutil

# Mock matplotlib with proper structure
mock_figure = MagicMock()
mock_figure.savefig = MagicMock()

mock_plt = MagicMock()
mock_plt.figure.return_value = mock_figure
mock_plt.subplots.return_value = (mock_figure, MagicMock())
mock_plt.style.use = MagicMock()

mock_matplotlib = MagicMock()
mock_matplotlib.pyplot = mock_plt
mock_matplotlib.dates = MagicMock()
mock_matplotlib.figure.Figure = MagicMock(return_value=mock_figure)

sys.modules['matplotlib'] = mock_matplotlib
sys.modules['matplotlib.pyplot'] = mock_plt
sys.modules['matplotlib.dates'] = MagicMock()
sys.modules['matplotlib.figure'] = MagicMock()

# Mock seaborn
mock_seaborn = MagicMock()
mock_seaborn.set_palette = MagicMock()
sys.modules['seaborn'] = mock_seaborn

# Mock pandas and numpy
mock_pandas = MagicMock()
sys.modules['pandas'] = mock_pandas

mock_numpy = MagicMock()
sys.modules['numpy'] = mock_numpy

from infrastructure.monitoring.metrics_collector import (
    MetricType, AlertLevel, MetricValue, Alert, MetricsStorage,
    MetricsCollector, TimerContext, get_metrics_collector,
    initialize_metrics_system, shutdown_metrics_system
)
from infrastructure.monitoring.analytics_dashboard import (
    AnalyticsDashboard, DashboardConfig
)


class TestMetricTypes:
    """Test metric and alert enums."""
    
    def test_metric_types(self):
        """Test MetricType enum values."""
        assert MetricType.COUNTER.value == "counter"
        assert MetricType.GAUGE.value == "gauge" 
        assert MetricType.HISTOGRAM.value == "histogram"
        assert MetricType.TIMER.value == "timer"
        assert MetricType.DISTRIBUTION.value == "distribution"
    
    def test_alert_levels(self):
        """Test AlertLevel enum values."""
        assert AlertLevel.INFO.value == "info"
        assert AlertLevel.WARNING.value == "warning"
        assert AlertLevel.ERROR.value == "error"
        assert AlertLevel.CRITICAL.value == "critical"


class TestMetricValue:
    """Test MetricValue data model."""
    
    def test_metric_value_creation(self):
        """Test MetricValue creation with required fields."""
        timestamp = datetime.now()
        metric = MetricValue(
            name="test.metric",
            value=42.5,
            metric_type=MetricType.GAUGE,
            timestamp=timestamp
        )
        
        assert metric.name == "test.metric"
        assert metric.value == 42.5
        assert metric.metric_type == MetricType.GAUGE
        assert metric.timestamp == timestamp
        assert metric.tags == {}
        assert metric.labels == {}
        assert metric.session_id == ""
    
    def test_metric_value_with_metadata(self):
        """Test MetricValue with tags and labels."""
        metric = MetricValue(
            name="system.cpu",
            value=75.0,
            metric_type=MetricType.GAUGE,
            timestamp=datetime.now(),
            tags={"host": "server1", "env": "prod"},
            labels={"region": "us-west"},
            session_id="session123"
        )
        
        assert metric.tags["host"] == "server1"
        assert metric.labels["region"] == "us-west"
        assert metric.session_id == "session123"
    
    def test_metric_value_to_dict(self):
        """Test MetricValue serialization to dict."""
        timestamp = datetime.now()
        metric = MetricValue(
            name="test.counter",
            value=10,
            metric_type=MetricType.COUNTER,
            timestamp=timestamp,
            tags={"component": "test"}
        )
        
        data = metric.to_dict()
        
        assert data["name"] == "test.counter"
        assert data["value"] == 10
        assert data["metric_type"] == "counter"
        assert data["timestamp"] == timestamp.isoformat()
        assert data["tags"] == {"component": "test"}


class TestAlert:
    """Test Alert data model."""
    
    def test_alert_creation(self):
        """Test Alert creation with required fields."""
        timestamp = datetime.now()
        alert = Alert(
            id="alert-123",
            level=AlertLevel.WARNING,
            message="High CPU usage detected",
            metric_name="system.cpu_percent",
            metric_value=85.0,
            threshold=80.0,
            timestamp=timestamp
        )
        
        assert alert.id == "alert-123"
        assert alert.level == AlertLevel.WARNING
        assert alert.message == "High CPU usage detected"
        assert alert.metric_value == 85.0
        assert alert.threshold == 80.0
        assert alert.resolved is False
        assert alert.resolution_timestamp is None
    
    def test_alert_to_dict(self):
        """Test Alert serialization to dict."""
        timestamp = datetime.now()
        alert = Alert(
            id="alert-456",
            level=AlertLevel.CRITICAL,
            message="System failure",
            metric_name="system.health",
            metric_value=0.0,
            threshold=0.5,
            timestamp=timestamp,
            resolved=True,
            resolution_timestamp=timestamp + timedelta(minutes=5)
        )
        
        data = alert.to_dict()
        
        assert data["id"] == "alert-456"
        assert data["level"] == "critical"
        assert data["resolved"] is True
        assert data["resolution_timestamp"] is not None


class TestMetricsStorage:
    """Test metrics storage functionality."""
    
    @pytest.fixture
    def temp_storage(self):
        """Create temporary metrics storage."""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = str(Path(temp_dir) / "test_metrics.db")
            storage = MetricsStorage(db_path)
            yield storage
    
    def test_storage_initialization(self, temp_storage):
        """Test storage initialization and database creation."""
        assert Path(temp_storage.db_path).exists()
        
        # Check tables exist
        with temp_storage._get_connection() as conn:
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('metrics', 'alerts')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            assert "metrics" in tables
            assert "alerts" in tables
    
    def test_store_metric(self, temp_storage):
        """Test storing a metric."""
        timestamp = datetime.now()
        metric = MetricValue(
            name="test.metric",
            value=100.0,
            metric_type=MetricType.COUNTER,
            timestamp=timestamp,
            tags={"test": "true"},
            session_id="test-session"
        )
        
        temp_storage.store_metric(metric)
        
        # Verify storage
        with temp_storage._get_connection() as conn:
            cursor = conn.execute("SELECT * FROM metrics WHERE name = ?", ("test.metric",))
            row = cursor.fetchone()
            
            assert row is not None
            assert row["name"] == "test.metric"
            assert row["value"] == 100.0
            assert row["metric_type"] == "counter"
    
    def test_store_alert(self, temp_storage):
        """Test storing an alert."""
        timestamp = datetime.now()
        alert = Alert(
            id="test-alert",
            level=AlertLevel.WARNING,
            message="Test alert",
            metric_name="test.metric",
            metric_value=90.0,
            threshold=80.0,
            timestamp=timestamp
        )
        
        temp_storage.store_alert(alert)
        
        # Verify storage
        with temp_storage._get_connection() as conn:
            cursor = conn.execute("SELECT * FROM alerts WHERE id = ?", ("test-alert",))
            row = cursor.fetchone()
            
            assert row is not None
            assert row["id"] == "test-alert"
            assert row["level"] == "warning"
            assert row["message"] == "Test alert"
    
    def test_get_metrics(self, temp_storage):
        """Test retrieving metrics."""
        # Store test metrics
        base_time = datetime.now()
        for i in range(5):
            metric = MetricValue(
                name="test.series",
                value=float(i * 10),
                metric_type=MetricType.GAUGE,
                timestamp=base_time + timedelta(minutes=i)
            )
            temp_storage.store_metric(metric)
        
        # Retrieve all metrics
        metrics = temp_storage.get_metrics(name="test.series")
        assert len(metrics) == 5
        
        # Retrieve with time filter
        start_time = base_time + timedelta(minutes=2)
        metrics_filtered = temp_storage.get_metrics(
            name="test.series",
            start_time=start_time
        )
        assert len(metrics_filtered) == 3
    
    def test_get_alerts(self, temp_storage):
        """Test retrieving alerts."""
        # Store test alerts
        for i, level in enumerate([AlertLevel.INFO, AlertLevel.WARNING, AlertLevel.ERROR]):
            alert = Alert(
                id=f"alert-{i}",
                level=level,
                message=f"Test alert {i}",
                metric_name="test.metric",
                metric_value=float(i * 10),
                threshold=50.0,
                timestamp=datetime.now()
            )
            temp_storage.store_alert(alert)
        
        # Retrieve all alerts
        all_alerts = temp_storage.get_alerts()
        assert len(all_alerts) == 3
        
        # Retrieve only warnings
        warning_alerts = temp_storage.get_alerts(level=AlertLevel.WARNING)
        assert len(warning_alerts) == 1
        assert warning_alerts[0]["level"] == "warning"


class TestMetricsCollector:
    """Test MetricsCollector functionality."""
    
    @pytest.fixture
    def temp_collector(self):
        """Create temporary metrics collector."""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = str(Path(temp_dir) / "test_metrics.db")
            collector = MetricsCollector(
                storage_path=db_path,
                buffer_size=10,
                flush_interval=1
            )
            yield collector
            collector.stop()
    
    def test_collector_initialization(self, temp_collector):
        """Test MetricsCollector initialization."""
        assert temp_collector.buffer_size == 10
        assert temp_collector.flush_interval == 1
        assert len(temp_collector.metrics_buffer) == 0
        assert len(temp_collector.alerts_buffer) == 0
        assert temp_collector.counters == {}
        assert temp_collector.gauges == {}
    
    def test_record_counter(self, temp_collector):
        """Test counter metric recording."""
        temp_collector.record_counter("test.counter", 5.0, {"tag": "test"})
        temp_collector.record_counter("test.counter", 3.0)
        
        assert temp_collector.counters["test.counter"] == 8.0
        assert len(temp_collector.metrics_buffer) == 2
    
    def test_record_gauge(self, temp_collector):
        """Test gauge metric recording."""
        temp_collector.record_gauge("test.gauge", 42.5, {"unit": "percent"})
        temp_collector.record_gauge("test.gauge", 38.2)
        
        assert temp_collector.gauges["test.gauge"] == 38.2
        assert len(temp_collector.metrics_buffer) == 2
    
    def test_record_histogram(self, temp_collector):
        """Test histogram metric recording."""
        values = [1.0, 2.0, 3.0, 4.0, 5.0]
        for value in values:
            temp_collector.record_histogram("test.histogram", value)
        
        assert len(temp_collector.histograms["test.histogram"]) == 5
        assert temp_collector.histograms["test.histogram"] == values
    
    def test_record_timer(self, temp_collector):
        """Test timer metric recording."""
        temp_collector.record_timer("test.timer", 0.5, {"operation": "query"})
        temp_collector.record_timer("test.timer", 0.3)
        
        assert len(temp_collector.timers["test.timer"]) == 2
        assert 0.5 in temp_collector.timers["test.timer"]
        assert 0.3 in temp_collector.timers["test.timer"]
    
    def test_timer_context(self, temp_collector):
        """Test timer context manager."""
        with temp_collector.time_operation("test.operation", {"context": "test"}):
            time.sleep(0.01)  # Small delay
        
        assert len(temp_collector.timers["test.operation"]) == 1
        assert temp_collector.timers["test.operation"][0] > 0
    
    def test_set_threshold_and_alerts(self, temp_collector):
        """Test threshold setting and alert generation."""
        # Set threshold
        temp_collector.set_threshold("test.metric", AlertLevel.WARNING, 80.0)
        
        assert "test.metric" in temp_collector.thresholds
        assert temp_collector.thresholds["test.metric"]["warning"] == 80.0
        
        # Record metric that exceeds threshold
        temp_collector.record_gauge("test.metric", 85.0)
        
        # Should generate alert
        assert len(temp_collector.alerts_buffer) > 0
        alert = temp_collector.alerts_buffer[0]
        assert alert.level == AlertLevel.WARNING
        assert alert.metric_value == 85.0
    
    def test_alert_callbacks(self, temp_collector):
        """Test alert callback functionality."""
        callback_called = []
        
        def test_callback(alert):
            callback_called.append(alert)
        
        temp_collector.add_alert_callback(test_callback)
        temp_collector.set_threshold("callback.test", AlertLevel.ERROR, 50.0)
        
        # Trigger alert
        temp_collector.record_gauge("callback.test", 60.0)
        
        assert len(callback_called) == 1
        assert callback_called[0].level == AlertLevel.ERROR
    
    @patch('infrastructure.monitoring.metrics_collector.psutil')
    def test_system_metrics_collection(self, mock_psutil, temp_collector):
        """Test system metrics collection."""
        # Mock psutil responses
        mock_psutil.cpu_percent.return_value = 45.0
        mock_psutil.virtual_memory.return_value = Mock(percent=60.0, available=1024*1024*1024)
        mock_psutil.disk_usage.return_value = Mock(used=500*1024*1024, total=1000*1024*1024)
        mock_psutil.pids.return_value = list(range(100))
        
        # Force system metrics collection
        temp_collector._system_metrics_enabled = True
        temp_collector._last_system_collection = datetime.now() - timedelta(minutes=1)
        temp_collector._maybe_collect_system_metrics()
        
        # Verify system metrics were recorded
        assert "system.cpu_percent" in temp_collector.gauges
        assert "system.memory_percent" in temp_collector.gauges
        assert "system.process_count" in temp_collector.gauges
    
    def test_get_metric_summary(self, temp_collector):
        """Test metric summary generation."""
        # Record some test data
        values = [10.0, 20.0, 30.0, 40.0, 50.0]
        for value in values:
            temp_collector.record_gauge("summary.test", value)
        
        # Flush to storage
        temp_collector._flush_buffers()
        
        # Get summary
        summary = temp_collector.get_metric_summary("summary.test", hours=1)
        
        assert summary["metric_name"] == "summary.test"
        assert summary["count"] == 5
        assert summary["min"] == 10.0
        assert summary["max"] == 50.0
        assert summary["mean"] == 30.0


class TestTimerContext:
    """Test timer context manager."""
    
    def test_timer_context_success(self):
        """Test successful timer context execution."""
        collector = Mock()
        
        with TimerContext(collector, "test.timer", {"tag": "value"}):
            time.sleep(0.01)
        
        collector.record_timer.assert_called_once()
        args = collector.record_timer.call_args
        assert args[0][0] == "test.timer"
        assert args[0][1] > 0  # Duration should be positive
        assert args[0][2] == {"tag": "value"}
    
    def test_timer_context_with_exception(self):
        """Test timer context with exception."""
        collector = Mock()
        
        try:
            with TimerContext(collector, "test.timer"):
                raise ValueError("Test exception")
        except ValueError:
            pass
        
        # Should still record timing even with exception
        collector.record_timer.assert_called_once()


class TestAnalyticsDashboard:
    """Test analytics dashboard functionality."""
    
    @pytest.fixture
    def mock_collector(self):
        """Create mock metrics collector."""
        collector = Mock()
        collector.get_all_metrics_summary.return_value = {
            "system.cpu_percent": {
                "count": 100,
                "latest": 45.0,
                "mean": 40.0,
                "max": 55.0,
                "min": 30.0
            },
            "system.memory_percent": {
                "count": 100,
                "latest": 60.0,
                "mean": 58.0,
                "max": 70.0,
                "min": 50.0
            }
        }
        collector.get_active_alerts.return_value = [
            {
                "id": "alert-1",
                "level": "warning",
                "message": "High CPU usage",
                "timestamp": datetime.now().isoformat()
            }
        ]
        collector.storage = Mock()
        collector.storage.get_metrics.return_value = []
        return collector
    
    @pytest.fixture
    def dashboard(self, mock_collector):
        """Create analytics dashboard with mock collector."""
        config = DashboardConfig(refresh_interval=1)
        return AnalyticsDashboard(mock_collector, config)
    
    def test_dashboard_initialization(self, dashboard):
        """Test dashboard initialization."""
        assert dashboard.metrics_collector is not None
        assert dashboard.config.refresh_interval == 1
        assert dashboard.cached_data == {}
        assert dashboard.chart_cache == {}
    
    def test_generate_dashboard_data(self, dashboard):
        """Test dashboard data generation."""
        data = dashboard.generate_dashboard_data(hours=24)
        
        assert "timestamp" in data
        assert "time_window_hours" in data
        assert "system_overview" in data
        assert "agent_metrics" in data
        assert "optimization_metrics" in data
        assert "performance_trends" in data
        assert "alert_summary" in data
        assert "health_score" in data
        
        # Verify system overview
        system_overview = data["system_overview"]
        assert system_overview["cpu_usage"] == 45.0
        assert system_overview["memory_usage"] == 60.0
    
    def test_system_overview_generation(self, dashboard, mock_collector):
        """Test system overview metrics extraction."""
        all_metrics = mock_collector.get_all_metrics_summary.return_value
        overview = dashboard._generate_system_overview(all_metrics)
        
        assert overview["cpu_usage"] == 45.0
        assert overview["memory_usage"] == 60.0
        assert overview["disk_usage"] == 0.0  # Not provided in mock
        assert overview["request_rate"] == 0.0
        assert overview["error_rate"] == 0.0
    
    def test_health_score_calculation(self, dashboard, mock_collector):
        """Test health score calculation."""
        all_metrics = mock_collector.get_all_metrics_summary.return_value
        active_alerts = mock_collector.get_active_alerts.return_value
        
        health_score = dashboard._calculate_health_score(all_metrics, active_alerts)
        
        assert 0 <= health_score <= 100
        # Should be less than 100 due to warning alert
        assert health_score < 100
    
    def test_alert_summary_generation(self, dashboard, mock_collector):
        """Test alert summary generation."""
        active_alerts = mock_collector.get_active_alerts.return_value
        
        summary = dashboard._generate_alert_summary(active_alerts)
        
        assert summary["total_active"] == 1
        assert summary["by_level"]["warning"] == 1
        assert summary["latest_alert"] is not None
        assert summary["warning_count"] == 1
        assert summary["critical_count"] == 0
    
    def test_empty_alerts_summary(self, dashboard):
        """Test alert summary with no alerts."""
        summary = dashboard._generate_alert_summary([])
        
        assert summary["total_active"] == 0
        assert summary["by_level"] == {}
        assert summary["latest_alert"] is None
    
    @patch('infrastructure.monitoring.analytics_dashboard.plt')
    def test_generate_performance_chart(self, mock_plt, dashboard, mock_collector):
        """Test performance chart generation."""
        # Mock chart creation
        mock_fig = Mock()
        mock_ax = Mock()
        mock_plt.subplots.return_value = (mock_fig, mock_ax)
        mock_plt.savefig = Mock()
        
        # Mock metrics data
        mock_collector.storage.get_metrics.return_value = [
            {
                "timestamp": datetime.now().isoformat(),
                "value": 45.0
            },
            {
                "timestamp": (datetime.now() - timedelta(hours=1)).isoformat(),
                "value": 40.0
            }
        ]
        
        chart = dashboard.generate_performance_chart("system.cpu_percent", hours=24)
        
        # Should return base64 encoded string
        assert isinstance(chart, str)
        assert len(chart) > 0
    
    def test_cache_validity(self, dashboard):
        """Test cache validity checking."""
        cache_key = "test_cache"
        
        # Initially not valid (no cache)
        assert not dashboard._is_cache_valid(cache_key)
        
        # Add to cache
        dashboard.cached_data[cache_key] = {"test": "data"}
        dashboard.last_refresh = datetime.now()
        
        # Should be valid immediately
        assert dashboard._is_cache_valid(cache_key)
    
    def test_export_dashboard_report(self, dashboard):
        """Test dashboard report export."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = str(Path(temp_dir) / "test_report.json")
            
            exported_path = dashboard.export_dashboard_report(output_path, hours=24)
            
            assert exported_path == output_path
            assert Path(output_path).exists()
            
            # Verify report content
            with open(output_path) as f:
                report_data = json.load(f)
            
            assert "timestamp" in report_data
            assert "system_overview" in report_data
            assert "charts" in report_data


class TestGlobalFunctions:
    """Test global function utilities."""
    
    def test_get_metrics_collector(self):
        """Test global metrics collector retrieval."""
        # Should create collector if none exists
        collector1 = get_metrics_collector()
        assert collector1 is not None
        
        # Should return same instance
        collector2 = get_metrics_collector()
        assert collector1 is collector2
        
        # Cleanup
        collector1.stop()
    
    def test_initialize_metrics_system(self):
        """Test metrics system initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = str(Path(temp_dir) / "init_test.db")
            
            collector = initialize_metrics_system(
                storage_path=db_path,
                buffer_size=50,
                flush_interval=30
            )
            
            assert collector is not None
            assert collector.buffer_size == 50
            assert collector.flush_interval == 30
            
            # Should have default thresholds set
            assert "system.cpu_percent" in collector.thresholds
            assert "system.memory_percent" in collector.thresholds
            
            # Cleanup
            shutdown_metrics_system()
    
    def test_shutdown_metrics_system(self):
        """Test metrics system shutdown."""
        # Initialize system
        collector = initialize_metrics_system()
        assert collector is not None
        
        # Shutdown
        shutdown_metrics_system()
        
        # Global collector should be None after shutdown
        # Note: This test depends on implementation details


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"]) 