"""
Tests for DSPy MIPROv2 Optimization Pipeline (Task 1.3).

Tests base optimizer, MIPROv2 optimizer, instruction generation, and adaptive sampling.
"""

import pytest
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import json

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Mock DSPy components since they may not be available in test environment
sys.modules['dspy'] = MagicMock()
sys.modules['dspy.teleprompt'] = MagicMock()
sys.modules['dspy.evaluate'] = MagicMock()

from optimization.dspy.base_optimizer import (
    OptimizationConfig, OptimizationResult, BaseOptimizer,
    BootstrapOptimizer, RandomSearchOptimizer, get_optimizer
)
from optimization.dspy.mipro_v2_optimizer import (
    MIPROv2Config, MIPROv2Optimizer, InstructionGenerator, AdaptiveSampler
)


class TestOptimizationConfig:
    """Test optimization configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = OptimizationConfig()
        
        assert config.max_bootstrapped_demos == 4
        assert config.max_labeled_demos == 16
        assert config.max_rounds == 1
        assert config.num_candidate_programs == 16
        assert config.num_threads == 6
        assert config.max_errors == 10
        assert config.teacher_settings == {}
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = OptimizationConfig(
            max_bootstrapped_demos=8,
            max_labeled_demos=32,
            teacher_settings={"model": "gpt-4"}
        )
        
        assert config.max_bootstrapped_demos == 8
        assert config.max_labeled_demos == 32
        assert config.teacher_settings == {"model": "gpt-4"}


class TestOptimizationResult:
    """Test optimization result structure."""
    
    def test_result_creation(self):
        """Test OptimizationResult creation."""
        mock_program = Mock()
        result = OptimizationResult(
            optimized_program=mock_program,
            optimization_score=0.85,
            evaluation_results={"accuracy": 0.85, "f1": 0.82},
            best_config={"param1": "value1"},
            optimization_history=[{"step": 1, "score": 0.8}]
        )
        
        assert result.optimized_program == mock_program
        assert result.optimization_score == 0.85
        assert result.evaluation_results["accuracy"] == 0.85
        assert len(result.optimization_history) == 1


class TestBootstrapOptimizer:
    """Test BootstrapOptimizer implementation."""
    
    @pytest.fixture
    def mock_examples(self):
        """Create mock DSPy examples."""
        examples = []
        for i in range(10):
            example = Mock()
            example.question = f"Question {i}?"
            example.answer = f"Answer {i}"
            examples.append(example)
        return examples
    
    @pytest.fixture
    def mock_program(self):
        """Create mock DSPy program."""
        program = Mock()
        program.signature = {"instructions": "Answer the question"}
        return program
    
    @patch('optimization.dspy.base_optimizer.BootstrapFewShot')
    @patch('optimization.dspy.base_optimizer.Evaluate')
    def test_bootstrap_optimization(self, mock_evaluate, mock_bootstrap, mock_examples, mock_program):
        """Test BootstrapOptimizer optimization process."""
        # Setup mocks
        mock_optimizer_instance = Mock()
        mock_bootstrap.return_value = mock_optimizer_instance
        mock_optimizer_instance.compile.return_value = mock_program
        
        mock_evaluate_instance = Mock()
        mock_evaluate.return_value = mock_evaluate_instance
        mock_evaluate_instance.__call__ = Mock(return_value=0.85)
        
        # Create optimizer and run
        optimizer = BootstrapOptimizer()
        result = optimizer.optimize(mock_program, mock_examples)
        
        # Verify results
        assert isinstance(result, OptimizationResult)
        assert result.optimized_program == mock_program
        assert result.optimization_score == 0.85
        assert len(result.optimization_history) > 0
    
    def test_bootstrap_optimizer_initialization(self):
        """Test BootstrapOptimizer initialization."""
        config = OptimizationConfig(max_bootstrapped_demos=8)
        optimizer = BootstrapOptimizer(config)
        
        assert optimizer.config.max_bootstrapped_demos == 8
        assert optimizer.optimization_history == []
    
    def test_example_preparation(self, mock_examples):
        """Test example preparation and validation."""
        optimizer = BootstrapOptimizer()
        prepared = optimizer._prepare_examples(mock_examples)
        
        assert len(prepared) == len(mock_examples)
        assert all(hasattr(ex, 'question') for ex in prepared)
    
    def test_empty_examples_error(self):
        """Test error handling for empty examples."""
        optimizer = BootstrapOptimizer()
        
        with pytest.raises(ValueError, match="No examples provided"):
            optimizer._prepare_examples([])


class TestRandomSearchOptimizer:
    """Test RandomSearchOptimizer implementation."""
    
    @pytest.fixture
    def mock_examples(self):
        """Create mock examples for medium dataset."""
        examples = []
        for i in range(50):
            example = Mock()
            example.question = f"Question {i}?"
            example.answer = f"Answer {i}"
            examples.append(example)
        return examples
    
    @patch('optimization.dspy.base_optimizer.BootstrapFewShotWithRandomSearch')
    @patch('optimization.dspy.base_optimizer.Evaluate')
    def test_random_search_optimization(self, mock_evaluate, mock_random_search, mock_examples):
        """Test RandomSearchOptimizer optimization process."""
        mock_program = Mock()
        
        # Setup mocks
        mock_optimizer_instance = Mock()
        mock_random_search.return_value = mock_optimizer_instance
        mock_optimizer_instance.compile.return_value = mock_program
        
        mock_evaluate_instance = Mock()
        mock_evaluate.return_value = mock_evaluate_instance
        mock_evaluate_instance.__call__ = Mock(return_value=0.78)
        
        # Create optimizer and run
        optimizer = RandomSearchOptimizer()
        result = optimizer.optimize(mock_program, mock_examples)
        
        # Verify results
        assert isinstance(result, OptimizationResult)
        assert result.optimization_score == 0.78
        assert "random_search" in str(result.optimization_history).lower()
    
    def test_small_dataset_warning(self, capfd):
        """Test warning for small datasets."""
        small_examples = [Mock() for _ in range(5)]
        optimizer = RandomSearchOptimizer()
        
        # This should print a warning
        prepared = optimizer._prepare_examples(small_examples)
        
        assert len(prepared) == 5


class TestMIPROv2Config:
    """Test MIPROv2 configuration."""
    
    def test_default_miprov2_config(self):
        """Test MIPROv2Config default values."""
        config = MIPROv2Config()
        
        assert config.num_instruction_candidates == 50
        assert config.num_instruction_iterations == 25
        assert config.enable_multi_stage is True
        assert config.enable_parallel_evaluation is True
        assert config.enable_checkpointing is True
        assert config.diversity_penalty == 0.1
    
    def test_miprov2_config_post_init(self):
        """Test MIPROv2Config post-initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = MIPROv2Config(checkpoint_dir=temp_dir)
            
            # Should create checkpoint directory
            assert Path(temp_dir).exists()
    
    def test_custom_miprov2_config(self):
        """Test custom MIPROv2Config values."""
        config = MIPROv2Config(
            num_instruction_candidates=100,
            enable_multi_stage=False,
            temperature_schedule=[1.5, 1.0, 0.5]
        )
        
        assert config.num_instruction_candidates == 100
        assert config.enable_multi_stage is False
        assert config.temperature_schedule == [1.5, 1.0, 0.5]


class TestInstructionGenerator:
    """Test instruction generation components."""
    
    @pytest.fixture
    def instruction_generator(self):
        """Create InstructionGenerator instance."""
        config = MIPROv2Config()
        return InstructionGenerator(config)
    
    @pytest.fixture
    def mock_program(self):
        """Create mock program with signature."""
        program = Mock()
        program.signature = {"instructions": "Answer the question based on context"}
        return program
    
    @pytest.fixture
    def mock_examples(self):
        """Create mock examples with different characteristics."""
        examples = []
        
        # Simple example
        example1 = Mock()
        example1.question = "What is the capital of France?"
        example1.answer = "Paris"
        examples.append(example1)
        
        # Complex comparison example
        example2 = Mock()
        example2.question = "Compare and contrast renewable energy vs fossil fuels"
        example2.answer = "Renewable energy is cleaner but fossil fuels are more established"
        examples.append(example2)
        
        # Reasoning example
        example3 = Mock()
        example3.question = "Why do plants need sunlight?"
        example3.answer = "Because photosynthesis requires light energy"
        examples.append(example3)
        
        return examples
    
    def test_generate_initial_instructions(self, instruction_generator, mock_program, mock_examples):
        """Test initial instruction generation."""
        instructions = instruction_generator.generate_initial_instructions(
            mock_program, mock_examples, 10
        )
        
        assert len(instructions) <= 10
        assert len(instructions) > 0
        assert any("answer the question based on context" in inst.lower() for inst in instructions)
    
    def test_generate_from_examples(self, instruction_generator, mock_examples):
        """Test instruction generation from examples."""
        base_instruction = "Answer the question"
        example_instructions = instruction_generator._generate_from_examples(
            mock_examples, base_instruction, 3
        )
        
        assert len(example_instructions) <= 3
        # Should generate instructions based on example patterns
        if example_instructions:
            assert all(base_instruction.lower() in inst for inst in example_instructions)
    
    def test_refine_instruction_high_performance(self, instruction_generator):
        """Test instruction refinement for high performance."""
        instruction = "Answer the question carefully"
        performance_data = {"score": 0.9}
        
        refined = instruction_generator.refine_instruction(
            instruction, performance_data, 0
        )
        
        assert "ensure accuracy" in refined.lower()
        assert instruction.lower() in refined.lower()
    
    def test_refine_instruction_low_performance(self, instruction_generator):
        """Test instruction refinement for low performance."""
        instruction = "Answer the question"
        performance_data = {"score": 0.3}
        
        refined = instruction_generator.refine_instruction(
            instruction, performance_data, 0
        )
        
        assert "carefully examine" in refined.lower()
        assert instruction.lower() in refined.lower()


class TestAdaptiveSampler:
    """Test adaptive sampling components."""
    
    @pytest.fixture
    def adaptive_sampler(self):
        """Create AdaptiveSampler instance."""
        config = MIPROv2Config()
        return AdaptiveSampler(config)
    
    def test_sample_candidates(self, adaptive_sampler):
        """Test candidate sampling with scores."""
        candidates = [
            "Instruction 1",
            "Instruction 2", 
            "Instruction 3",
            "Instruction 4"
        ]
        scores = [0.8, 0.6, 0.9, 0.7]
        
        selected = adaptive_sampler.sample_candidates(candidates, scores, iteration=0)
        
        assert len(selected) <= len(candidates)
        assert len(selected) > 0
        assert all(candidate in candidates for candidate in selected)
    
    def test_temperature_schedule(self, adaptive_sampler):
        """Test temperature scheduling."""
        # Test different iterations
        temp0 = adaptive_sampler._get_temperature(0)
        temp1 = adaptive_sampler._get_temperature(1)
        temp_high = adaptive_sampler._get_temperature(100)
        
        assert temp0 >= temp1  # Temperature should decrease
        assert temp_high > 0   # Should not go to zero
    
    def test_softmax_calculation(self, adaptive_sampler):
        """Test softmax probability calculation."""
        scores = [0.8, 0.6, 0.9, 0.7]
        temperature = 1.0
        
        probabilities = adaptive_sampler._softmax(scores, temperature)
        
        assert len(probabilities) == len(scores)
        assert abs(sum(probabilities) - 1.0) < 1e-6  # Should sum to 1
        assert all(p > 0 for p in probabilities)  # All positive
    
    def test_diversity_penalty(self, adaptive_sampler):
        """Test diversity penalty application."""
        candidates = [
            "Answer the question carefully",
            "Answer the question thoroughly", 
            "Solve the math problem",
            "Calculate the result"
        ]
        probabilities = [0.25, 0.25, 0.25, 0.25]
        
        adjusted = adaptive_sampler._apply_diversity_penalty(candidates, probabilities)
        
        assert len(adjusted) == len(probabilities)
        assert abs(sum(adjusted) - 1.0) < 1e-6  # Should still sum to 1
    
    def test_mismatched_candidates_scores_error(self, adaptive_sampler):
        """Test error for mismatched candidates and scores."""
        candidates = ["A", "B", "C"]
        scores = [0.8, 0.6]  # Mismatch
        
        with pytest.raises(ValueError, match="same length"):
            adaptive_sampler.sample_candidates(candidates, scores, 0)


class TestMIPROv2Optimizer:
    """Test MIPROv2Optimizer implementation."""
    
    @pytest.fixture
    def miprov2_optimizer(self):
        """Create MIPROv2Optimizer instance."""
        config = MIPROv2Config(
            num_instruction_candidates=5,  # Small for testing
            num_instruction_iterations=2,
            enable_checkpointing=False  # Disable for testing
        )
        return MIPROv2Optimizer(config)
    
    @pytest.fixture
    def mock_large_examples(self):
        """Create mock examples for large dataset."""
        examples = []
        for i in range(300):
            example = Mock()
            example.question = f"Question {i}?"
            example.answer = f"Answer {i}"
            examples.append(example)
        return examples
    
    def test_miprov2_initialization(self, miprov2_optimizer):
        """Test MIPROv2Optimizer initialization."""
        assert miprov2_optimizer.instruction_generator is not None
        assert miprov2_optimizer.adaptive_sampler is not None
        assert miprov2_optimizer.optimization_state == {}
    
    def test_create_program_with_instruction(self, miprov2_optimizer):
        """Test creating program with instruction."""
        mock_program = Mock()
        mock_program.deepcopy = Mock(return_value=mock_program)
        mock_program.signature = Mock()
        mock_program.signature.instructions = "old instruction"
        
        instruction = "new instruction"
        
        result_program = miprov2_optimizer._create_program_with_instruction(
            mock_program, instruction
        )
        
        assert result_program is not None
    
    def test_get_stage_config(self, miprov2_optimizer):
        """Test stage configuration generation."""
        # Test different stages
        stage0_config = miprov2_optimizer._get_stage_config(0)  # Exploration
        stage1_config = miprov2_optimizer._get_stage_config(1)  # Refinement
        stage2_config = miprov2_optimizer._get_stage_config(2)  # Fine-tuning
        
        # Exploration should have different params than fine-tuning
        assert stage0_config.temperature_schedule != stage2_config.temperature_schedule
        assert stage0_config.num_instruction_candidates >= stage2_config.num_instruction_candidates
    
    @patch('optimization.dspy.mipro_v2_optimizer.MIPROv2Optimizer._evaluate_single_instruction')
    def test_evaluate_instructions_sequential(self, mock_evaluate, miprov2_optimizer):
        """Test sequential instruction evaluation."""
        mock_evaluate.return_value = 0.8
        
        instructions = ["Instruction 1", "Instruction 2"]
        mock_program = Mock()
        mock_examples = [Mock(), Mock()]
        
        scores = miprov2_optimizer._evaluate_instructions_sequential(
            instructions, mock_program, mock_examples, mock_examples
        )
        
        assert len(scores) == len(instructions)
        assert all(score == 0.8 for score in scores)
    
    def test_checkpoint_operations(self):
        """Test checkpoint save/load operations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = MIPROv2Config(
                checkpoint_dir=temp_dir,
                enable_checkpointing=True
            )
            optimizer = MIPROv2Optimizer(config)
            
            # Set some optimization state
            optimizer.optimization_state = {
                "best_score": 0.85,
                "best_instruction": "test instruction"
            }
            optimizer.optimization_history = [{"step": 1, "score": 0.8}]
            
            # Save checkpoint
            optimizer._save_checkpoint()
            
            # Verify checkpoint file exists
            checkpoint_file = Path(temp_dir) / "miprov2_checkpoint.json"
            assert checkpoint_file.exists()
            
            # Load checkpoint in new optimizer
            new_optimizer = MIPROv2Optimizer(config)
            new_optimizer._load_checkpoint(checkpoint_file)
            
            assert new_optimizer.optimization_state["best_score"] == 0.85
            assert len(new_optimizer.optimization_history) == 1


class TestOptimizerFactory:
    """Test optimizer factory function."""
    
    def test_get_optimizer_small_dataset(self):
        """Test getting optimizer for small dataset."""
        optimizer = get_optimizer(dataset_size=5)
        
        assert isinstance(optimizer, BootstrapOptimizer)
    
    def test_get_optimizer_medium_dataset(self):
        """Test getting optimizer for medium dataset."""
        optimizer = get_optimizer(dataset_size=50)
        
        assert isinstance(optimizer, RandomSearchOptimizer)
    
    @patch('optimization.dspy.base_optimizer.MIPROv2Optimizer')
    def test_get_optimizer_large_dataset(self, mock_miprov2):
        """Test getting optimizer for large dataset."""
        mock_instance = Mock()
        mock_miprov2.return_value = mock_instance
        
        optimizer = get_optimizer(dataset_size=500)
        
        # Should attempt to use MIPROv2
        mock_miprov2.assert_called_once()
    
    def test_get_optimizer_with_config(self):
        """Test getting optimizer with custom config."""
        config = OptimizationConfig(max_bootstrapped_demos=8)
        optimizer = get_optimizer(dataset_size=5, config=config)
        
        assert isinstance(optimizer, BootstrapOptimizer)
        assert optimizer.config.max_bootstrapped_demos == 8


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"]) 