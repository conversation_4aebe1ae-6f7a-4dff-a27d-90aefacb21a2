# DSPy Multi-Agent System API Requirements
# Core API framework
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
websockets>=12.0

# Data validation and serialization
pydantic-settings>=2.1.0

# Async file handling
aiofiles>=23.2.0
python-multipart>=0.0.6

# Session storage (Redis optional, SQLite fallback)
redis>=5.0.1
aioredis>=2.0.1

# HTTP client for testing
httpx>=0.25.2

# Development and testing
pytest-asyncio>=0.21.1

# Optional: Enhanced monitoring (if available)
# prometheus-client==0.19.0
# grafana-api==1.0.3
