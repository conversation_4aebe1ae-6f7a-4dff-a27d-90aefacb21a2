#!/usr/bin/env python
"""
Main entry point for the Multi-Agent Question Answering System.

Provides both CLI and programmatic interfaces for running the
hierarchical multi-agent workflow with CrewAI 2025 Flows + Phase 1 Enhancements.
"""

import asyncio
import sys
import json
import argparse
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
import traceback
import time
import dspy

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# CRITICAL: Initialize debug logging FIRST
from src.infrastructure.monitoring.debug_logger import init_debug_logging
debug_logger = init_debug_logging()
print(f"🔍 Debug logging initialized: {debug_logger.get_session_summary()}")

# CRITICAL: Initialize configuration FIRST to set environment variables
# This must happen before any CrewAI imports to ensure API keys are available
from src.infrastructure.config.settings import initialize_config
print("🔧 Initializing configuration and environment variables...")
config = initialize_config()
print(f"✅ Configuration loaded from config.yaml")
print(f"✅ Set OPENAI_API_KEY environment variable for CrewAI")
print(f"✅ Configuration ready: {config.llm.provider} with {config.llm.model_name}")

# CRITICAL: Configure DSPy with language model BEFORE any DSPy imports
print("🧠 Configuring DSPy with language model...")
dspy_lm = dspy.LM(model=config.llm.teacher_model, api_key=config.llm.api_key)
dspy.configure(lm=dspy_lm)
print(f"✅ DSPy configured with {config.llm.teacher_model}")

# PHASE 1 ENHANCEMENTS: Initialize Enterprise Vector Knowledge Base
from src.infrastructure.storage.enterprise_vector_db import EnterpriseVectorDatabase, VectorDBType
from src.infrastructure.storage.enterprise_embedding_service import EnterpriseEmbeddingService, EmbeddingProvider

# PHASE 1 ENHANCEMENTS: Initialize DSPy MIPROv2 Optimization
from src.optimization.dspy.base_optimizer import get_optimizer, OptimizationConfig
from src.optimization.dspy.mipro_v2_optimizer import MIPROv2Config

# PHASE 1 ENHANCEMENTS: Initialize Production Monitoring
from src.infrastructure.monitoring.metrics_collector import initialize_metrics_system, get_metrics_collector
from src.infrastructure.monitoring.analytics_dashboard import AnalyticsDashboard

# PHASE 1 ENHANCEMENTS: Initialize Training Data Collection for Continuous Learning
from src.optimization.dspy.training_data_collector import initialize_training_data_collector, get_training_data_collector

# ENHANCED EVALUATION PIPELINE: Initialize Automated Evaluation Framework
from src.optimization.dspy.evaluation.evaluation_pipeline import AutomatedEvaluationPipeline
from src.optimization.dspy.evaluation.quality_gates import QualityGateSystem, QualityThresholds

# Now import CrewAI flows after environment is properly set
from src.orchestration.flows.main_workflow import MainWorkflowFlow, run_main_workflow
from src.core.models.state_models import FlowResult

# NEW: Import Enhanced Coordination Flow
from src.orchestration.flows.advanced_coordination_flow import AdvancedCoordinationFlow
from src.core.models.state_models import WorkflowState

# Initialize Phase 1 enhancements globally
print("🚀 Initializing Phase 1 Enterprise Enhancements...")
print("   • Enterprise Vector Knowledge Base")
print("   • DSPy MIPROv2 Optimization Pipeline") 
print("   • Production Monitoring and Analytics")
print("   • Continuous Learning Training Data Collection")

# Initialize enterprise vector database
vector_db = EnterpriseVectorDatabase(
    config={
        "provider": "chroma",  # Using Chroma for development
        "collection_name": "multi_agent_kb",
        "persist_directory": "./data/chroma_db",
        "dimension": 1536  # OpenAI embedding dimension
    }
)

# Initialize enterprise embedding service
embedding_service = EnterpriseEmbeddingService(
    config={
        "provider": "openai",
        "model_name": "text-embedding-3-small", 
        "dimension": 1536,
        "batch_size": 100,
        "cache_enabled": True
    }
)

# Initialize DSPy optimization with MIPROv2 for large datasets
mipro_config = MIPROv2Config(
    num_instruction_candidates=50,
    num_instruction_iterations=20,
    enable_multi_stage=True,
    enable_adaptive_learning=True,
    temperature_schedule=[1.0, 0.8, 0.6, 0.4],
    max_workers=4,
    enable_parallel_evaluation=True,
    enable_checkpointing=True
)

# Initialize production monitoring
metrics_collector = initialize_metrics_system(
    storage_path="monitoring/metrics.db",
    buffer_size=100,
    flush_interval=30
)
analytics_dashboard = AnalyticsDashboard(metrics_collector)

# Initialize training data collection for continuous learning
training_data_collector = initialize_training_data_collector(
    db_path="data/training_data.db",
    min_examples_for_optimization=500,
    min_quality_threshold=0.7
)

print("✅ Phase 1 Enterprise Enhancements initialized successfully")


def load_config_file() -> Dict[str, Any]:
    """Load configuration from config.yaml."""
    # Look for config.yaml in the project root (parent directory of src)
    current_dir = Path(__file__).parent  # src directory
    config_path = current_dir.parent / "config.yaml"  # project root / config.yaml
    
    if config_path.exists():
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    
    # Fallback: try current directory (for when run from project root)
    fallback_path = Path("config.yaml")
    if fallback_path.exists():
        with open(fallback_path, 'r') as f:
            return yaml.safe_load(f)
    
    print(f"⚠️ Config file not found at {config_path} or {fallback_path}")
    return {}

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Multi-Agent Question Answering System")
    parser.add_argument("query", nargs="?", help="Question to ask the system")
    parser.add_argument("--enhanced-flow", action="store_true", 
                       help="Use the enhanced coordination flow with advanced features")
    parser.add_argument("--config", default="config.yaml", 
                       help="Path to configuration file")
    parser.add_argument("--output", help="Output file for results")
    return parser.parse_args()

async def run_workflow(query: str, use_enhanced_flow: bool, config_data: Dict[str, Any]) -> str:
    """Run the appropriate workflow based on configuration."""
    
    if use_enhanced_flow:
        print("🚀 Starting Enhanced Coordination Flow")
        print("=" * 70)
        print("🎯 Advanced Features Active:")
        print("   ✅ Dynamic Complexity Analysis")
        print("   ✅ Intelligent Workflow Routing")
        print("   ✅ Parallel Processing with Load Balancing")  
        print("   ✅ Real-time Monitoring and Error Recovery")
        print("   ✅ Enhanced Specialist Integration")
        print("   ✅ DSPy Chain-of-Thought Reasoning")
        print("=" * 70)
        
        
        # Initialize enhanced workflow with proper configuration
        workflow = AdvancedCoordinationFlow(config_data)
        
        # Execute enhanced workflow with inputs (don't set state directly)
        inputs = {
            "original_query": query,
            "processed_query": query,
            "workflow_id": workflow.flow_id,
            "session_id": f"enhanced_session_{int(time.time())}"
        }
        
        result = await workflow.kickoff_async(inputs=inputs)
        return str(result)
    else:
        print("🔄 Starting Main Workflow (Default)")
        print("=" * 70)
        print("🎯 Standard Features Active:")
        print("   ✅ Task Planning with TaskManagerFlow")
        print("   ✅ Enhanced Specialist Execution")
        print("   ✅ Final Synthesis with WriterFlow")
        print("   ✅ Training Data Collection")
        print("   ✅ Production Monitoring")
        print("=" * 70)
        
        # Use working main workflow (the original approach)
        system = MultiAgentSystem()
        result = await system.answer_question(query, config_data)
        
        # Extract final answer properly from the workflow result
        final_answer = None
        
        # Try multiple extraction methods
        if isinstance(result, dict):
            # First try direct final_answer key
            final_answer = result.get('final_answer')
            
            # If not found, try workflow state
            if not final_answer and 'workflow_summary' in result:
                workflow_summary = result['workflow_summary']
                if isinstance(workflow_summary, dict):
                    final_answer = workflow_summary.get('final_answer')
            
            # If still not found, try to get from system workflow state
            if not final_answer and hasattr(system, 'workflow') and system.workflow:
                if hasattr(system.workflow.state, 'final_answer'):
                    final_answer = system.workflow.state.final_answer
        
        # If we still don't have a final answer, create one from available data
        if not final_answer:
            if isinstance(result, dict) and result.get('success'):
                # Create a summary from available results
                final_answer = f"Analysis completed for: {query}"
                
                # Add research findings if available
                detailed_results = result.get('detailed_results', {})
                if detailed_results:
                    research_findings = detailed_results.get('research_findings', [])
                    library_findings = detailed_results.get('library_findings', [])
                    analysis_findings = detailed_results.get('analysis_findings', [])
                    
                    if research_findings or library_findings or analysis_findings:
                        final_answer = f"Comprehensive analysis completed for: {query}\n\n"
                        if research_findings:
                            final_answer += f"Research findings: {len(research_findings)} results\n"
                        if library_findings:
                            final_answer += f"Library knowledge: {len(library_findings)} results\n"
                        if analysis_findings:
                            final_answer += f"Data analysis: {len(analysis_findings)} results\n"
            else:
                final_answer = f"Unable to process query: {query}"
        
        # Add JSON save prompt (like it was before)
        if isinstance(result, dict) and result.get('success') and final_answer:
            print(f"\n📄 Would you like to save the complete workflow result to JSON? (y/n): ", end="")
            try:
                save_choice = input().strip().lower()
                if save_choice in ['y', 'yes']:
                    filename = system.save_result(result)
                    if filename:
                        print(f"✅ Complete workflow result saved to: {filename}")
            except:
                # Handle cases where input is not available (like in tests)
                pass
        
        return final_answer or str(result)


class MultiAgentSystem:
    """
    Main interface for the Multi-Agent Question Answering System.
    
    Enhanced with Phase 1 enterprise features:
    - Enterprise Vector Knowledge Base for advanced document retrieval
    - DSPy MIPROv2 Optimization for intelligent prompt optimization
    - Production Monitoring and Analytics for system observability
    """
    
    def __init__(self):
        self.workflow = None
        self.last_result = None
        
        # Phase 1 Enhancement Integration
        self.vector_db = vector_db
        self.embedding_service = embedding_service
        self.metrics_collector = metrics_collector
        self.analytics_dashboard = analytics_dashboard
        
        # Track system initialization metrics
        self.metrics_collector.record_counter("system.initialization", 1.0, {"component": "MultiAgentSystem"})
    
    async def answer_question(self, question: str, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Answer a question using the complete multi-agent workflow with Phase 1 enhancements.
        
        Args:
            question: The question to answer
            config: Optional configuration parameters
            
        Returns:
            Dictionary containing the complete workflow result with enhanced analytics
        """
        start_time = time.time()
        session_id = f"session_{int(start_time)}"
        
        try:
            print("🚀 Starting Enhanced Multi-Agent Question Answering System")
            print("=" * 80)
            print(f"📝 Question: {question}")
            print("=" * 80)
            
            # PHASE 1: Start system monitoring
            with self.metrics_collector.time_operation("workflow.total_execution", {"session_id": session_id}):
                
                # PHASE 1: Enhanced vector knowledge preparation
                print("🔍 Phase 1: Preparing Enterprise Vector Knowledge Base...")
                try:
                    # Check if we have relevant knowledge in vector store
                    query_embedding = await self._get_query_embedding(question)
                    relevant_docs = await self._search_knowledge_base(question, query_embedding)
                    
                    self.metrics_collector.record_gauge("knowledge_base.relevant_docs", len(relevant_docs), 
                                                       {"session_id": session_id})
                    print(f"   📚 Found {len(relevant_docs)} relevant documents in knowledge base")
                    
                except Exception as e:
                    print(f"   ⚠️ Vector knowledge base search failed: {e}")
                    relevant_docs = []
                    self.metrics_collector.add_alert_callback(lambda alert: print(f"🚨 Alert: {alert.message}"))
                
                # PHASE 1: DSPy MIPROv2 optimization for query processing
                print("🧠 Phase 1: Applying DSPy MIPROv2 Optimization...")
                # DSPy optimization with continuous learning
                optimized_query = question
                if (config or {}).get("enable_optimization", True):
                    try:
                        print("   🤖 Checking DSPy optimization with continuous learning...")
                        
                        # Get current training data statistics
                        training_stats = await training_data_collector.get_dataset_stats()
                        print(f"   📊 Training dataset: {training_stats['total_examples']} examples, {training_stats['avg_quality']:.2f} avg quality")
                        
                        # Use dynamic dataset size for optimization decision
                        if training_stats['ready_for_optimization']:
                            print(f"   🎯 Dataset ready for MIPROv2 optimization ({training_stats['total_examples']} examples)")
                            
                            # Get high-quality training examples
                            training_examples = await training_data_collector.get_training_examples(
                                min_quality=0.7,
                                limit=500
                            )
                            
                            # Use actual dataset size to trigger MIPROv2
                            optimizer = get_optimizer(
                                dataset_size=len(training_examples),
                                config=mipro_config,
                                metric=None  # Will use default metric
                            )
                            optimized_query = await self._optimize_query(question, optimizer, training_examples)
                            
                            self.metrics_collector.record_gauge("optimization.query_enhancement", 1.0,
                                                               {"session_id": session_id, "method": "mipro_v2"})
                            print(f"   🎯 Query optimized with MIPROv2 using {len(training_examples)} training examples")
                        
                        else:
                            # Use basic query enhancement patterns until we have enough training data
                            optimized_query = self._enhance_query_prompt(question)
                            print(f"   📝 Using basic query enhancement (need {500 - training_stats['total_examples']} more examples for MIPROv2)")
                            
                            self.metrics_collector.record_gauge("optimization.query_enhancement", 1.0,
                                                               {"session_id": session_id, "method": "basic_patterns"})
                        
                    except Exception as e:
                        print(f"   ⚠️ DSPy optimization failed, using original query: {e}")
                        optimized_query = question
                        self.metrics_collector.record_counter("optimization.failures", 1.0, {"error": str(e)})
                
                # Initialize workflow with enhanced context
                self.workflow = MainWorkflowFlow()
                
                # Set the enhanced query and context
                self.workflow.state.original_query = question
                self.workflow.state.processed_query = optimized_query
                self.workflow.state.context.update({
                    "session_id": session_id,
                    "relevant_docs": relevant_docs,
                    "phase1_enhancements": True,
                    "vector_knowledge_available": len(relevant_docs) > 0,
                    "query_optimized": optimized_query != question
                })
                
                # Apply configuration if provided
                if config:
                    # Update config attributes individually since SystemConfig is a dataclass
                    for key, value in config.items():
                        if hasattr(self.workflow.state.config, key):
                            setattr(self.workflow.state.config, key, value)
                
                # Run the complete workflow with monitoring
                print("\n🎯 Executing Enhanced Hierarchical Multi-Agent Workflow...")
                print("   • Real-time performance monitoring active")
                print("   • Enterprise vector knowledge integration active")
                print("   • DSPy intelligence optimization active")
                
                result = await self.workflow.kickoff_async()
                
                # Store result for later access
                self.last_result = result
                
                # PHASE 1: Generate enhanced analytics
                execution_time = time.time() - start_time
                self.metrics_collector.record_timer("workflow.execution_time", execution_time, 
                                                   {"session_id": session_id})
                
                # Generate real-time dashboard data
                dashboard_data = self.analytics_dashboard.generate_dashboard_data(hours=1)
                
                # Create comprehensive result with Phase 1 enhancements
                workflow_result = {
                    "success": True,
                    "question": question,
                    "final_answer": self.workflow.state.final_answer,
                    "workflow_summary": self.workflow.state.get_summary(),
                    "execution_metrics": {
                        "total_time": self.workflow.state.metrics.total_execution_time,
                        "success_rate": self.workflow.state.metrics.success_rate,
                        "research_results": len(self.workflow.state.research_results),
                        "library_results": len(self.workflow.state.library_results),
                        "analysis_results": len(self.workflow.state.analysis_results),
                        "error_count": len(self.workflow.state.errors)
                    },
                    "quality_metrics": {
                        "answer_quality_score": self.workflow.state.answer_quality_score,
                        "completion_percentage": self.workflow.state.get_completion_percentage()
                    },
                    "phase1_enhancements": {
                        "vector_knowledge_used": len(relevant_docs),
                        "query_optimization_applied": optimized_query != question,
                        "real_time_monitoring": True,
                        "analytics_dashboard_available": True,
                        "system_health_score": dashboard_data.get("health_score", 0)
                    },
                    "detailed_results": {
                        "research_findings": [result.model_dump() for result in self.workflow.state.research_results],
                        "library_findings": [result.model_dump() for result in self.workflow.state.library_results],
                        "analysis_findings": [result.model_dump() for result in self.workflow.state.analysis_results],
                        "vector_knowledge_context": relevant_docs[:3],  # Include top 3 for context
                        "optimization_details": {
                            "original_query": question,
                            "optimized_query": optimized_query,
                            "optimization_method": "mipro_v2"
                        }
                    },
                    "system_analytics": {
                        "dashboard_summary": dashboard_data.get("system_overview", {}),
                        "performance_trends": dashboard_data.get("performance_trends", {}),
                        "session_id": session_id
                    }
                }
                
                print("\n✅ Enhanced Multi-Agent Workflow Completed Successfully!")
                self._display_enhanced_summary(workflow_result)
                
                # PHASE 1: Collect training data for continuous learning
                try:
                    example_id = await self.enhanced_collect_training_example(
                        session_id=session_id,
                        original_query=question,
                        workflow_result=workflow_result,
                        training_data_collector=training_data_collector,
                        config_data=config
                    )
                    
                    # Store example_id in result for potential feedback
                    workflow_result["training_example_id"] = example_id
                    print(f"   📚 Training example collected: {example_id[:8]}...")
                    
                except Exception as e:
                    print(f"   ⚠️ Training data collection failed: {e}")
                    self.metrics_collector.record_counter("training_data.collection_failures", 1.0, {"error": str(e)})
                
                return workflow_result
                
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Enhanced workflow failed: {str(e)}"
            
            # Record error metrics
            self.metrics_collector.record_counter("workflow.errors", 1.0, 
                                                 {"error_type": type(e).__name__, "session_id": session_id})
            self.metrics_collector.record_timer("workflow.failed_execution_time", execution_time,
                                               {"session_id": session_id})
            
            print(f"\n❌ {error_msg}")
            print(f"Traceback: {traceback.format_exc()}")
            
            return {
                "success": False,
                "question": question,
                "error": str(e),
                "traceback": traceback.format_exc(),
                "phase1_enhancements": {
                    "monitoring_captured_error": True,
                    "session_id": session_id,
                    "execution_time": execution_time
                }
            }
    
    async def _get_query_embedding(self, query: str) -> Optional[list]:
        """Get embedding for query using enterprise embedding service."""
        try:
            embedding = await self.embedding_service.get_embedding_async(query)
            return embedding
        except Exception as e:
            print(f"   ⚠️ Embedding generation failed: {e}")
            return None
    
    async def _search_knowledge_base(self, query: str, embedding: Optional[list]) -> list:
        """Search enterprise vector knowledge base for relevant documents."""
        try:
            if embedding is None:
                return []
            
            results = await self.vector_db.search_async(
                query_embedding=embedding,
                limit=5,
                threshold=0.7
            )
            return results
        except Exception as e:
            print(f"   ⚠️ Knowledge base search failed: {e}")
            return []
    
    async def _optimize_query(self, query: str, optimizer, training_examples: list) -> str:
        """Optimize query using DSPy MIPROv2."""
        try:
            # Simple query enhancement until training data is available
            enhanced_query = self._enhance_query_prompt(query)
            
            # TODO: Implement full MIPROv2 optimization when training data is collected
            # This would involve:
            # 1. Loading previous Q&A examples as training data
            # 2. Running MIPROv2 optimization on query processing patterns
            # 3. Using optimized instructions for query refinement
            
            return enhanced_query
        except Exception as e:
            print(f"   ⚠️ Query optimization failed: {e}")
            return query
    
    def _enhance_query_prompt(self, query: str) -> str:
        """Apply basic query enhancement patterns."""
        # Analyze query characteristics
        words = query.lower().split()
        
        # Mathematical/scientific questions
        if any(word in words for word in ['ratio', 'fibonacci', 'mathematical', 'formula', 'equation']):
            return f"Provide a comprehensive analysis of {query}, including mathematical definitions, formulas, examples, and practical applications."
        
        # Comparison questions
        elif any(word in words for word in ['difference', 'compare', 'versus', 'vs', 'better']):
            return f"Compare and contrast the key aspects of {query}, highlighting similarities, differences, advantages, and disadvantages with specific examples."
        
        # Explanatory questions
        elif any(word in words for word in ['why', 'how', 'what', 'explain']):
            return f"Explain {query} with clear definitions, step-by-step reasoning, relevant examples, and practical implications."
        
        # Default enhancement
        else:
            return f"Provide a detailed, well-structured response to: {query}, including key concepts, examples, and relevant context."
    
    def _display_enhanced_summary(self, result: Dict[str, Any]) -> None:
        """Display a comprehensive summary with Phase 1 enhancements."""
        print("\n" + "=" * 80)
        print("📊 ENHANCED WORKFLOW EXECUTION SUMMARY")
        print("=" * 80)
        
        metrics = result.get("execution_metrics", {})
        quality = result.get("quality_metrics", {})
        phase1 = result.get("phase1_enhancements", {})
        
        print(f"⏱️  Total Execution Time: {metrics.get('total_time', 0):.2f} seconds")
        print(f"✅ Success Rate: {metrics.get('success_rate', 0):.1%}")
        print(f"🔬 Research Results: {metrics.get('research_results', 0)}")
        print(f"📚 Library Results: {metrics.get('library_results', 0)}")
        print(f"📊 Analysis Results: {metrics.get('analysis_results', 0)}")
        print(f"❌ Errors: {metrics.get('error_count', 0)}")
        print(f"⭐ Answer Quality: {quality.get('answer_quality_score', 0):.1%}")
        print(f"📈 Completion: {quality.get('completion_percentage', 0):.1f}%")
        
        print("\n" + "🚀 PHASE 1 ENTERPRISE ENHANCEMENTS:")
        print(f"   📊 Vector Knowledge Documents Used: {phase1.get('vector_knowledge_used', 0)}")
        print(f"   🧠 Query Optimization Applied: {'✅' if phase1.get('query_optimization_applied') else '❌'}")
        print(f"   📈 Real-time Monitoring: {'✅' if phase1.get('real_time_monitoring') else '❌'}")
        print(f"   💯 System Health Score: {phase1.get('system_health_score', 0):.1f}/100")
        
        final_answer = result.get("final_answer", "")
        if final_answer:
            print(f"\n💡 FINAL ANSWER:")
            print("-" * 60)
            print(final_answer)
            print("-" * 60)
        
        print("\n" + "=" * 80)

    async def enhanced_collect_training_example(self,
                                              session_id: str,
                                              original_query: str,
                                              workflow_result: Dict[str, Any],
                                              training_data_collector,
                                              config_data: Optional[Dict[str, Any]] = None) -> str:
        """Enhanced training data collection with automated evaluation."""
        
        # Initialize evaluation pipeline if enabled
        evaluation_pipeline = None
        quality_gates = None
        
        config_data = config_data or {}
        if config_data.get('evaluation', {}).get('enabled', False):
            print("🔍 Running automated evaluation...")
            
            try:
                # Initialize evaluation pipeline
                evaluation_pipeline = AutomatedEvaluationPipeline(config_data)
                
                # Initialize quality gates
                thresholds = QualityThresholds.from_config(config_data)
                quality_gates = QualityGateSystem(thresholds, config_data)
                
                # Run comprehensive evaluation
                evaluation_results = await evaluation_pipeline.evaluate_comprehensive(
                    question=original_query,
                    answer=workflow_result.get('final_answer', ''),
                    context={
                        'instructions': 'Provide comprehensive analysis',
                        'available_tools': workflow_result.get('tools_used', []),
                        'workflow_type': 'MainWorkflow'
                    },
                    tools_used=workflow_result.get('tools_used', [])
                )
                
                print(f"   📊 Evaluation complete: {evaluation_results.composite_score:.3f}")
                
                # Run quality gates if enabled
                quality_gate_results = None
                if config_data.get('evaluation', {}).get('quality_gates', {}).get('enabled', False):
                    quality_gate_results = await quality_gates.evaluate_quality_gates(
                        evaluation_results.to_dict(),
                        {'session_id': session_id, 'query': original_query}
                    )
                    
                    print(f"   🚪 Quality gates: {len(quality_gate_results.passed_gates)} passed, {len(quality_gate_results.failed_gates)} failed")
                
                # Add evaluation results to workflow result
                workflow_result.update({
                    'evaluation_results': evaluation_results.to_dict(),
                    'quality_gate_results': quality_gate_results.to_dict() if quality_gate_results else {},
                    'answer_quality_score': evaluation_results.composite_score
                })
                
            except Exception as e:
                print(f"   ⚠️ Evaluation pipeline failed: {e}")
                # Continue with training data collection even if evaluation fails
        
        # Collect training example with enhanced metrics
        try:
            example_id = await training_data_collector.collect_training_example(
                session_id=session_id,
                original_query=original_query,
                optimized_query=workflow_result.get('optimized_query', original_query),
                workflow_result=workflow_result,
                context={
                    'evaluation_results': workflow_result.get('evaluation_results', {}),
                    'quality_gate_results': workflow_result.get('quality_gate_results', {}),
                    'tools_used': workflow_result.get('tools_used', []),
                    'workflow_type': 'MainWorkflow'
                },
                quality_metrics={
                    'answer_quality_score': workflow_result.get('answer_quality_score', 0.0),
                    'relevance_score': workflow_result.get('evaluation_results', {}).get('relevance_score', 0.0),
                    'coherence_score': workflow_result.get('evaluation_results', {}).get('coherence_score', 0.0)
                }
            )
            
            return example_id
            
        except Exception as e:
            print(f"   ⚠️ Enhanced training data collection failed: {e}")
            raise e

    def save_result(self, result: Dict[str, Any], filename: Optional[str] = None) -> str:
        """Save the workflow result to a JSON file."""
        import json
        from datetime import datetime
        
        def json_serializer(obj):
            """JSON serializer for objects not serializable by default json code"""
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"workflow_result_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=json_serializer)
            
            print(f"✅ Results saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Failed to save results: {e}")
            return ""


async def main():
    """Main CLI interface for the multi-agent system with enhanced flow support."""
    args = parse_arguments()
    config_data = load_config_file()
    

    
    # Determine if enhanced flow should be used
    use_enhanced_flow = (
        args.enhanced_flow or 
        config_data.get('ENHANCED_FLOW', False) or 
        config_data.get('enhanced_flow', {}).get('enabled', False)
    )
    
    print("🤖 Multi-Agent Question Answering System")
    print("Powered by CrewAI 2025 Flows + DSPy Intelligence")
    print("=" * 60)
    
    # Get query
    query = args.query
    if not query:
        query = input("\n📝 Enter your question: ").strip()
        if not query:
            query = "What are the latest developments in renewable energy technology?"
            print(f"Using default question: {query}")
    
    if not query.strip():
        print("❌ No query provided")
        return
    
    try:
        print(f"🎯 Query: {query}")
        print(f"🔧 Enhanced Flow: {'Yes' if use_enhanced_flow else 'No'}")
        print("-" * 60)
        
        result = await run_workflow(query, use_enhanced_flow, config_data)
        
        print("\n" + "=" * 60)
        print("🎉 FINAL RESULT")
        print("=" * 60)
        print(result)
        
        # Save to file if specified
        if args.output:
            with open(args.output, 'w') as f:
                f.write(result)
            print(f"\n💾 Result saved to {args.output}")
        
        return result
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Workflow interrupted by user")
        return None
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        return None


# Programmatic API functions
async def answer_question(question: str, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Programmatic API for answering questions.
    
    Args:
        question: The question to answer
        config: Optional configuration parameters
        
    Returns:
        Complete workflow result dictionary
    """
    system = MultiAgentSystem()
    return await system.answer_question(question, config)


async def quick_answer(question: str) -> str:
    """
    Quick API that returns just the final answer string.
    
    Args:
        question: The question to answer
        
    Returns:
        Final answer string
    """
    result = await answer_question(question)
    if result.get("success"):
        return result.get("final_answer", "No answer generated")
    else:
        return f"Error: {result.get('error', 'Unknown error')}"


# Batch processing function
async def answer_multiple_questions(questions: list, config: Optional[Dict[str, Any]] = None) -> list:
    """
    Answer multiple questions in parallel.
    
    Args:
        questions: List of questions to answer
        config: Optional configuration parameters
        
    Returns:
        List of workflow results
    """
    print(f"🔄 Processing {len(questions)} questions in parallel...")
    
    tasks = [answer_question(q, config) for q in questions]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Process results
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            processed_results.append({
                "success": False,
                "question": questions[i],
                "error": str(result)
            })
        else:
            processed_results.append(result)
    
    print(f"✅ Completed processing {len(questions)} questions")
    return processed_results


# Development and testing functions
async def test_system():
    """Test the enhanced system with comprehensive capabilities."""
    print("🧪 Testing Enhanced Multi-Agent System with Phase 1 Enhancements...")
    
    test_questions = [
        "What is the golden ratio and how is it used in nature?",
        "Compare renewable energy sources vs fossil fuels",
        "Explain how machine learning algorithms work"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🧪 Test {i}: {question}")
        result = await answer_question(question)
        print(f"✅ Success: {result.get('success', False)}")
        print(f"🎯 Answer: {result.get('final_answer', 'No answer')[:100]}...")
        
        # Simulate user feedback (for testing)
        if result.get('training_example_id'):
            print(f"   📚 Simulating positive feedback for training example {result['training_example_id'][:8]}...")
            await add_feedback(
                example_id=result['training_example_id'],
                feedback_type='positive',
                feedback_score=0.9,
                comments="Excellent comprehensive answer with good examples"
            )


async def add_feedback(example_id: str, 
                      feedback_type: str, 
                      feedback_score: Optional[float] = None,
                      comments: Optional[str] = None) -> bool:
    """
    Add user feedback to a training example for continuous learning.
    
    Args:
        example_id: The ID of the training example
        feedback_type: 'positive', 'negative', or 'quality_score'
        feedback_score: Numeric score (0.0 to 1.0)
        comments: Optional feedback comments
        
    Returns:
        bool: Success status
    """
    try:
        training_data_collector = get_training_data_collector()
        
        # Convert string to enum
        from src.optimization.dspy.training_data_collector import FeedbackType
        
        if feedback_type.lower() == 'positive':
            feedback_enum = FeedbackType.EXPLICIT_POSITIVE
            feedback_score = feedback_score or 1.0
        elif feedback_type.lower() == 'negative':
            feedback_enum = FeedbackType.EXPLICIT_NEGATIVE
            feedback_score = feedback_score or 0.0
        elif feedback_type.lower() == 'quality_score':
            feedback_enum = FeedbackType.QUALITY_SCORE
        else:
            feedback_enum = FeedbackType.QUALITY_SCORE
        
        await training_data_collector.add_feedback(
            example_id=example_id,
            feedback_type=feedback_enum,
            feedback_score=feedback_score,
            comments=comments
        )
        
        print(f"✅ Feedback added for example {example_id[:8]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed to add feedback: {e}")
        return False


async def get_training_stats() -> Dict[str, Any]:
    """Get current training dataset statistics."""
    try:
        training_data_collector = get_training_data_collector()
        return await training_data_collector.get_dataset_stats()
    except Exception as e:
        print(f"❌ Failed to get training stats: {e}")
        return {}


if __name__ == "__main__":
    # Check for special commands
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test":
            asyncio.run(test_system())
        elif sys.argv[1] == "--help":
            print("""
Multi-Agent Question Answering System

Usage:
    python main.py                          # Interactive mode
    python main.py "Your question here"     # Direct question
    python main.py --test                   # Run system tests
    python main.py --help                   # Show this help

Features:
    • Hierarchical multi-agent architecture
    • Parallel specialist execution (Research, Library, Analysis)
    • CrewAI 2025 Flows orchestration
    • DSPy intelligence optimization
    • Comprehensive quality validation
    • Professional result formatting
            """)
        else:
            # Treat as question
            asyncio.run(main())
    else:
        # Interactive mode
        asyncio.run(main()) 