"""
ReAct Knowledge Specialist with reasoning and computational capabilities.

Implements step-by-step reasoning with knowledge synthesis and analysis
using DSPy's ReAct pattern for complex analytical tasks.
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime

import dspy
from dspy import ReAct, ChainOfThought, PythonInterpreter

from ..base.base_agent import BaseSpecialistAgent
from ...core.interfaces.task_interface import ITask, TaskResult, TaskStatus, TaskType
from ...tools.crewai_tools_integration import professional_tools


class ReActKnowledgeSpecialist(dspy.Module):
    """
    ReAct-enabled knowledge specialist with computational and tool capabilities.
    
    Features:
    - ReAct reasoning with knowledge synthesis
    - Python code execution for calculations
    - Professional tool integration
    - Multi-modal problem solving
    """
    
    def __init__(self, config=None, tools=None, **kwargs):
        super().__init__()
        
        # Store configuration
        self.config = config
        self._agent_id = str(uuid.uuid4())
        
        # Initialize computational capabilities
        self.calculator = PythonInterpreter({})
        
        # Setup tools for ReAct agent
        self.available_tools = self._setup_react_tools()
        
        # Create ReAct agent with comprehensive tools
        self.react_agent = ReAct(
            signature="question -> answer: str, confidence: float, methodology: str, sources: list[str]",
            tools=list(self.available_tools.values()),
            max_iters=10  # Allow more iterations for complex knowledge tasks
        )
        
        # Specialized predictors
        self.knowledge_synthesizer = ChainOfThought(
            "information_pieces -> synthesis: str, key_insights: list[str], confidence: float"
        )
    
    def forward(self, question: str, context: str = "") -> dspy.Prediction:
        """
        DSPy forward method for ReAct knowledge processing.
        
        Args:
            question: The knowledge question
            context: Additional context
            
        Returns:
            DSPy Prediction with knowledge synthesis results
        """
        try:
            # Prepare comprehensive question
            full_question = f"{question}"
            if context:
                full_question += f"\n\nContext: {context}"
            
            # Execute ReAct agent
            react_result = self.react_agent(question=full_question)
            
            return dspy.Prediction(
                answer=react_result.answer,
                confidence=getattr(react_result, 'confidence', 0.8),
                methodology=getattr(react_result, 'methodology', 'ReAct reasoning'),
                sources=getattr(react_result, 'sources', []),
                reasoning=getattr(react_result, 'reasoning', ''),
                tools_available=list(self.available_tools.keys()),
                capabilities=[
                    "Mathematical computation",
                    "Knowledge research", 
                    "Document analysis",
                    "Fact verification",
                    "Code generation",
                    "Information synthesis"
                ],
                method="react_knowledge_processing",
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            return dspy.Prediction(
                answer=f"ReAct knowledge processing failed: {str(e)}",
                confidence=0.0,
                methodology="error_fallback",
                sources=[],
                error=str(e),
                method="error_handling"
            )

    async def execute_task(self, task: ITask) -> TaskResult:
        """Execute knowledge task using ReAct with comprehensive capabilities."""
        try:
            query = task.spec.description
            context = task.spec.context.get('content', '') if task.spec.context else ''
            
            # Use the forward method for processing
            result = self.forward(question=query, context=context)
            
            # Format comprehensive result
            output = {
                "original_query": query,
                "answer": result.answer,
                "confidence": getattr(result, 'confidence', 0.8),
                "methodology": getattr(result, 'methodology', 'ReAct reasoning'),
                "sources": getattr(result, 'sources', []),
                "reasoning": getattr(result, 'reasoning', ''),
                "tools_available": getattr(result, 'tools_available', list(self.available_tools.keys())),
                "capabilities": getattr(result, 'capabilities', []),
                "method": getattr(result, 'method', 'react_knowledge_processing'),
                "timestamp": getattr(result, 'timestamp', datetime.now().isoformat())
            }
            
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.COMPLETED,
                output=output,
                agent_id=self._agent_id
            )
            
        except Exception as e:
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.FAILED,
                error=f"ReAct knowledge processing failed: {str(e)}",
                agent_id=self._agent_id
            )
    
    def _setup_react_tools(self) -> Dict[str, Callable]:
        """Setup comprehensive tools for ReAct agent."""
        
        def mathematical_computation_tool(expression: str) -> str:
            """Execute mathematical computations and code."""
            try:
                # First try as direct mathematical expression
                result = self.calculator.execute(expression)
                return f"Computation result: {result}"
            except Exception as e:
                return f"Computation failed: {str(e)}. Consider reformulating the expression."
        
        def knowledge_research_tool(topic: str) -> str:
            """Research a topic using web search."""
            try:
                web_tool = professional_tools.available_tools['web_search']
                result = web_tool._run(f"comprehensive information about {topic}")
                return f"Research on '{topic}': {result}"
            except Exception as e:
                return f"Research failed: {str(e)}"
        
        def document_analysis_tool(content: str) -> str:
            """Analyze document content for key insights."""
            analyzer = ChainOfThought(
                "content -> main_topics: list[str], key_insights: list[str], summary: str"
            )
            
            try:
                result = analyzer(content=content[:2000])  # Limit content length
                return f"Document analysis: Topics: {result.main_topics}, Insights: {result.key_insights}, Summary: {result.summary}"
            except Exception as e:
                return f"Document analysis failed: {str(e)}"
        
        def fact_verification_tool(claim: str) -> str:
            """Verify factual claims using multiple approaches."""
            try:
                # Use web search for verification
                web_tool = professional_tools.available_tools['web_search']
                search_result = web_tool._run(f"verify fact: {claim}")
                
                # Use reasoning for analysis
                verifier = ChainOfThought(
                    "claim, evidence -> verification_status: str, confidence: float, reasoning: str"
                )
                verification = verifier(claim=claim, evidence=search_result)
                
                return f"Fact verification for '{claim}': Status: {verification.verification_status}, Confidence: {verification.confidence}, Reasoning: {verification.reasoning}"
            except Exception as e:
                return f"Fact verification failed: {str(e)}"
        
        def knowledge_synthesis_tool(information_list: str) -> str:
            """Synthesize multiple pieces of information."""
            try:
                synthesis = self.knowledge_synthesizer(information_pieces=information_list)
                return f"Knowledge synthesis: {synthesis.synthesis}, Key insights: {synthesis.key_insights}, Confidence: {synthesis.confidence}"
            except Exception as e:
                return f"Knowledge synthesis failed: {str(e)}"
        
        def code_generation_tool(problem_description: str) -> str:
            """Generate and execute code for problem solving."""
            try:
                code_generator = ChainOfThought(
                    "problem -> python_code: str, explanation: str"
                )
                
                code_result = code_generator(problem=problem_description)
                
                # Execute the generated code
                execution_result = self.calculator.execute(code_result.python_code)
                
                return f"Code solution: {code_result.explanation}\nCode: {code_result.python_code}\nResult: {execution_result}"
            except Exception as e:
                return f"Code generation failed: {str(e)}"
        
        return {
            'mathematical_computation': mathematical_computation_tool,
            'knowledge_research': knowledge_research_tool,
            'document_analysis': document_analysis_tool,
            'fact_verification': fact_verification_tool,
            'knowledge_synthesis': knowledge_synthesis_tool,
            'code_generation': code_generation_tool
        }
    
    def _get_supported_task_types(self) -> List[TaskType]:
        """Return supported task types."""
        return [TaskType.ANALYSIS, TaskType.RESEARCH]
    
    async def solve_complex_problem(self, problem: str) -> Dict[str, Any]:
        """
        Solve complex problems using all available capabilities.
        
        Args:
            problem: Complex problem description
            
        Returns:
            Comprehensive solution with reasoning
        """
        result = self.react_agent(question=f"Solve this complex problem step by step: {problem}")
        
        return {
            "problem": problem,
            "solution": result.answer,
            "confidence": getattr(result, 'confidence', 0.8),
            "methodology": getattr(result, 'methodology', 'ReAct problem solving'),
            "reasoning": getattr(result, 'reasoning', ''),
            "tools_used": "All available ReAct tools",
            "problem_type": "Complex multi-step problem"
        }
    
    async def verify_and_synthesize(self, claims: List[str]) -> Dict[str, Any]:
        """
        Verify multiple claims and synthesize findings.
        
        Args:
            claims: List of claims to verify
            
        Returns:
            Verification results and synthesis
        """
        verification_query = f"Verify and synthesize these claims: {', '.join(claims)}"
        result = self.react_agent(question=verification_query)
        
        return {
            "original_claims": claims,
            "verification_synthesis": result.answer,
            "confidence": getattr(result, 'confidence', 0.8),
            "sources": getattr(result, 'sources', []),
            "methodology": "Multi-claim verification and synthesis",
            "reasoning": getattr(result, 'reasoning', '')
        } 