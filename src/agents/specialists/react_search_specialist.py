"""
ReAct Search Specialist with reasoning and action capabilities.

Implements step-by-step reasoning with tool usage for complex search
and information gathering tasks using DSPy's ReAct pattern.
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime

import dspy
from dspy import ReAct, ChainOfThought

from ..base.base_agent import BaseSpecialistAgent
from ...core.interfaces.task_interface import <PERSON>ask, TaskResult, TaskStatus, TaskType
from ...tools.crewai_tools_integration import professional_tools


class ReActSearchSpecialist(dspy.Module):
    """
    ReAct-enabled search specialist with tool integration.
    
    Features:
    - ReAct reasoning + action capabilities
    - Professional tool integration
    - Step-by-step problem solving
    - Automatic tool selection and usage
    """
    
    def __init__(self, config=None, tools=None, **kwargs):
        super().__init__()
        
        # Store configuration
        self.config = config
        self._agent_id = str(uuid.uuid4())
        
        # Define available tools for ReAct agent
        self.available_tools = self._setup_react_tools()
        
        # Create ReAct agent with tools
        self.react_agent = ReAct(
            signature="question -> answer: str, confidence: float, reasoning_steps: list[str]",
            tools=list(self.available_tools.values()),
            max_iters=8  # Allow up to 8 reasoning/action cycles
        )
        
        # Fallback predictor for complex queries
        self.fallback_predictor = ChainOfThought(
            "question, context -> answer: str, confidence: float, approach: str"
        )
    
    def _setup_react_tools(self) -> Dict[str, Callable]:
        """Setup tools for ReAct agent."""
        
        def web_search_tool(query: str) -> str:
            """Search the web for information using professional search."""
            try:
                web_tool = professional_tools.available_tools['web_search']
                # Use the tool's run method
                result = web_tool._run(query)
                return f"Web search results for '{query}': {result}"
            except Exception as e:
                return f"Web search failed: {str(e)}"
        
        def website_analysis_tool(url: str) -> str:
            """Analyze content from a specific website."""
            try:
                website_tool = professional_tools.available_tools['website_search']
                result = website_tool._run(url)
                return f"Website analysis for '{url}': {result}"
            except Exception as e:
                return f"Website analysis failed: {str(e)}"
        
        def knowledge_synthesis_tool(topic: str) -> str:
            """Synthesize knowledge about a specific topic."""
            # Use chain of thought for knowledge synthesis
            synthesizer = ChainOfThought("topic -> key_points: list[str], summary: str, reliability: float")
            result = synthesizer(topic=topic)
            
            return f"Knowledge synthesis for '{topic}': Summary: {result.summary}, Key points: {result.key_points}, Reliability: {result.reliability}"
        
        def query_expansion_tool(original_query: str) -> str:
            """Expand and improve search queries."""
            expander = ChainOfThought("original_query -> expanded_queries: list[str], search_strategy: str")
            result = expander(original_query=original_query)
            
            return f"Query expansion: Original: '{original_query}', Expanded: {result.expanded_queries}, Strategy: {result.search_strategy}"
        
        return {
            'web_search': web_search_tool,
            'website_analysis': website_analysis_tool,
            'knowledge_synthesis': knowledge_synthesis_tool,
            'query_expansion': query_expansion_tool
        }
    
    def forward(self, question: str, context: str = "") -> dspy.Prediction:
        """
        DSPy forward method for ReAct search processing.
        
        Args:
            question: The search question
            context: Additional context
            
        Returns:
            DSPy Prediction with search results
        """
        try:
            # Combine question and context
            full_question = f"{question}"
            if context:
                full_question += f"\n\nAdditional context: {context}"
            
            # Execute ReAct agent
            react_result = self.react_agent(question=full_question)
            
            # Extract reasoning steps if available
            reasoning_steps = []
            if hasattr(react_result, 'reasoning_steps'):
                reasoning_steps = react_result.reasoning_steps
            elif hasattr(react_result, 'reasoning'):
                # If no explicit steps, extract from reasoning
                reasoning_text = react_result.reasoning
                reasoning_steps = reasoning_text.split('\n') if reasoning_text else []
            
            return dspy.Prediction(
                answer=react_result.answer,
                confidence=getattr(react_result, 'confidence', 0.8),
                reasoning_steps=reasoning_steps,
                method="react_reasoning_and_action",
                tools_used=list(self.available_tools.keys()),
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            # Fallback to regular reasoning if ReAct fails
            try:
                fallback_result = self.fallback_predictor(
                    question=question,
                    context=context
                )
                
                return dspy.Prediction(
                    answer=fallback_result.answer,
                    confidence=fallback_result.confidence,
                    approach=fallback_result.approach,
                    method="fallback_reasoning",
                    react_error=str(e),
                    timestamp=datetime.now().isoformat()
                )
                
            except Exception as fallback_error:
                return dspy.Prediction(
                    answer=f"ReAct search failed: {str(e)}, Fallback failed: {str(fallback_error)}",
                    confidence=0.0,
                    method="error_fallback",
                    error=str(e)
                )

    async def execute_task(self, task: ITask) -> TaskResult:
        """Execute search task using ReAct reasoning and tools."""
        try:
            query = task.spec.description
            context = task.spec.context.get('content', '') if task.spec.context else ''
            
            # Use the forward method for processing
            result = self.forward(question=query, context=context)
            
            # Format result for TaskResult
            output = {
                "original_query": query,
                "answer": result.answer,
                "confidence": getattr(result, 'confidence', 0.8),
                "reasoning_steps": getattr(result, 'reasoning_steps', []),
                "method": getattr(result, 'method', 'react_reasoning_and_action'),
                "tools_used": getattr(result, 'tools_used', list(self.available_tools.keys())),
                "timestamp": getattr(result, 'timestamp', datetime.now().isoformat()),
                "metadata": {
                    "max_iterations": 8,
                    "agent_type": "ReAct",
                    "reasoning_enabled": True
                }
            }
            
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.COMPLETED,
                output=output,
                agent_id=self._agent_id
            )
            
        except Exception as e:
            return TaskResult(
                task_id=task.spec.task_id,
                task_type=task.spec.task_type,
                status=TaskStatus.FAILED,
                error=f"ReAct search failed: {str(e)}",
                agent_id=self._agent_id
            )
    
    def _get_supported_task_types(self) -> List[TaskType]:
        """Return supported task types."""
        return [TaskType.RESEARCH, TaskType.ANALYSIS]
    
    async def search_with_reasoning(self, query: str, max_steps: int = 6) -> Dict[str, Any]:
        """
        Direct method for search with step-by-step reasoning.
        
        Args:
            query: Search query
            max_steps: Maximum reasoning steps
            
        Returns:
            Search results with detailed reasoning
        """
        # Create a temporary ReAct agent with custom max_iters
        temp_agent = ReAct(
            signature="question -> answer: str, confidence: float, reasoning_steps: list[str]",
            tools=list(self.available_tools.values()),
            max_iters=max_steps
        )
        
        result = temp_agent(question=query)
        
        return {
            "query": query,
            "answer": result.answer,
            "confidence": getattr(result, 'confidence', 0.8),
            "reasoning_steps": getattr(result, 'reasoning_steps', []),
            "reasoning": getattr(result, 'reasoning', ''),
            "max_steps_used": max_steps
        }
    
    async def demonstrate_reasoning(self, query: str) -> Dict[str, Any]:
        """
        Demonstrate the reasoning process for educational purposes.
        
        Args:
            query: Query to demonstrate reasoning for
            
        Returns:
            Detailed breakdown of reasoning process
        """
        result = await self.search_with_reasoning(query, max_steps=5)
        
        # Add educational breakdown
        result["educational_breakdown"] = {
            "what_is_react": "ReAct combines Reasoning and Acting - the agent thinks step by step and uses tools",
            "reasoning_process": "Each step involves: Think -> Act (use tool) -> Observe (see result) -> Think again",
            "tools_available": list(self.available_tools.keys()),
            "benefits": [
                "Step-by-step problem solving",
                "Tool integration",
                "Transparent reasoning",
                "Better accuracy through iteration"
            ]
        }
        
        return result 