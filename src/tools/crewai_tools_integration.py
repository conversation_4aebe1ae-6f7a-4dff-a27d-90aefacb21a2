"""
CrewAI Built-in Tools Integration.

Provides instant access to professional-grade tools including
web search, website scraping, file operations, and code documentation.
"""

from typing import List, Dict, Any, Optional
from crewai_tools import (
    SerperDevTool,      # Professional Google search
    WebsiteSearchTool,  # Website content extraction
    FileReadTool,       # File system access
    DirectoryReadTool,  # Directory exploration
    CodeDocsSearchTool, # Code documentation search
)
from crewai import Agent


class ProfessionalToolsManager:
    """
    Manager for CrewAI's professional built-in tools.
    
    Provides instant access to enterprise-grade capabilities
    with zero development time.
    """
    
    def __init__(self):
        self.available_tools = {
            'web_search': SerperDevTool(),
            'website_search': WebsiteSearchTool(),
            'file_reader': FileReadTool(),
            'directory_reader': DirectoryReadTool(),
            'code_docs_search': CodeDocsSearchTool()
        }
        
        self.tool_descriptions = {
            'web_search': 'Professional Google search with comprehensive results',
            'website_search': 'Extract content from specific websites',
            'file_reader': 'Read and analyze file contents',
            'directory_reader': 'Explore directory structures',
            'code_docs_search': 'Search code documentation and repositories'
        }
    
    def get_all_tools(self) -> List:
        """Get all available professional tools."""
        return list(self.available_tools.values())
    
    def get_tools_by_category(self, category: str) -> List:
        """
        Get tools by category.
        
        Categories:
        - 'search': Web and content search tools
        - 'file': File system tools
        - 'code': Code-related tools
        """
        category_mapping = {
            'search': ['web_search', 'website_search'],
            'file': ['file_reader', 'directory_reader'],
            'code': ['code_docs_search'],
            'all': list(self.available_tools.keys())
        }
        
        tool_names = category_mapping.get(category, [])
        return [self.available_tools[name] for name in tool_names if name in self.available_tools]
    
    def create_enhanced_agent(self, 
                            role: str, 
                            goal: str, 
                            backstory: str,
                            tool_categories: List[str] = None,
                            **agent_kwargs) -> Agent:
        """
        Create a CrewAI agent with professional tools.
        
        Args:
            role: Agent role
            goal: Agent goal
            backstory: Agent backstory
            tool_categories: Categories of tools to include ('search', 'file', 'code', 'all')
            **agent_kwargs: Additional agent parameters
            
        Returns:
            Enhanced CrewAI agent with professional tools
        """
        if tool_categories is None:
            tool_categories = ['all']
        
        # Collect tools from specified categories
        tools = []
        for category in tool_categories:
            tools.extend(self.get_tools_by_category(category))
        
        # Remove duplicates while preserving order
        seen = set()
        unique_tools = []
        for tool in tools:
            tool_id = id(tool)
            if tool_id not in seen:
                seen.add(tool_id)
                unique_tools.append(tool)
        
        return Agent(
            role=role,
            goal=goal,
            backstory=backstory,
            tools=unique_tools,
            verbose=True,
            allow_delegation=False,
            **agent_kwargs
        )
    
    def upgrade_existing_agent(self, agent: Agent, tool_categories: List[str] = None) -> Agent:
        """
        Add professional tools to an existing CrewAI agent.
        
        Args:
            agent: Existing CrewAI agent
            tool_categories: Categories of tools to add
            
        Returns:
            Agent with added professional tools
        """
        if tool_categories is None:
            tool_categories = ['all']
        
        # Get tools to add
        new_tools = []
        for category in tool_categories:
            new_tools.extend(self.get_tools_by_category(category))
        
        # Add to existing tools (avoid duplicates)
        existing_tool_types = {type(tool) for tool in agent.tools}
        for tool in new_tools:
            if type(tool) not in existing_tool_types:
                agent.tools.append(tool)
        
        return agent
    
    def get_tool_capabilities(self) -> Dict[str, str]:
        """Get description of all tool capabilities."""
        return self.tool_descriptions.copy()


# Convenience functions for immediate use
def create_research_agent(role: str = "Research Specialist",
                         goal: str = "Conduct comprehensive research using professional tools",
                         backstory: str = "Expert researcher with access to professional-grade tools") -> Agent:
    """Create a research agent with web search and content tools."""
    manager = ProfessionalToolsManager()
    return manager.create_enhanced_agent(
        role=role,
        goal=goal, 
        backstory=backstory,
        tool_categories=['search']
    )


def create_analyst_agent(role: str = "Data Analyst",
                        goal: str = "Analyze data and documents using professional tools",
                        backstory: str = "Expert analyst with file system and search capabilities") -> Agent:
    """Create an analyst agent with file and search tools."""
    manager = ProfessionalToolsManager()
    return manager.create_enhanced_agent(
        role=role,
        goal=goal,
        backstory=backstory,
        tool_categories=['file', 'search']
    )


def create_developer_agent(role: str = "Development Assistant", 
                          goal: str = "Assist with code analysis and documentation",
                          backstory: str = "Expert developer with code documentation and file system access") -> Agent:
    """Create a developer agent with code and file tools."""
    manager = ProfessionalToolsManager()
    return manager.create_enhanced_agent(
        role=role,
        goal=goal,
        backstory=backstory,
        tool_categories=['code', 'file']
    )


def create_super_agent(role: str, goal: str, backstory: str, **kwargs) -> Agent:
    """Create an agent with ALL professional tools."""
    manager = ProfessionalToolsManager()
    return manager.create_enhanced_agent(
        role=role,
        goal=goal,
        backstory=backstory,
        tool_categories=['all'],
        **kwargs
    )


# Global manager instance for convenience
professional_tools = ProfessionalToolsManager() 