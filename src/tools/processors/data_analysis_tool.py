from typing import Type, Optional, Dict, List, Any
import json
import re
from collections import Counter, defaultdict
from datetime import datetime
import statistics
from pydantic import BaseModel, Field
from crewai.tools import BaseTool


class DataAnalysisInput(BaseModel):
    """Input schema for DataAnalysisTool."""
    data: str = Field(..., description="Data to analyze (text, JSON, or structured content)")
    analysis_type: str = Field(default="summary", description="Type of analysis: 'summary', 'statistics', 'patterns', 'keywords', 'sentiment_basic'")
    output_format: str = Field(default="text", description="Output format: 'text' or 'json'")


class DataAnalysisTool(BaseTool):
    name: str = "DataAnalysisTool"
    description: str = "Analyze text data to extract insights, statistics, patterns, keywords, and basic sentiment. Supports various analysis types and output formats."
    args_schema: Type[BaseModel] = DataAnalysisInput
    
    def _run(self, data: str, analysis_type: str = "summary", output_format: str = "text") -> str:
        """
        Analyze the provided data and return insights.
        
        Args:
            data: The data to analyze
            analysis_type: Type of analysis to perform
            output_format: Format for the output
            
        Returns:
            Analysis results as formatted text or JSON
        """
        try:
            if not data or not data.strip():
                return "Error: No data provided for analysis"
            
            # Clean and prepare data
            cleaned_data = self._clean_data(data)
            
            # Perform analysis based on type
            if analysis_type == "summary":
                result = self._analyze_summary(cleaned_data)
            elif analysis_type == "statistics":
                result = self._analyze_statistics(cleaned_data)
            elif analysis_type == "patterns":
                result = self._analyze_patterns(cleaned_data)
            elif analysis_type == "keywords":
                result = self._analyze_keywords(cleaned_data)
            elif analysis_type == "sentiment_basic":
                result = self._analyze_basic_sentiment(cleaned_data)
            else:
                result = {"error": f"Unknown analysis type: {analysis_type}"}
            
            # Format output
            if output_format == "json":
                return json.dumps(result, indent=2)
            else:
                return self._format_as_text(result, analysis_type)
                
        except Exception as e:
            return f"Error during data analysis: {str(e)}"
    
    def _clean_data(self, data: str) -> str:
        """Clean and normalize the input data."""
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', data.strip())
        # Remove special characters that might interfere with analysis
        cleaned = re.sub(r'[^\w\s\.\,\!\?\-\'\"]', ' ', cleaned)
        return cleaned
    
    def _analyze_summary(self, data: str) -> Dict[str, Any]:
        """Perform summary analysis of the data."""
        words = data.split()
        sentences = re.split(r'[.!?]+', data)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # Basic text statistics
        char_count = len(data)
        word_count = len(words)
        sentence_count = len(sentences)
        
        # Average calculations
        avg_words_per_sentence = word_count / sentence_count if sentence_count > 0 else 0
        avg_chars_per_word = char_count / word_count if word_count > 0 else 0
        
        # Find longest sentence
        longest_sentence = max(sentences, key=len) if sentences else ""
        
        # Readability estimate (simplified)
        readability_score = self._calculate_readability(word_count, sentence_count, data)
        
        return {
            "total_characters": char_count,
            "total_words": word_count,
            "total_sentences": sentence_count,
            "avg_words_per_sentence": round(avg_words_per_sentence, 2),
            "avg_chars_per_word": round(avg_chars_per_word, 2),
            "longest_sentence": longest_sentence[:200] + "..." if len(longest_sentence) > 200 else longest_sentence,
            "readability_score": readability_score
        }
    
    def _analyze_statistics(self, data: str) -> Dict[str, Any]:
        """Perform statistical analysis of the data."""
        words = [word.lower().strip('.,!?";()') for word in data.split()]
        word_lengths = [len(word) for word in words if word]
        
        # Word frequency
        word_freq = Counter(words)
        most_common = word_freq.most_common(10)
        
        # Length statistics
        stats = {}
        if word_lengths:
            stats = {
                "min_word_length": min(word_lengths),
                "max_word_length": max(word_lengths),
                "avg_word_length": round(statistics.mean(word_lengths), 2),
                "median_word_length": statistics.median(word_lengths)
            }
        
        # Character analysis
        char_freq = Counter(char.lower() for char in data if char.isalnum())
        
        return {
            "word_frequency": dict(most_common),
            "unique_words": len(set(words)),
            "word_length_stats": stats,
            "most_common_characters": dict(char_freq.most_common(10)),
            "vocabulary_diversity": round(len(set(words)) / len(words), 3) if words else 0
        }
    
    def _analyze_patterns(self, data: str) -> Dict[str, Any]:
        """Analyze patterns in the data."""
        # Email pattern
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', data)
        
        # URL pattern
        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', data)
        
        # Phone number pattern (basic)
        phones = re.findall(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', data)
        
        # Number patterns
        numbers = re.findall(r'\b\d+\.?\d*\b', data)
        
        # Date patterns (basic)
        dates = re.findall(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b', data)
        
        # Repeated phrases (3+ words that appear multiple times)
        words = data.lower().split()
        phrase_counts = defaultdict(int)
        for i in range(len(words) - 2):
            phrase = ' '.join(words[i:i+3])
            phrase_counts[phrase] += 1
        
        repeated_phrases = {phrase: count for phrase, count in phrase_counts.items() if count > 1}
        
        return {
            "emails_found": emails[:5],  # Limit output
            "urls_found": urls[:5],
            "phone_numbers": phones[:5],
            "numbers_found": len(numbers),
            "dates_found": dates[:5],
            "repeated_phrases": dict(sorted(repeated_phrases.items(), key=lambda x: x[1], reverse=True)[:10])
        }
    
    def _analyze_keywords(self, data: str) -> Dict[str, Any]:
        """Extract and analyze keywords from the data."""
        words = [word.lower().strip('.,!?";()') for word in data.split()]
        
        # Common stop words to filter out
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does',
            'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that',
            'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her',
            'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
        }
        
        # Filter out stop words and short words
        keywords = [word for word in words if len(word) > 3 and word not in stop_words and word.isalpha()]
        
        # Keyword frequency
        keyword_freq = Counter(keywords)
        top_keywords = keyword_freq.most_common(20)
        
        # Find potential key phrases (2-3 word combinations)
        phrases = []
        for i in range(len(keywords) - 1):
            phrase = ' '.join(keywords[i:i+2])
            phrases.append(phrase)
        
        phrase_freq = Counter(phrases)
        top_phrases = phrase_freq.most_common(10)
        
        return {
            "top_keywords": dict(top_keywords),
            "total_keywords": len(keywords),
            "unique_keywords": len(set(keywords)),
            "top_phrases": dict(top_phrases),
            "keyword_density": round(len(keywords) / len(words), 3) if words else 0
        }
    
    def _analyze_basic_sentiment(self, data: str) -> Dict[str, Any]:
        """Perform basic sentiment analysis using word lists."""
        # Simple positive and negative word lists
        positive_words = {
            'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'perfect',
            'love', 'like', 'enjoy', 'happy', 'pleased', 'satisfied', 'brilliant',
            'awesome', 'outstanding', 'superb', 'magnificent', 'terrific', 'marvelous',
            'success', 'successful', 'win', 'winner', 'victory', 'achieve', 'accomplished'
        }
        
        negative_words = {
            'bad', 'terrible', 'awful', 'horrible', 'disgusting', 'hate', 'dislike',
            'sad', 'angry', 'frustrated', 'disappointed', 'annoyed', 'upset', 'poor',
            'worst', 'fail', 'failure', 'lose', 'loser', 'defeat', 'problem', 'issue',
            'wrong', 'error', 'mistake', 'difficult', 'hard', 'challenging', 'struggle'
        }
        
        words = [word.lower().strip('.,!?";()') for word in data.split()]
        
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        neutral_count = len(words) - positive_count - negative_count
        
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            sentiment_score = 0.0
            sentiment_label = "neutral"
        else:
            sentiment_score = (positive_count - negative_count) / total_sentiment_words
            if sentiment_score > 0.1:
                sentiment_label = "positive"
            elif sentiment_score < -0.1:
                sentiment_label = "negative"
            else:
                sentiment_label = "neutral"
        
        return {
            "sentiment_label": sentiment_label,
            "sentiment_score": round(sentiment_score, 3),
            "positive_words_count": positive_count,
            "negative_words_count": negative_count,
            "neutral_words_count": neutral_count,
            "confidence": round(total_sentiment_words / len(words), 3) if words else 0
        }
    
    def _calculate_readability(self, word_count: int, sentence_count: int, text: str) -> str:
        """Calculate a simplified readability score."""
        if sentence_count == 0 or word_count == 0:
            return "Unable to calculate"
        
        avg_sentence_length = word_count / sentence_count
        
        # Count syllables (simplified)
        syllable_count = 0
        for word in text.split():
            syllable_count += max(1, len(re.findall(r'[aeiouAEIOU]', word)))
        
        avg_syllables_per_word = syllable_count / word_count
        
        # Simplified readability classification
        if avg_sentence_length < 15 and avg_syllables_per_word < 1.5:
            return "Very Easy"
        elif avg_sentence_length < 20 and avg_syllables_per_word < 2:
            return "Easy"
        elif avg_sentence_length < 25 and avg_syllables_per_word < 2.5:
            return "Moderate"
        else:
            return "Difficult"
    
    def _format_as_text(self, result: Dict[str, Any], analysis_type: str) -> str:
        """Format the analysis result as readable text."""
        if "error" in result:
            return f"Analysis Error: {result['error']}"
        
        output = f"=== Data Analysis Results ({analysis_type.title()}) ===\n\n"
        
        for key, value in result.items():
            if isinstance(value, dict):
                output += f"{key.replace('_', ' ').title()}:\n"
                for subkey, subvalue in value.items():
                    output += f"  • {subkey}: {subvalue}\n"
                output += "\n"
            elif isinstance(value, list):
                output += f"{key.replace('_', ' ').title()}: {', '.join(map(str, value[:5]))}\n"
                if len(value) > 5:
                    output += f"  (... and {len(value) - 5} more)\n"
            else:
                output += f"{key.replace('_', ' ').title()}: {value}\n"
        
        return output 