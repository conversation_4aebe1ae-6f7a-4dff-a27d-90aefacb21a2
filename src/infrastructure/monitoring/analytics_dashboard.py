"""
Production Analytics Dashboard for Multi-Agent Question Answering System.

Real-time monitoring dashboard with visualizations, performance analytics,
and operational insights for the multi-agent system.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import json
import logging
import statistics
from pathlib import Path
from dataclasses import dataclass, asdict
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
import seaborn as sns
import pandas as pd
from io import BytesIO
import base64

from .metrics_collector import MetricsCollector, get_metrics_collector, AlertLevel

logger = logging.getLogger(__name__)

# Set style for plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


@dataclass
class DashboardConfig:
    """Configuration for analytics dashboard."""
    refresh_interval: int = 30  # seconds
    default_time_window: int = 24  # hours
    max_data_points: int = 1000
    chart_width: int = 12
    chart_height: int = 6
    export_format: str = "png"
    dpi: int = 100


class AnalyticsDashboard:
    """
    Production Analytics Dashboard for monitoring multi-agent system performance.
    
    Provides real-time visualizations, performance metrics, and operational
    insights for system monitoring and optimization.
    """
    
    def __init__(self, 
                 metrics_collector: Optional[MetricsCollector] = None,
                 config: Optional[DashboardConfig] = None):
        self.metrics_collector = metrics_collector or get_metrics_collector()
        self.config = config or DashboardConfig()
        
        # Dashboard state
        self.last_refresh = datetime.now()
        self.cached_data = {}
        self.chart_cache = {}
        
        logger.info("Analytics Dashboard initialized")
    
    def generate_dashboard_data(self, hours: int = None) -> Dict[str, Any]:
        """Generate comprehensive dashboard data."""
        hours = hours or self.config.default_time_window
        
        # Check cache validity
        cache_key = f"dashboard_{hours}h"
        if self._is_cache_valid(cache_key):
            return self.cached_data[cache_key]
        
        try:
            # Collect all metrics summaries
            all_metrics = self.metrics_collector.get_all_metrics_summary(hours)
            
            # Get active alerts
            active_alerts = self.metrics_collector.get_active_alerts()
            
            # System overview
            system_overview = self._generate_system_overview(all_metrics)
            
            # Agent performance metrics
            agent_metrics = self._extract_agent_metrics(all_metrics)
            
            # Optimization metrics
            optimization_metrics = self._extract_optimization_metrics(all_metrics)
            
            # Performance trends
            performance_trends = self._generate_performance_trends(hours)
            
            # Alert summary
            alert_summary = self._generate_alert_summary(active_alerts)
            
            dashboard_data = {
                "timestamp": datetime.now().isoformat(),
                "time_window_hours": hours,
                "system_overview": system_overview,
                "agent_metrics": agent_metrics,
                "optimization_metrics": optimization_metrics,
                "performance_trends": performance_trends,
                "alert_summary": alert_summary,
                "active_alerts": active_alerts,
                "health_score": self._calculate_health_score(all_metrics, active_alerts)
            }
            
            # Cache the results
            self.cached_data[cache_key] = dashboard_data
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Failed to generate dashboard data: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    def _generate_system_overview(self, all_metrics: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Generate system overview metrics."""
        overview = {
            "cpu_usage": 0.0,
            "memory_usage": 0.0,
            "disk_usage": 0.0,
            "active_processes": 0,
            "request_rate": 0.0,
            "error_rate": 0.0,
            "avg_response_time": 0.0
        }
        
        # Extract system metrics
        if "system.cpu_percent" in all_metrics:
            overview["cpu_usage"] = all_metrics["system.cpu_percent"].get("latest", 0.0)
        
        if "system.memory_percent" in all_metrics:
            overview["memory_usage"] = all_metrics["system.memory_percent"].get("latest", 0.0)
        
        if "system.disk_percent" in all_metrics:
            overview["disk_usage"] = all_metrics["system.disk_percent"].get("latest", 0.0)
        
        if "system.process_count" in all_metrics:
            overview["active_processes"] = all_metrics["system.process_count"].get("latest", 0)
        
        # Calculate derived metrics
        request_metrics = [m for name, m in all_metrics.items() if "request" in name.lower()]
        if request_metrics:
            overview["request_rate"] = sum(m.get("count", 0) for m in request_metrics) / len(request_metrics)
        
        error_metrics = [m for name, m in all_metrics.items() if "error" in name.lower()]
        success_metrics = [m for name, m in all_metrics.items() if "success" in name.lower()]
        if error_metrics and success_metrics:
            total_errors = sum(m.get("count", 0) for m in error_metrics)
            total_success = sum(m.get("count", 0) for m in success_metrics)
            total_requests = total_errors + total_success
            overview["error_rate"] = (total_errors / total_requests * 100) if total_requests > 0 else 0.0
        
        response_time_metrics = [m for name, m in all_metrics.items() if "response_time" in name.lower()]
        if response_time_metrics:
            overview["avg_response_time"] = statistics.mean(
                m.get("mean", 0) for m in response_time_metrics
            )
        
        return overview
    
    def _extract_agent_metrics(self, all_metrics: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Extract agent-specific performance metrics."""
        agent_metrics = {
            "total_agents": 0,
            "active_agents": 0,
            "agent_performance": {},
            "task_completion_rate": 0.0,
            "avg_task_duration": 0.0,
            "agent_utilization": {}
        }
        
        # Filter agent-related metrics
        agent_metric_names = [name for name in all_metrics.keys() if "agent" in name.lower()]
        
        for metric_name in agent_metric_names:
            metric_data = all_metrics[metric_name]
            
            # Extract agent identifier from metric name
            agent_id = self._extract_agent_id(metric_name)
            
            if "completion" in metric_name:
                agent_metrics["agent_performance"][agent_id] = {
                    "completion_rate": metric_data.get("mean", 0.0),
                    "total_tasks": metric_data.get("count", 0)
                }
            
            if "duration" in metric_name:
                agent_metrics["avg_task_duration"] = metric_data.get("mean", 0.0)
            
            if "utilization" in metric_name:
                agent_metrics["agent_utilization"][agent_id] = metric_data.get("latest", 0.0)
        
        # Calculate summary statistics
        if agent_metrics["agent_performance"]:
            completion_rates = [perf["completion_rate"] for perf in agent_metrics["agent_performance"].values()]
            agent_metrics["task_completion_rate"] = statistics.mean(completion_rates) if completion_rates else 0.0
            agent_metrics["total_agents"] = len(agent_metrics["agent_performance"])
            agent_metrics["active_agents"] = len([rate for rate in completion_rates if rate > 0])
        
        return agent_metrics
    
    def _extract_optimization_metrics(self, all_metrics: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Extract DSPy optimization metrics."""
        optimization_metrics = {
            "total_optimizations": 0,
            "optimization_success_rate": 0.0,
            "avg_optimization_time": 0.0,
            "best_scores": {},
            "optimization_methods": {},
            "recent_optimizations": []
        }
        
        # Filter optimization-related metrics
        opt_metric_names = [name for name in all_metrics.keys() if "optimization" in name.lower()]
        
        for metric_name in opt_metric_names:
            metric_data = all_metrics[metric_name]
            
            if "success" in metric_name:
                optimization_metrics["optimization_success_rate"] = metric_data.get("mean", 0.0)
            
            if "duration" in metric_name or "time" in metric_name:
                optimization_metrics["avg_optimization_time"] = metric_data.get("mean", 0.0)
            
            if "score" in metric_name:
                method = self._extract_optimization_method(metric_name)
                optimization_metrics["best_scores"][method] = metric_data.get("max", 0.0)
            
            optimization_metrics["total_optimizations"] += metric_data.get("count", 0)
        
        return optimization_metrics
    
    def _generate_performance_trends(self, hours: int) -> Dict[str, Any]:
        """Generate performance trend data for visualization."""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        # Key metrics to track trends
        trend_metrics = [
            "system.cpu_percent",
            "system.memory_percent", 
            "agent.task_completion_rate",
            "optimization.score",
            "response_time"
        ]
        
        trends = {}
        
        for metric_name in trend_metrics:
            try:
                # Get time series data
                metrics_data = self.metrics_collector.storage.get_metrics(
                    name=metric_name,
                    start_time=start_time,
                    end_time=end_time,
                    limit=self.config.max_data_points
                )
                
                if metrics_data:
                    timestamps = [datetime.fromisoformat(m["timestamp"]) for m in metrics_data]
                    values = [m["value"] for m in metrics_data]
                    
                    trends[metric_name] = {
                        "timestamps": [ts.isoformat() for ts in timestamps],
                        "values": values,
                        "trend_direction": self._calculate_trend_direction(values),
                        "latest_value": values[0] if values else 0,
                        "change_percent": self._calculate_change_percent(values) if len(values) > 1 else 0
                    }
                
            except Exception as e:
                logger.warning(f"Failed to get trend data for {metric_name}: {e}")
                trends[metric_name] = {"error": str(e)}
        
        return trends
    
    def _generate_alert_summary(self, active_alerts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate alert summary statistics."""
        if not active_alerts:
            return {
                "total_active": 0,
                "by_level": {},
                "latest_alert": None,
                "avg_resolution_time": 0
            }
        
        # Count by level
        level_counts = {}
        for alert in active_alerts:
            level = alert.get("level", "unknown")
            level_counts[level] = level_counts.get(level, 0) + 1
        
        # Find latest alert
        latest_alert = max(active_alerts, key=lambda a: a.get("timestamp", ""))
        
        return {
            "total_active": len(active_alerts),
            "by_level": level_counts,
            "latest_alert": latest_alert,
            "critical_count": level_counts.get("critical", 0),
            "warning_count": level_counts.get("warning", 0)
        }
    
    def _calculate_health_score(self, 
                               all_metrics: Dict[str, Dict[str, Any]], 
                               active_alerts: List[Dict[str, Any]]) -> float:
        """Calculate overall system health score (0-100)."""
        score = 100.0
        
        # Penalize for system resource usage
        cpu_usage = 0
        memory_usage = 0
        
        if "system.cpu_percent" in all_metrics:
            cpu_usage = all_metrics["system.cpu_percent"].get("latest", 0)
        
        if "system.memory_percent" in all_metrics:
            memory_usage = all_metrics["system.memory_percent"].get("latest", 0)
        
        # Resource usage penalties
        if cpu_usage > 80:
            score -= (cpu_usage - 80) * 0.5
        if memory_usage > 85:
            score -= (memory_usage - 85) * 0.5
        
        # Alert penalties
        for alert in active_alerts:
            level = alert.get("level", "info")
            if level == "critical":
                score -= 20
            elif level == "error":
                score -= 10
            elif level == "warning":
                score -= 5
        
        # Performance bonuses
        task_completion_rate = 0
        optimization_success_rate = 0
        
        # Extract performance metrics
        for name, metric in all_metrics.items():
            if "completion_rate" in name:
                task_completion_rate = max(task_completion_rate, metric.get("mean", 0))
            if "optimization" in name and "success" in name:
                optimization_success_rate = metric.get("mean", 0)
        
        # Bonus for good performance
        if task_completion_rate > 90:
            score += 5
        if optimization_success_rate > 80:
            score += 5
        
        return max(0, min(100, score))
    
    def generate_performance_chart(self, 
                                 metric_name: str, 
                                 hours: int = 24) -> str:
        """Generate a performance chart for a specific metric."""
        cache_key = f"chart_{metric_name}_{hours}h"
        
        if self._is_cache_valid(cache_key):
            return self.chart_cache[cache_key]
        
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # Get metric data
            metrics_data = self.metrics_collector.storage.get_metrics(
                name=metric_name,
                start_time=start_time,
                end_time=end_time,
                limit=self.config.max_data_points
            )
            
            if not metrics_data:
                return self._generate_no_data_chart(metric_name)
            
            # Prepare data
            timestamps = [datetime.fromisoformat(m["timestamp"]) for m in reversed(metrics_data)]
            values = [m["value"] for m in reversed(metrics_data)]
            
            # Create plot
            fig, ax = plt.subplots(figsize=(self.config.chart_width, self.config.chart_height))
            
            ax.plot(timestamps, values, linewidth=2, marker='o', markersize=3)
            ax.set_title(f"{metric_name} - Last {hours} Hours", fontsize=14, fontweight='bold')
            ax.set_xlabel("Time")
            ax.set_ylabel("Value")
            
            # Format x-axis
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=max(1, hours//12)))
            plt.xticks(rotation=45)
            
            # Add grid
            ax.grid(True, alpha=0.3)
            
            # Add statistics text
            stats_text = f"Latest: {values[-1]:.2f}\nMean: {statistics.mean(values):.2f}\nMax: {max(values):.2f}\nMin: {min(values):.2f}"
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            
            plt.tight_layout()
            
            # Convert to base64 string
            img_buffer = BytesIO()
            plt.savefig(img_buffer, format=self.config.export_format, dpi=self.config.dpi)
            img_buffer.seek(0)
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            plt.close(fig)
            
            # Cache the result
            self.chart_cache[cache_key] = img_base64
            
            return img_base64
            
        except Exception as e:
            logger.error(f"Failed to generate chart for {metric_name}: {e}")
            return self._generate_error_chart(metric_name, str(e))
    
    def generate_multi_metric_dashboard(self, 
                                      metric_names: List[str], 
                                      hours: int = 24) -> str:
        """Generate a multi-metric dashboard visualization."""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            axes = axes.flatten()
            
            for i, metric_name in enumerate(metric_names[:4]):  # Limit to 4 charts
                if i >= len(axes):
                    break
                
                ax = axes[i]
                
                # Get data
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=hours)
                
                metrics_data = self.metrics_collector.storage.get_metrics(
                    name=metric_name,
                    start_time=start_time,
                    end_time=end_time,
                    limit=200  # Smaller limit for multi-chart
                )
                
                if metrics_data:
                    timestamps = [datetime.fromisoformat(m["timestamp"]) for m in reversed(metrics_data)]
                    values = [m["value"] for m in reversed(metrics_data)]
                    
                    ax.plot(timestamps, values, linewidth=2)
                    ax.set_title(metric_name, fontsize=12)
                    ax.grid(True, alpha=0.3)
                    
                    # Format x-axis
                    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
                else:
                    ax.text(0.5, 0.5, 'No Data', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(metric_name, fontsize=12)
            
            # Hide unused subplots
            for i in range(len(metric_names), len(axes)):
                axes[i].set_visible(False)
            
            plt.tight_layout()
            
            # Convert to base64
            img_buffer = BytesIO()
            plt.savefig(img_buffer, format=self.config.export_format, dpi=self.config.dpi)
            img_buffer.seek(0)
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            plt.close(fig)
            
            return img_base64
            
        except Exception as e:
            logger.error(f"Failed to generate multi-metric dashboard: {e}")
            return self._generate_error_chart("Multi-Metric Dashboard", str(e))
    
    def export_dashboard_report(self, 
                              output_path: str = None, 
                              hours: int = 24) -> str:
        """Export comprehensive dashboard report."""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"reports/dashboard_report_{timestamp}.json"
        
        # Ensure directory exists
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # Generate comprehensive data
            dashboard_data = self.generate_dashboard_data(hours)
            
            # Add charts data
            key_metrics = [
                "system.cpu_percent",
                "system.memory_percent",
                "agent.task_completion_rate",
                "optimization.score"
            ]
            
            dashboard_data["charts"] = {}
            for metric in key_metrics:
                try:
                    chart_data = self.generate_performance_chart(metric, hours)
                    dashboard_data["charts"][metric] = chart_data
                except Exception as e:
                    dashboard_data["charts"][metric] = f"Error: {e}"
            
            # Save report
            with open(output_path, 'w') as f:
                json.dump(dashboard_data, f, indent=2, default=str)
            
            logger.info(f"Dashboard report exported to {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to export dashboard report: {e}")
            raise
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.cached_data and cache_key not in self.chart_cache:
            return False
        
        # Cache is valid for refresh interval
        return (datetime.now() - self.last_refresh).seconds < self.config.refresh_interval
    
    def _extract_agent_id(self, metric_name: str) -> str:
        """Extract agent ID from metric name."""
        # Simple extraction - can be enhanced based on naming convention
        parts = metric_name.split('.')
        for part in parts:
            if 'agent' in part.lower():
                return part
        return "unknown"
    
    def _extract_optimization_method(self, metric_name: str) -> str:
        """Extract optimization method from metric name."""
        if "bootstrap" in metric_name.lower():
            return "bootstrap"
        elif "random" in metric_name.lower():
            return "random_search"
        elif "mipro" in metric_name.lower():
            return "miprov2"
        return "unknown"
    
    def _calculate_trend_direction(self, values: List[float]) -> str:
        """Calculate trend direction from values."""
        if len(values) < 2:
            return "stable"
        
        recent_avg = statistics.mean(values[:len(values)//3]) if len(values) >= 3 else values[0]
        older_avg = statistics.mean(values[-len(values)//3:]) if len(values) >= 3 else values[-1]
        
        threshold = 0.05  # 5% change threshold
        change = (recent_avg - older_avg) / older_avg if older_avg != 0 else 0
        
        if change > threshold:
            return "increasing"
        elif change < -threshold:
            return "decreasing"
        else:
            return "stable"
    
    def _calculate_change_percent(self, values: List[float]) -> float:
        """Calculate percentage change from first to last value."""
        if len(values) < 2:
            return 0.0
        
        first_val = values[-1]  # oldest
        last_val = values[0]    # newest
        
        if first_val == 0:
            return 0.0
        
        return ((last_val - first_val) / first_val) * 100
    
    def _generate_no_data_chart(self, metric_name: str) -> str:
        """Generate a placeholder chart for no data."""
        fig, ax = plt.subplots(figsize=(self.config.chart_width, self.config.chart_height))
        ax.text(0.5, 0.5, f'No data available for {metric_name}', 
               ha='center', va='center', fontsize=16)
        ax.set_title(metric_name)
        
        img_buffer = BytesIO()
        plt.savefig(img_buffer, format=self.config.export_format, dpi=self.config.dpi)
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        
        return img_base64
    
    def _generate_error_chart(self, metric_name: str, error: str) -> str:
        """Generate an error chart."""
        fig, ax = plt.subplots(figsize=(self.config.chart_width, self.config.chart_height))
        ax.text(0.5, 0.5, f'Error generating chart for {metric_name}:\n{error}', 
               ha='center', va='center', fontsize=12, color='red')
        ax.set_title(f"Error - {metric_name}")
        
        img_buffer = BytesIO()
        plt.savefig(img_buffer, format=self.config.export_format, dpi=self.config.dpi)
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        
        return img_base64 