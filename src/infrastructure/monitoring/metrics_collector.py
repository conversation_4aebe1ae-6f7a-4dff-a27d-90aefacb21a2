"""
Production Metrics Collection System for Multi-Agent Question Answering System.

Comprehensive monitoring and analytics framework for tracking system performance,
agent behavior, optimization results, and operational metrics.
"""

import time
import threading
import uuid
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque
from pathlib import Path
import json
import logging
from enum import Enum
import statistics
import psutil
import sqlite3
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of metrics collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"
    DISTRIBUTION = "distribution"


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MetricValue:
    """Individual metric measurement."""
    name: str
    value: Union[int, float]
    metric_type: MetricType
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    labels: Dict[str, str] = field(default_factory=dict)
    session_id: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "name": self.name,
            "value": self.value,
            "metric_type": self.metric_type.value,
            "timestamp": self.timestamp.isoformat(),
            "tags": self.tags,
            "labels": self.labels,
            "session_id": self.session_id
        }


@dataclass
class Alert:
    """System alert."""
    id: str
    level: AlertLevel
    message: str
    metric_name: str
    metric_value: float
    threshold: float
    timestamp: datetime
    resolved: bool = False
    resolution_timestamp: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "level": self.level.value,
            "message": self.message,
            "metric_name": self.metric_name,
            "metric_value": self.metric_value,
            "threshold": self.threshold,
            "timestamp": self.timestamp.isoformat(),
            "resolved": self.resolved,
            "resolution_timestamp": self.resolution_timestamp.isoformat() if self.resolution_timestamp else None
        }


class MetricsStorage:
    """Persistent storage for metrics data."""
    
    def __init__(self, db_path: str = "data/metrics.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for metrics storage."""
        with self._get_connection() as conn:
            # Metrics table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    value REAL NOT NULL,
                    metric_type TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    tags TEXT,
                    labels TEXT,
                    session_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Alerts table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    threshold REAL NOT NULL,
                    timestamp TEXT NOT NULL,
                    resolved BOOLEAN DEFAULT FALSE,
                    resolution_timestamp TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indices for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_name ON metrics(name)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_alerts_level ON alerts(level)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_alerts_resolved ON alerts(resolved)")
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper cleanup."""
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def store_metric(self, metric: MetricValue):
        """Store a metric value."""
        with self._get_connection() as conn:
            conn.execute("""
                INSERT INTO metrics (name, value, metric_type, timestamp, tags, labels, session_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                metric.name,
                metric.value,
                metric.metric_type.value,
                metric.timestamp.isoformat(),
                json.dumps(metric.tags),
                json.dumps(metric.labels),
                metric.session_id
            ))
    
    def store_alert(self, alert: Alert):
        """Store an alert."""
        with self._get_connection() as conn:
            conn.execute("""
                INSERT OR REPLACE INTO alerts 
                (id, level, message, metric_name, metric_value, threshold, timestamp, resolved, resolution_timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                alert.id,
                alert.level.value,
                alert.message,
                alert.metric_name,
                alert.metric_value,
                alert.threshold,
                alert.timestamp.isoformat(),
                alert.resolved,
                alert.resolution_timestamp.isoformat() if alert.resolution_timestamp else None
            ))
    
    def get_metrics(self, 
                   name: Optional[str] = None,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None,
                   limit: int = 1000) -> List[Dict[str, Any]]:
        """Retrieve metrics from storage."""
        query = "SELECT * FROM metrics WHERE 1=1"
        params = []
        
        if name:
            query += " AND name = ?"
            params.append(name)
        
        if start_time:
            query += " AND timestamp >= ?"
            params.append(start_time.isoformat())
        
        if end_time:
            query += " AND timestamp <= ?"
            params.append(end_time.isoformat())
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        with self._get_connection() as conn:
            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def get_alerts(self, 
                  resolved: Optional[bool] = None,
                  level: Optional[AlertLevel] = None,
                  limit: int = 100) -> List[Dict[str, Any]]:
        """Retrieve alerts from storage."""
        query = "SELECT * FROM alerts WHERE 1=1"
        params = []
        
        if resolved is not None:
            query += " AND resolved = ?"
            params.append(resolved)
        
        if level:
            query += " AND level = ?"
            params.append(level.value)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        with self._get_connection() as conn:
            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]


class MetricsCollector:
    """
    Central metrics collection and monitoring system.
    
    Collects, stores, and analyzes metrics from across the multi-agent system
    including performance metrics, resource usage, and optimization results.
    """
    
    def __init__(self, 
                 storage_path: str = "data/metrics.db",
                 buffer_size: int = 1000,
                 flush_interval: int = 60):
        self.storage = MetricsStorage(storage_path)
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        
        # In-memory buffers
        self.metrics_buffer: deque = deque(maxlen=buffer_size)
        self.alerts_buffer: deque = deque(maxlen=buffer_size)
        
        # Real-time metrics tracking
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self.timers: Dict[str, List[float]] = defaultdict(list)
        
        # Alert thresholds
        self.thresholds: Dict[str, Dict[str, float]] = {}
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # Background processing
        self._running = False
        self._flush_thread = None
        self._session_id = str(uuid.uuid4())
        
        # System metrics collection
        self._system_metrics_enabled = True
        self._last_system_collection = datetime.now()
        
        logger.info(f"MetricsCollector initialized with session {self._session_id}")
    
    def start(self):
        """Start background metrics processing."""
        if self._running:
            return
        
        self._running = True
        self._flush_thread = threading.Thread(target=self._background_flush, daemon=True)
        self._flush_thread.start()
        
        logger.info("MetricsCollector background processing started")
    
    def stop(self):
        """Stop background processing and flush remaining metrics."""
        self._running = False
        if self._flush_thread:
            self._flush_thread.join(timeout=5)
        
        self._flush_buffers()
        logger.info("MetricsCollector stopped")
    
    def record_counter(self, name: str, value: float = 1.0, tags: Dict[str, str] = None):
        """Record a counter metric (cumulative value)."""
        self.counters[name] += value
        self._record_metric(name, self.counters[name], MetricType.COUNTER, tags)
    
    def record_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record a gauge metric (point-in-time value)."""
        self.gauges[name] = value
        self._record_metric(name, value, MetricType.GAUGE, tags)
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record a histogram metric (distribution of values)."""
        self.histograms[name].append(value)
        # Keep only recent values to prevent memory growth
        if len(self.histograms[name]) > 1000:
            self.histograms[name] = self.histograms[name][-1000:]
        
        self._record_metric(name, value, MetricType.HISTOGRAM, tags)
    
    def record_timer(self, name: str, duration: float, tags: Dict[str, str] = None):
        """Record a timer metric (execution duration)."""
        self.timers[name].append(duration)
        # Keep only recent values
        if len(self.timers[name]) > 1000:
            self.timers[name] = self.timers[name][-1000:]
        
        self._record_metric(name, duration, MetricType.TIMER, tags)
    
    def time_operation(self, name: str, tags: Dict[str, str] = None):
        """Context manager for timing operations."""
        return TimerContext(self, name, tags)
    
    def _record_metric(self, 
                      name: str, 
                      value: float, 
                      metric_type: MetricType, 
                      tags: Dict[str, str] = None):
        """Internal method to record a metric."""
        metric = MetricValue(
            name=name,
            value=value,
            metric_type=metric_type,
            timestamp=datetime.now(),
            tags=tags or {},
            session_id=self._session_id
        )
        
        self.metrics_buffer.append(metric)
        
        # Check for alert conditions
        self._check_alerts(metric)
        
        # Collect system metrics periodically
        self._maybe_collect_system_metrics()
    
    def set_threshold(self, 
                     metric_name: str, 
                     level: AlertLevel, 
                     threshold: float):
        """Set alert threshold for a metric."""
        if metric_name not in self.thresholds:
            self.thresholds[metric_name] = {}
        
        self.thresholds[metric_name][level.value] = threshold
        logger.info(f"Set {level.value} threshold for {metric_name}: {threshold}")
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """Add callback function for alert notifications."""
        self.alert_callbacks.append(callback)
    
    def _check_alerts(self, metric: MetricValue):
        """Check if metric value triggers any alerts."""
        if metric.name not in self.thresholds:
            return
        
        thresholds = self.thresholds[metric.name]
        
        for level_str, threshold in thresholds.items():
            level = AlertLevel(level_str)
            
            # Simple threshold check (can be extended for more complex conditions)
            if metric.value > threshold:
                alert = Alert(
                    id=f"{metric.name}_{level.value}_{metric.timestamp.isoformat()}",
                    level=level,
                    message=f"Metric {metric.name} exceeded {level.value} threshold: {metric.value} > {threshold}",
                    metric_name=metric.name,
                    metric_value=metric.value,
                    threshold=threshold,
                    timestamp=metric.timestamp
                )
                
                self.alerts_buffer.append(alert)
                
                # Notify callbacks
                for callback in self.alert_callbacks:
                    try:
                        callback(alert)
                    except Exception as e:
                        logger.error(f"Alert callback failed: {e}")
    
    def _maybe_collect_system_metrics(self):
        """Collect system metrics if enough time has passed."""
        if not self._system_metrics_enabled:
            return
        
        now = datetime.now()
        if (now - self._last_system_collection).seconds < 30:  # Collect every 30 seconds
            return
        
        self._last_system_collection = now
        
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            self.record_gauge("system.cpu_percent", cpu_percent, {"type": "system"})
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.record_gauge("system.memory_percent", memory.percent, {"type": "system"})
            self.record_gauge("system.memory_available_mb", memory.available / 1024 / 1024, {"type": "system"})
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.record_gauge("system.disk_percent", (disk.used / disk.total) * 100, {"type": "system"})
            
            # Process count
            process_count = len(psutil.pids())
            self.record_gauge("system.process_count", process_count, {"type": "system"})
            
        except Exception as e:
            logger.warning(f"Failed to collect system metrics: {e}")
    
    def _background_flush(self):
        """Background thread for flushing metrics to storage."""
        while self._running:
            time.sleep(self.flush_interval)
            self._flush_buffers()
    
    def _flush_buffers(self):
        """Flush buffered metrics and alerts to storage."""
        try:
            # Flush metrics
            metrics_to_flush = []
            while self.metrics_buffer and len(metrics_to_flush) < 100:
                metrics_to_flush.append(self.metrics_buffer.popleft())
            
            for metric in metrics_to_flush:
                self.storage.store_metric(metric)
            
            # Flush alerts
            alerts_to_flush = []
            while self.alerts_buffer and len(alerts_to_flush) < 100:
                alerts_to_flush.append(self.alerts_buffer.popleft())
            
            for alert in alerts_to_flush:
                self.storage.store_alert(alert)
            
            if metrics_to_flush or alerts_to_flush:
                logger.debug(f"Flushed {len(metrics_to_flush)} metrics and {len(alerts_to_flush)} alerts")
                
        except Exception as e:
            logger.error(f"Failed to flush metrics: {e}")
    
    def get_metric_summary(self, metric_name: str, hours: int = 24) -> Dict[str, Any]:
        """Get statistical summary of a metric over time period."""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        metrics = self.storage.get_metrics(
            name=metric_name,
            start_time=start_time,
            end_time=end_time
        )
        
        if not metrics:
            return {"metric_name": metric_name, "count": 0}
        
        values = [m["value"] for m in metrics]
        
        return {
            "metric_name": metric_name,
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "mean": statistics.mean(values),
            "median": statistics.median(values),
            "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
            "latest": values[0] if values else None,
            "oldest": values[-1] if values else None,
            "time_range": {
                "start": start_time.isoformat(),
                "end": end_time.isoformat()
            }
        }
    
    def get_all_metrics_summary(self, hours: int = 24) -> Dict[str, Dict[str, Any]]:
        """Get summaries of all metrics."""
        # Get unique metric names from recent data
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        all_metrics = self.storage.get_metrics(
            start_time=start_time,
            end_time=end_time,
            limit=10000
        )
        
        metric_names = set(m["name"] for m in all_metrics)
        
        summaries = {}
        for name in metric_names:
            summaries[name] = self.get_metric_summary(name, hours)
        
        return summaries
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all unresolved alerts."""
        return self.storage.get_alerts(resolved=False)
    
    def resolve_alert(self, alert_id: str):
        """Mark an alert as resolved."""
        # This would need to update the database
        # Implementation depends on storage backend
        pass


class TimerContext:
    """Context manager for timing operations."""
    
    def __init__(self, collector: MetricsCollector, name: str, tags: Dict[str, str] = None):
        self.collector = collector
        self.name = name
        self.tags = tags or {}
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.collector.record_timer(self.name, duration, self.tags)


# Global metrics collector instance
_global_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    global _global_collector
    if _global_collector is None:
        _global_collector = MetricsCollector()
        _global_collector.start()
    return _global_collector


def initialize_metrics_system(storage_path: str = "data/metrics.db",
                            buffer_size: int = 1000,
                            flush_interval: int = 60) -> MetricsCollector:
    """Initialize the global metrics system."""
    global _global_collector
    if _global_collector:
        _global_collector.stop()
    
    _global_collector = MetricsCollector(storage_path, buffer_size, flush_interval)
    _global_collector.start()
    
    # Set default thresholds
    _global_collector.set_threshold("system.cpu_percent", AlertLevel.WARNING, 80.0)
    _global_collector.set_threshold("system.cpu_percent", AlertLevel.CRITICAL, 95.0)
    _global_collector.set_threshold("system.memory_percent", AlertLevel.WARNING, 85.0)
    _global_collector.set_threshold("system.memory_percent", AlertLevel.CRITICAL, 95.0)
    
    logger.info("Global metrics system initialized")
    return _global_collector


def shutdown_metrics_system():
    """Shutdown the global metrics system."""
    global _global_collector
    if _global_collector:
        _global_collector.stop()
        _global_collector = None
    logger.info("Global metrics system shutdown") 