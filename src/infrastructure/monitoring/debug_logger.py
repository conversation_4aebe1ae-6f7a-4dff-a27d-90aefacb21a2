"""
Debug Logger for System Validation

This module provides comprehensive file logging to validate that all system components
are working exactly as intended. It logs to timestamped files with detailed validation.
"""

import os
import json
import time
from datetime import datetime
from typing import Any, Dict, Optional
from pathlib import Path


class SystemDebugLogger:
    """Debug logger that writes detailed validation logs to files."""
    
    def __init__(self, base_dir: str = "logs/debug"):
        """Initialize debug logger with timestamped log files."""
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # Create timestamped session
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.session_dir = self.base_dir / f"session_{self.session_id}"
        self.session_dir.mkdir(exist_ok=True)
        
        # Component-specific log files
        self.log_files = {
            'config_validation': self.session_dir / "config_validation.log",
            'evaluation_pipeline': self.session_dir / "evaluation_pipeline.log", 
            'training_data': self.session_dir / "training_data.log",
            'dspy_optimization': self.session_dir / "dspy_optimization.log",
            'workflow_execution': self.session_dir / "workflow_execution.log",
            'quality_gates': self.session_dir / "quality_gates.log",
            'specialist_flows': self.session_dir / "specialist_flows.log"
        }
        
        # Initialize all log files
        for component, log_file in self.log_files.items():
            self._write_log(component, "SYSTEM_START", {
                "timestamp": datetime.now().isoformat(),
                "session_id": self.session_id,
                "component": component,
                "message": f"{component} debug logging initialized"
            })
        
        print(f"🔍 Debug logging initialized: {self.session_dir}")
    
    def log_config_validation(self, config_section: str, expected: Any, actual: Any, valid: bool):
        """Log configuration validation results."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "config_section": config_section,
            "expected": expected,
            "actual": actual,
            "valid": valid,
            "issue": None if valid else f"Expected {expected}, got {actual}"
        }
        self._write_log('config_validation', 'CONFIG_CHECK', entry)
        
        if not valid:
            print(f"❌ CONFIG ISSUE: {config_section} - Expected {expected}, got {actual}")
        else:
            print(f"✅ CONFIG OK: {config_section}")
    
    def log_evaluation_execution(self, query: str, answer: str, evaluation_results: Optional[Dict], 
                               enabled: bool, error: Optional[str] = None):
        """Log evaluation pipeline execution details."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "evaluation_enabled": enabled,
            "query": query[:100] + "..." if len(query) > 100 else query,
            "answer_length": len(answer),
            "evaluation_executed": evaluation_results is not None,
            "evaluation_results": evaluation_results,
            "error": error,
            "success": evaluation_results is not None and error is None
        }
        self._write_log('evaluation_pipeline', 'EVALUATION_ATTEMPT', entry)
        
        if not enabled:
            print(f"⚠️ EVALUATION: Disabled in execution (config may not be loaded)")
        elif error:
            print(f"❌ EVALUATION: Failed - {error}")
        elif evaluation_results:
            composite = evaluation_results.composite_score if hasattr(evaluation_results, 'composite_score') else evaluation_results.get('composite_score', 0)
            print(f"✅ EVALUATION: Success - Composite score: {composite:.3f}")
    
    def log_training_data_collection(self, session_id: str, query: str, workflow_result: Dict,
                                   example_id: Optional[str], quality_metrics: Dict, 
                                   stored_correctly: bool, error: Optional[str] = None):
        """Log training data collection with validation."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": session_id,
            "query": query[:100] + "..." if len(query) > 100 else query,
            "workflow_result_keys": list(workflow_result.keys()),
            "example_id": example_id,
            "quality_metrics": quality_metrics,
            "stored_correctly": stored_correctly,
            "error": error,
            "success": example_id is not None and stored_correctly and error is None
        }
        self._write_log('training_data', 'DATA_COLLECTION', entry)
        
        if error:
            print(f"❌ TRAINING DATA: Failed - {error}")
        elif not stored_correctly:
            print(f"❌ TRAINING DATA: Storage validation failed")
        else:
            print(f"✅ TRAINING DATA: Stored correctly - ID: {example_id}")
    
    def log_dspy_optimization(self, dataset_size: int, optimizer_type: Optional[str],
                            triggered: bool, success: bool, error: Optional[str] = None):
        """Log DSPy optimization attempts and results."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "dataset_size": dataset_size,
            "optimizer_type": optimizer_type,
            "triggered": triggered,
            "success": success,
            "error": error
        }
        self._write_log('dspy_optimization', 'OPTIMIZATION_ATTEMPT', entry)
        
        if error:
            print(f"❌ DSPY OPT: Failed - {error}")
        elif not triggered:
            print(f"⚠️ DSPY OPT: Not triggered - Dataset size: {dataset_size}")
        elif success:
            print(f"✅ DSPY OPT: Success - {optimizer_type}")
    
    def log_quality_gates(self, evaluation_results: Dict, gate_results: Optional[Dict],
                         enabled: bool, error: Optional[str] = None):
        """Log quality gates evaluation."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "quality_gates_enabled": enabled,
            "evaluation_results": evaluation_results,
            "gate_results": gate_results,
            "error": error,
            "success": gate_results is not None and error is None
        }
        self._write_log('quality_gates', 'GATES_EVALUATION', entry)
        
        if not enabled:
            print(f"⚠️ QUALITY GATES: Disabled")
        elif error:
            print(f"❌ QUALITY GATES: Failed - {error}")
        elif gate_results:
            passed = len(gate_results.get('passed_gates', []))
            failed = len(gate_results.get('failed_gates', []))
            print(f"✅ QUALITY GATES: {passed} passed, {failed} failed")
    
    def log_workflow_execution(self, workflow_type: str, stage: str, data: Dict,
                             expected_behavior: str, actual_behavior: str, correct: bool):
        """Log workflow execution validation."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "workflow_type": workflow_type,
            "stage": stage,
            "expected_behavior": expected_behavior,
            "actual_behavior": actual_behavior,
            "correct": correct,
            "data": data
        }
        self._write_log('workflow_execution', 'WORKFLOW_VALIDATION', entry)
        
        if not correct:
            print(f"❌ WORKFLOW: {workflow_type}/{stage} - Expected: {expected_behavior}, Got: {actual_behavior}")
        else:
            print(f"✅ WORKFLOW: {workflow_type}/{stage} - Correct")
    
    def log_specialist_flow(self, flow_name: str, optimization_enabled: bool, 
                          compilation_success: bool, results: Dict, error: Optional[str] = None):
        """Log specialist flow execution and optimization."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "flow_name": flow_name,
            "optimization_enabled": optimization_enabled,
            "compilation_success": compilation_success,
            "results": results,
            "error": error,
            "success": compilation_success and error is None
        }
        self._write_log('specialist_flows', 'SPECIALIST_EXECUTION', entry)
        
        if error:
            print(f"❌ SPECIALIST: {flow_name} - {error}")
        elif not optimization_enabled:
            print(f"⚠️ SPECIALIST: {flow_name} - Optimization disabled")
        else:
            print(f"✅ SPECIALIST: {flow_name} - Success")
    
    def _write_log(self, component: str, event_type: str, data: Dict):
        """Write log entry to component-specific file."""
        log_file = self.log_files[component]
        
        log_entry = {
            "event_type": event_type,
            "data": data
        }
        
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, indent=2, default=str) + "\n" + "="*80 + "\n")
        except Exception as e:
            print(f"❌ Failed to write debug log: {e}")
    
    def get_session_summary(self) -> str:
        """Get summary of current debug session."""
        return f"Debug session: {self.session_id} in {self.session_dir}"


# Global debug logger instance
_debug_logger: Optional[SystemDebugLogger] = None

def get_debug_logger() -> SystemDebugLogger:
    """Get or create global debug logger instance."""
    global _debug_logger
    if _debug_logger is None:
        _debug_logger = SystemDebugLogger()
    return _debug_logger

def init_debug_logging() -> SystemDebugLogger:
    """Initialize debug logging for a new session."""
    global _debug_logger
    _debug_logger = SystemDebugLogger()
    return _debug_logger 