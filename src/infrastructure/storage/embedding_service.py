import asyncio
import time
from typing import List, Dict, Any, Optional, Union
from enum import Enum
import numpy as np
import logging
import hashlib
import json

# Embedding provider imports with fallbacks
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    from transformers import AutoTokenizer, AutoModel
    import torch
    HUGGINGFACE_AVAILABLE = True
except ImportError:
    HUGGINGFACE_AVAILABLE = False

class EmbeddingProvider(Enum):
    OPENAI = "openai"
    SENTENCE_TRANSFORMERS = "sentence_transformers"
    HUGGINGFACE = "huggingface"

class EnterpriseEmbeddingService:
    """
    Production-grade embedding service with multiple provider support.
    
    Features:
    - Multiple embedding providers (OpenAI, SentenceTransformers, HuggingFace)
    - Automatic batching for performance
    - Caching for frequently used embeddings
    - Rate limiting and error handling
    - Multi-modal embedding support
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.provider = EmbeddingProvider(config.get('provider', 'openai'))
        self.model_name = config.get('model_name', 'text-embedding-3-small')
        self.dimension = config.get('dimension', 1536)
        self.batch_size = config.get('batch_size', 100)
        
        # Caching
        self.embedding_cache = {}
        self.cache_enabled = config.get('cache_enabled', True)
        self.max_cache_size = config.get('max_cache_size', 10000)
        
        # Rate limiting
        self.rate_limit = config.get('rate_limit', 1000)  # requests per minute
        self.request_count = 0
        self.last_reset = time.time()
        
        self.model = None
        self.tokenizer = None
        self.logger = logging.getLogger(__name__)
        
        # Validate provider availability
        self._validate_provider()
    
    def _validate_provider(self):
        """Validate that the selected provider is available."""
        if self.provider == EmbeddingProvider.OPENAI and not OPENAI_AVAILABLE:
            self.logger.warning("OpenAI not available, trying other providers")
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                self.provider = EmbeddingProvider.SENTENCE_TRANSFORMERS
                self.model_name = "all-MiniLM-L6-v2"
                self.dimension = 384
            elif HUGGINGFACE_AVAILABLE:
                self.provider = EmbeddingProvider.HUGGINGFACE
                self.model_name = "sentence-transformers/all-MiniLM-L6-v2"
                self.dimension = 384
            else:
                raise ImportError("No embedding providers available")
                
        elif self.provider == EmbeddingProvider.SENTENCE_TRANSFORMERS and not SENTENCE_TRANSFORMERS_AVAILABLE:
            self.logger.warning("SentenceTransformers not available, falling back to OpenAI or HuggingFace")
            if OPENAI_AVAILABLE:
                self.provider = EmbeddingProvider.OPENAI
            elif HUGGINGFACE_AVAILABLE:
                self.provider = EmbeddingProvider.HUGGINGFACE
            else:
                raise ImportError("No embedding providers available")
    
    async def initialize(self):
        """Initialize embedding model based on provider."""
        try:
            if self.provider == EmbeddingProvider.OPENAI and OPENAI_AVAILABLE:
                # OpenAI client is initialized on-demand
                pass
                
            elif self.provider == EmbeddingProvider.SENTENCE_TRANSFORMERS and SENTENCE_TRANSFORMERS_AVAILABLE:
                self.model = SentenceTransformer(self.model_name)
                
            elif self.provider == EmbeddingProvider.HUGGINGFACE and HUGGINGFACE_AVAILABLE:
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.model = AutoModel.from_pretrained(self.model_name)
                
            self.logger.info(f"Embedding service ({self.provider.value}) initialized with model: {self.model_name}")
            print(f"✅ Embedding service ({self.provider.value}) initialized with model: {self.model_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize embedding service: {e}")
            print(f"❌ Failed to initialize embedding service: {e}")
            raise
    
    async def embed_texts(
        self, 
        texts: List[str], 
        normalize: bool = True
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts with batching and caching.
        
        Args:
            texts: List of text strings to embed
            normalize: Whether to normalize embeddings
        """
        if not texts:
            return []
        
        # Check cache first
        embeddings = []
        uncached_texts = []
        uncached_indices = []
        
        for i, text in enumerate(texts):
            cache_key = self._get_cache_key(text)
            if self.cache_enabled and cache_key in self.embedding_cache:
                embeddings.append(self.embedding_cache[cache_key])
            else:
                embeddings.append(None)
                uncached_texts.append(text)
                uncached_indices.append(i)
        
        # Generate embeddings for uncached texts
        if uncached_texts:
            self.logger.info(f"Generating embeddings for {len(uncached_texts)} uncached texts")
            new_embeddings = await self._generate_embeddings(uncached_texts)
            
            # Update cache and results
            for idx, text, embedding in zip(uncached_indices, uncached_texts, new_embeddings):
                embeddings[idx] = embedding
                if self.cache_enabled:
                    self._update_cache(text, embedding)
        
        if normalize:
            embeddings = [self._normalize_embedding(emb) for emb in embeddings if emb is not None]
        
        return embeddings
    
    async def embed_multimodal(
        self,
        content: Dict[str, Any]
    ) -> List[float]:
        """
        Generate embeddings for multimodal content (text, images, etc.).
        
        Args:
            content: Dictionary with 'text', 'image_path', etc.
        """
        if 'text' in content and 'image_path' in content:
            # Combine text and image embeddings (simplified implementation)
            text_embedding = await self.embed_texts([content['text']])
            # Note: Image embedding would require additional models like CLIP
            # For now, just return text embedding
            return text_embedding[0] if text_embedding else []
            
        elif 'text' in content:
            embeddings = await self.embed_texts([content['text']])
            return embeddings[0] if embeddings else []
            
        elif 'image_path' in content:
            # Image embedding not implemented in this basic version
            # Would require CLIP or similar multimodal model
            self.logger.warning("Image embedding not implemented, returning zero vector")
            return [0.0] * self.dimension
        
        raise ValueError("Unsupported content type for embedding")
    
    async def _generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using the configured provider."""
        if self.provider == EmbeddingProvider.OPENAI:
            return await self._generate_openai_embeddings(texts)
        elif self.provider == EmbeddingProvider.SENTENCE_TRANSFORMERS:
            return await self._generate_st_embeddings(texts)
        elif self.provider == EmbeddingProvider.HUGGINGFACE:
            return await self._generate_hf_embeddings(texts)
        else:
            raise ValueError(f"Unknown provider: {self.provider}")
    
    async def _generate_openai_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using OpenAI API with batching."""
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI not available")
            
        embeddings = []
        
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            
            # Rate limiting check
            await self._check_rate_limit()
            
            try:
                response = await openai.Embedding.acreate(
                    model=self.model_name,
                    input=batch
                )
                
                batch_embeddings = [item['embedding'] for item in response['data']]
                embeddings.extend(batch_embeddings)
                
                self.request_count += 1
                self.logger.debug(f"Generated OpenAI embeddings for batch {i//self.batch_size + 1}")
                
            except Exception as e:
                self.logger.error(f"OpenAI embedding error for batch {i//self.batch_size + 1}: {e}")
                # Add empty embeddings for failed batch
                embeddings.extend([[0.0] * self.dimension] * len(batch))
        
        return embeddings
    
    async def _generate_st_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using SentenceTransformers."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("SentenceTransformers not available")
            
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None, 
                lambda: self.model.encode(texts, convert_to_numpy=True)
            )
            return embeddings.tolist()
            
        except Exception as e:
            self.logger.error(f"SentenceTransformers embedding error: {e}")
            return [[0.0] * self.dimension] * len(texts)
    
    async def _generate_hf_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using HuggingFace transformers."""
        if not HUGGINGFACE_AVAILABLE:
            raise ImportError("HuggingFace transformers not available")
            
        try:
            embeddings = []
            
            for text in texts:
                # Tokenize and encode
                inputs = self.tokenizer(text, return_tensors='pt', truncation=True, padding=True)
                
                with torch.no_grad():
                    outputs = self.model(**inputs)
                    # Use mean pooling of last hidden states
                    embedding = outputs.last_hidden_state.mean(dim=1).squeeze().numpy()
                    embeddings.append(embedding.tolist())
            
            return embeddings
            
        except Exception as e:
            self.logger.error(f"HuggingFace embedding error: {e}")
            return [[0.0] * self.dimension] * len(texts)
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        current_time = time.time()
        
        # Reset counter every minute
        if current_time - self.last_reset > 60:
            self.request_count = 0
            self.last_reset = current_time
        
        # Check if we're hitting rate limit
        if self.request_count >= self.rate_limit:
            sleep_time = 60 - (current_time - self.last_reset)
            if sleep_time > 0:
                self.logger.warning(f"Rate limit reached, sleeping for {sleep_time:.1f} seconds")
                await asyncio.sleep(sleep_time)
                self.request_count = 0
                self.last_reset = time.time()
    
    def _get_cache_key(self, text: str) -> str:
        """Generate cache key for text."""
        # Use hash of text + provider + model for cache key
        cache_string = f"{self.provider.value}:{self.model_name}:{text}"
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _normalize_embedding(self, embedding: List[float]) -> List[float]:
        """Normalize embedding vector to unit length."""
        if not embedding:
            return embedding
            
        embedding_array = np.array(embedding)
        norm = np.linalg.norm(embedding_array)
        if norm > 0:
            return (embedding_array / norm).tolist()
        return embedding
    
    def _update_cache(self, text: str, embedding: List[float]):
        """Update embedding cache with size management."""
        if len(self.embedding_cache) >= self.max_cache_size:
            # Remove oldest entry (simple LRU)
            oldest_key = next(iter(self.embedding_cache))
            del self.embedding_cache[oldest_key]
        
        cache_key = self._get_cache_key(text)
        self.embedding_cache[cache_key] = embedding
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cache_size": len(self.embedding_cache),
            "max_cache_size": self.max_cache_size,
            "cache_enabled": self.cache_enabled,
            "hit_rate": self.query_metrics.get('cache_hit_rate', 0.0) if hasattr(self, 'query_metrics') else 0.0
        }
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current provider."""
        return {
            "provider": self.provider.value,
            "model_name": self.model_name,
            "dimension": self.dimension,
            "batch_size": self.batch_size,
            "rate_limit": self.rate_limit
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the embedding service."""
        try:
            # Test with a simple embedding
            test_result = await self.embed_texts(["test"])
            
            return {
                "status": "healthy",
                "provider": self.provider.value,
                "model": self.model_name,
                "test_embedding_length": len(test_result[0]) if test_result and test_result[0] else 0,
                "cache_stats": self.get_cache_stats()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "provider": self.provider.value,
                "model": self.model_name
            } 