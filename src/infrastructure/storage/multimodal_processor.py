from typing import Dict, List, Any, Optional, Union, Tuple
import logging
import os
from enum import Enum
import base64
import io
from dataclasses import dataclass
from pathlib import Path

# Image processing imports with fallbacks
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import cv2
    import numpy as np
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

class ContentType(Enum):
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    PDF = "pdf"

@dataclass
class ProcessedContent:
    """Processed content with extracted text and metadata."""
    content_type: ContentType
    text_content: str
    metadata: Dict[str, Any]
    embedding_vector: List[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "content_type": self.content_type.value,
            "text_content": self.text_content,
            "metadata": self.metadata,
            "embedding_vector": self.embedding_vector
        }

class MultimodalProcessor:
    """
    Advanced multimodal content processor.
    
    Features:
    - Text processing and chunking
    - Image analysis and feature extraction
    - Document parsing and structuring
    - Content type detection
    - Metadata extraction
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Processing settings
        self.max_text_chunk_size = config.get('max_text_chunk_size', 1000)
        self.text_overlap = config.get('text_overlap', 100)
        self.image_max_size = config.get('image_max_size', (800, 800))
        self.supported_image_formats = config.get(
            'supported_image_formats', 
            ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        )
        
    async def initialize(self):
        """Initialize the multimodal processor."""
        self.logger.info("Multimodal processor initialized")
        print("✅ Multimodal processor initialized")
    
    async def process_content(
        self,
        content: Any,
        content_type: Optional[ContentType] = None,
        source_path: Optional[str] = None
    ) -> ProcessedContent:
        """
        Process content based on its type.
        
        Args:
            content: Raw content to process
            content_type: Explicit content type, if known
            source_path: Path to source file, if applicable
        """
        # Auto-detect content type if not provided
        if content_type is None:
            content_type = await self._detect_content_type(source_path)
        
        # Process based on content type
        if content_type == ContentType.TEXT:
            return await self._process_text(content, source_path)
        elif content_type == ContentType.IMAGE:
            return await self._process_image(content, source_path)
        elif content_type == ContentType.DOCUMENT:
            return await self._process_document(content, source_path)
        elif content_type == ContentType.PDF:
            return await self._process_pdf(content, source_path)
        else:
            # Default text processing for unsupported types
            return await self._process_text(str(content), source_path)
    
    async def _detect_content_type(
        self,
        source_path: Optional[str] = None
    ) -> ContentType:
        """Detect content type from file path."""
        
        # Check file extension first
        if source_path:
            ext = Path(source_path).suffix.lower().lstrip('.')
            
            if ext in self.supported_image_formats:
                return ContentType.IMAGE
            elif ext in ['.pdf', '.doc', '.docx', '.txt', '.md']:
                return ContentType.DOCUMENT
            elif ext in ['.mp3', '.wav', '.flac']:
                return ContentType.AUDIO
            elif ext in ['.mp4', '.avi', '.mov']:
                return ContentType.VIDEO
        
        # Check content type
        if isinstance(source_path, str) and os.path.isfile(source_path):
            # Check if it's a file path to an image
            if any(source_path.lower().endswith(ext) for ext in self.supported_image_formats):
                return ContentType.IMAGE
            return ContentType.TEXT
        
        elif hasattr(source_path, 'read'):  # File-like object
            return ContentType.DOCUMENT
        
        # Default to text
        return ContentType.TEXT
    
    async def _process_text(
        self,
        text: str,
        source_path: Optional[str] = None
    ) -> ProcessedContent:
        """Process text content with chunking and analysis."""
        
        # Clean and normalize text
        cleaned_text = self._clean_text(text)
        
        # Create chunks if text is too long
        chunks = self._chunk_text(cleaned_text)
        
        # Extract metadata
        metadata = {
            'content_type': 'text',
            'original_length': len(text),
            'cleaned_length': len(cleaned_text),
            'chunk_count': len(chunks),
            'source_path': source_path,
            'language': self._detect_language(cleaned_text),
            'word_count': len(cleaned_text.split()),
            'processing_timestamp': self._get_timestamp()
        }
        
        return ProcessedContent(
            content_type=ContentType.TEXT,
            text_content=text,
            metadata=metadata
        )
    
    async def _process_image(
        self,
        image_input: Union[str, bytes, Any],
        source_path: Optional[str] = None
    ) -> ProcessedContent:
        """Process image content with analysis and feature extraction."""
        
        if not PIL_AVAILABLE:
            self.logger.warning("PIL not available, basic image processing only")
            return ProcessedContent(
                content_type=ContentType.IMAGE,
                text_content="[Image content - PIL not available]",
                metadata={"source_path": source_path, "error": "PIL not available"}
            )
        
        try:
            # Load image
            if isinstance(image_input, str) and os.path.isfile(image_input):
                image = Image.open(image_input)
                source_path = source_path or image_input
            elif isinstance(image_input, bytes):
                image = Image.open(io.BytesIO(image_input))
            else:
                image = image_input
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize if too large
            if image.size[0] > self.image_max_size[0] or image.size[1] > self.image_max_size[1]:
                image.thumbnail(self.image_max_size, Image.Resampling.LANCZOS)
            
            # Extract basic features
            features = await self._extract_image_features(image)
            
            # Generate thumbnail
            thumbnail = image.copy()
            thumbnail.thumbnail((150, 150), Image.Resampling.LANCZOS)
            
            # Convert to base64 for storage
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=85)
            image_b64 = base64.b64encode(buffer.getvalue()).decode()
            
            metadata = {
                'content_type': 'image',
                'original_size': image.size,
                'format': image.format,
                'mode': image.mode,
                'source_path': source_path,
                'features': features,
                'file_size': len(image_b64),
                'processing_timestamp': self._get_timestamp()
            }
            
            processed_content = {
                'image_data': image_b64,
                'features': features,
                'size': image.size
            }
            
            return ProcessedContent(
                content_type=ContentType.IMAGE,
                text_content=f"[Image: {source_path}, {image.size[0]}x{image.size[1]}, {image.mode}]",
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"Error processing image: {e}")
            return ProcessedContent(
                content_type=ContentType.IMAGE,
                text_content=f"[Image processing error: {str(e)}]",
                metadata={"source_path": source_path, "error": str(e)}
            )
    
    async def _process_document(
        self,
        document_input: Union[str, bytes, Any],
        source_path: Optional[str] = None
    ) -> ProcessedContent:
        """Process document content with parsing and structuring."""
        
        try:
            # Extract text from document
            if isinstance(document_input, str) and os.path.isfile(document_input):
                text_content = await self._extract_text_from_file(document_input)
                source_path = source_path or document_input
            else:
                text_content = str(document_input)
            
            # Process as text
            text_result = await self._process_text(text_content, source_path)
            
            # Update metadata to reflect document processing
            text_result.metadata.update({
                'content_type': 'document',
                'original_type': 'document',
                'extraction_method': 'text_extraction'
            })
            
            text_result.content_type = ContentType.DOCUMENT
            
            return text_result
            
        except Exception as e:
            self.logger.error(f"Error processing document: {e}")
            return ProcessedContent(
                content_type=ContentType.DOCUMENT,
                text_content=f"[Document processing error: {str(e)}]",
                metadata={"source_path": source_path, "error": str(e)}
            )
    
    async def _process_pdf(self, content: bytes, source_path: Optional[str] = None) -> ProcessedContent:
        """Process PDF content."""
        if not PYPDF2_AVAILABLE:
            return ProcessedContent(
                content_type=ContentType.PDF,
                text_content="[PDF content - PyPDF2 not available]",
                metadata={"source_file": source_path, "error": "PyPDF2 not installed"}
            )
        
        try:
            pdf_file = io.BytesIO(content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            text_content = ""
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"
            
            metadata = {
                "source_file": source_path,
                "total_pages": len(pdf_reader.pages),
                "content_type": "pdf"
            }
            metadata.update(self._extract_metadata(text_content, source_path))
            
            return ProcessedContent(
                content_type=ContentType.PDF,
                text_content=text_content.strip(),
                metadata=metadata
            )
            
        except Exception as e:
            return ProcessedContent(
                content_type=ContentType.PDF,
                text_content=f"[PDF processing error: {str(e)}]",
                metadata={"source_file": source_path, "error": str(e)}
            )
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        # Remove excessive whitespace
        text = ' '.join(text.split())
        
        # Remove control characters
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
        
        return text.strip()
    
    def _chunk_text(self, text: str) -> List[str]:
        """Split text into chunks with overlap."""
        if len(text) <= self.max_text_chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.max_text_chunk_size
            
            # Try to break at sentence boundary
            if end < len(text):
                # Look for sentence ending
                for i in range(min(100, len(text) - end)):
                    if text[end + i] in '.!?':
                        end = end + i + 1
                        break
                else:
                    # Look for word boundary
                    while end > start and text[end] != ' ':
                        end -= 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - self.text_overlap
        
        return chunks
    
    def _detect_language(self, text: str) -> str:
        """Detect language of text (simplified implementation)."""
        # Very basic language detection
        # In production, use langdetect or similar library
        
        # Check for common English words
        english_words = ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with']
        words = text.lower().split()
        english_count = sum(1 for word in words if word in english_words)
        
        if len(words) > 0 and english_count / len(words) > 0.1:
            return 'english'
        else:
            return 'unknown'
    
    async def _extract_image_features(self, image: Any) -> Dict[str, Any]:
        """Extract basic features from image."""
        features = {}
        
        try:
            # Basic image statistics
            if PIL_AVAILABLE:
                # Convert to numpy array for analysis
                img_array = np.array(image)
                
                features.update({
                    'mean_brightness': float(np.mean(img_array)),
                    'std_brightness': float(np.std(img_array)),
                    'has_transparency': image.mode in ['RGBA', 'LA'],
                    'aspect_ratio': image.size[0] / image.size[1] if image.size[1] > 0 else 1.0
                })
                
                # Color analysis
                if len(img_array.shape) == 3:  # Color image
                    features.update({
                        'mean_red': float(np.mean(img_array[:, :, 0])),
                        'mean_green': float(np.mean(img_array[:, :, 1])),
                        'mean_blue': float(np.mean(img_array[:, :, 2]))
                    })
                
                # Simple edge detection if OpenCV available
                if OPENCV_AVAILABLE:
                    gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                    edges = cv2.Canny(gray, 100, 200)
                    features['edge_density'] = float(np.sum(edges > 0) / edges.size)
                
        except Exception as e:
            self.logger.error(f"Error extracting image features: {e}")
            features['extraction_error'] = str(e)
        
        return features
    
    async def _extract_text_from_file(self, file_path: str) -> str:
        """Extract text from various file formats."""
        ext = Path(file_path).suffix.lower().lstrip('.')
        
        try:
            if ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            elif ext == '.md':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            elif ext == '.pdf':
                # Would use PyPDF2 or pdfplumber in production
                return f"PDF content from {file_path} (PDF parsing not implemented)"
            
            elif ext in ['.doc', '.docx']:
                # Would use python-docx in production
                return f"Document content from {file_path} (DOC parsing not implemented)"
            
            else:
                # Try reading as text
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return f.read()
                    
        except Exception as e:
            self.logger.error(f"Error extracting text from {file_path}: {e}")
            return f"Error extracting text: {e}"
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    async def get_content_summary(self, processed_content: ProcessedContent) -> Dict[str, Any]:
        """Get summary of processed content."""
        summary = {
            'content_type': processed_content.content_type.value,
            'metadata': processed_content.metadata
        }
        
        if processed_content.content_type == ContentType.TEXT:
            chunks = processed_content.text_content.split('\n')
            summary.update({
                'total_chunks': len(chunks),
                'total_length': sum(len(chunk) for chunk in chunks),
                'average_chunk_length': np.mean([len(chunk) for chunk in chunks]) if chunks else 0
            })
        
        elif processed_content.content_type == ContentType.IMAGE:
            content = processed_content.text_content
            if isinstance(content, dict) and 'size' in content:
                summary.update({
                    'image_size': content['size'],
                    'has_features': 'features' in content,
                    'feature_count': len(content.get('features', {}))
                })
        
        return summary
    
    async def batch_process(
        self,
        content_list: List[Tuple[Any, Optional[ContentType], Optional[str]]]
    ) -> List[ProcessedContent]:
        """Process multiple content items in batch."""
        results = []
        
        for content, content_type, source_path in content_list:
            try:
                result = await self.process_content(content, content_type, source_path)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Error processing content item: {e}")
                # Add error result
                results.append(ProcessedContent(
                    content_type=content_type or ContentType.TEXT,
                    text_content=f"[Processing error: {str(e)}]",
                    metadata={"source_path": source_path, "error": str(e), "processing_timestamp": self._get_timestamp()}
                ))
        
        return results 

__all__ = ['ContentType', 'ProcessedContent', 'MultimodalProcessor'] 