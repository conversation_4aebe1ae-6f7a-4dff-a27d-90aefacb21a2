"""
Enhanced Specialist flow implementations using CrewAI 2025 Flows + Quick Wins.

Implements Researcher, Librarian, and DataProcessor flows with
Phase 0 Quick Wins integration: ColBERTv2, ReAct agents, PythonInterpreter,
reliability wrappers, and professional tools.
"""

import uuid
import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

# Import debug logging
from ...infrastructure.monitoring.debug_logger import get_debug_logger

from crewai.flow.flow import Flow, listen, start
from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from pydantic import BaseModel, Field

from ...core.models.state_models import WorkflowState, ResearchResult, LibraryResult, AnalysisResult
from ...core.interfaces.flow_interface import ISpecialist<PERSON>low, FlowStatus
from ...infrastructure.config.settings import get_llm_for_agent, enhance_backstory_with_current_date

# QUICK WINS INTEGRATION: Import enhanced specialists
from ...agents.specialists.search_specialist import EnhancedSearchSpecialist
from ...agents.specialists.knowledge_specialist import MathEnabledKnowledgeSpecialist
from ...agents.specialists.react_search_specialist import ReAct<PERSON>earchSpecialist
from ...agents.specialists.react_knowledge_specialist import <PERSON>Act<PERSON>nowledgeSpecialist
from ...optimization.dspy.reliability_wrapper import make_agents_reliable
from ...tools.crewai_tools_integration import ProfessionalToolsManager

# Standard tools for fallback
from ...tools.search.web_search_tool import WebSearchTool
from ...tools.processors.document_retrieval_tool import DocumentRetrievalTool
from ...tools.processors.data_analysis_tool import DataAnalysisTool


class SpecialistState(BaseModel):
    """Enhanced state for specialist flows with quick wins integration."""
    query: str = ""
    execution_plan: List[Dict[str, Any]] = Field(default_factory=list)
    current_iteration: int = 0
    max_iterations: int = 3
    context: Dict[str, Any] = Field(default_factory=dict)
    results: List[Dict[str, Any]] = Field(default_factory=list)
    final_output: str = ""
    
    # Quick wins enhancements
    enhanced_agents_used: List[str] = Field(default_factory=list)
    reliability_stats: Dict[str, Any] = Field(default_factory=dict)
    tools_performance: Dict[str, float] = Field(default_factory=dict)


class EnhancedResearcherFlow(Flow[SpecialistState]):
    """
    Enhanced Researcher flow with Quick Wins integration.
    
    Features:
    - ColBERTv2 professional-grade search (EnhancedSearchSpecialist)
    - ReAct reasoning and action capabilities
    - Reliability wrappers with automatic retry and validation
    - Professional CrewAI tools integration
    """
    
    def __init__(self):
        # Initialize attributes first
        self._flow_id = str(uuid.uuid4())
        self._status = FlowStatus.PENDING
        self._specialist_type = "Enhanced Researcher"
        
        # QUICK WINS: Initialize professional tools manager
        self._tools_manager = ProfessionalToolsManager()
        
        # QUICK WINS: Create enhanced agents with proper config
        from ...core.interfaces.agent_interface import AgentConfig
        
        config = AgentConfig(
            max_iterations=5,
            timeout_seconds=300,
            parallel_tools=True,
            retry_attempts=3,
            delegation_enabled=True
        )
        
        # Initialize enhanced specialists
        self._enhanced_search = EnhancedSearchSpecialist(config)
        self._react_search = ReActSearchSpecialist(config)
        
        # Initialize debug logging
        self._debug_logger = get_debug_logger()
        
        # DSPy OPTIMIZATION: Initialize specialist optimizer for enhanced flow
        try:
            from ...optimization.dspy.specialist_optimizer import get_specialist_optimizer
            self._specialist_optimizer = get_specialist_optimizer({
                'dspy_optimization_enabled': True,
                'enhanced_flow': True
            })
            print("🔧 [DSPy-Enhanced] Specialist optimizer initialized for enhanced flow")
            
            # DEBUG: Log specialist optimization
            self._debug_logger.log_specialist_flow(
                'EnhancedResearcherFlow', True, True, 
                {'optimizer_type': 'specialist_optimizer'}, None
            )
        except Exception as e:
            error_msg = str(e)
            print(f"⚠️ [DSPy-Enhanced] Specialist optimizer unavailable: {error_msg[:50]}...")
            self._specialist_optimizer = None
            
            # DEBUG: Log specialist optimization failure
            self._debug_logger.log_specialist_flow(
                'EnhancedResearcherFlow', False, False, 
                {'attempted': True}, error_msg
            )
        
        # QUICK WINS: Apply reliability wrappers
        reliable_agents = make_agents_reliable({
            "enhanced_search": self._enhanced_search,
            "react_search": self._react_search
        }, min_answer_length=50, confidence_threshold=0.7)
        
        self._reliable_enhanced_search = reliable_agents["enhanced_search"]
        self._reliable_react_search = reliable_agents["react_search"]
        
        # Standard tools for CrewAI agent
        self._standard_tools = [
            WebSearchTool(),
            *self._tools_manager.get_all_tools()  # Add professional tools
        ]
        
        # Initialize hybrid researcher agent (CrewAI + enhanced capabilities)
        self._researcher_agent = self._create_enhanced_researcher_agent()
        
        # Call super().__init__() after setting attributes
        super().__init__()
    
    def _create_enhanced_researcher_agent(self) -> Agent:
        """Create enhanced research specialist agent with professional tools."""
        from ...infrastructure.config.settings import enhance_backstory_with_current_date
        
        original_backstory = """You are an expert research specialist enhanced with cutting-edge AI capabilities. 
        You have access to ColBERTv2 professional-grade retrieval for high-quality document search,
        ReAct reasoning for step-by-step problem solving, and professional web search tools.
        You excel at finding current information, validating sources, and synthesizing research findings
        with advanced reasoning capabilities."""
        
        return Agent(
            role="Enhanced Research Specialist with ColBERTv2 & ReAct",
            goal="""Conduct comprehensive research using professional-grade tools including ColBERTv2 retrieval, 
            ReAct reasoning, and advanced web search capabilities to gather the most current and relevant information""",
            backstory=enhance_backstory_with_current_date(original_backstory),
            verbose=True,
            allow_delegation=False,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("researcher"),
            tools=self._standard_tools
        )
    
    @start()
    def initialize_enhanced_research(self):
        """Initialize research with quick wins enhancements."""
        print("🔬 Enhanced Researcher: Initializing with Quick Wins...")
        print("   ✅ ColBERTv2 professional-grade retrieval active")
        print("   ✅ ReAct reasoning and action capabilities active") 
        print("   ✅ Reliability wrappers applied")
        print("   ✅ Professional CrewAI tools integrated")
        
        # Extract query from inputs
        inputs = getattr(self, '_inputs', {})
        query = inputs.get('query', self.state.query)
        
        if not query:
            query = "latest developments in technology"
        
        self.state.query = query
        self.state.enhanced_agents_used.append("ColBERTv2_Enhanced_Search")
        self.state.enhanced_agents_used.append("ReAct_Search_Specialist")
        
        print(f"🎯 Enhanced research target: {query}")
        return {"initialized": True, "query": query, "enhancements": "quick_wins_active"}
    
    @listen(initialize_enhanced_research)
    async def conduct_colbert_research(self):
        """Conduct research using ColBERTv2 enhanced search."""
        print("🔍 Phase 1: ColBERTv2 Professional-Grade Search...")
        self.state.current_iteration = 1
        
        try:
            # Use enhanced search specialist with ColBERTv2
            from ...core.interfaces.task_interface import TaskSpec, TaskType
            
            enhanced_task = TaskSpec(
                task_id=f"colbert_search_{self._flow_id}",
                task_type=TaskType.RESEARCH,
                description=self.state.query,
                context={
                    "focus": "comprehensive_research",
                    "quality_threshold": 0.8,
                    "max_sources": 10
                }
            )
            
            enhanced_result = await self._reliable_enhanced_search.execute_task(enhanced_task)
            
            if enhanced_result.is_success:
                colbert_data = enhanced_result.output
                
                # Extract research details
                research_result = ResearchResult(
                    query=self.state.query,
                    summary=colbert_data.get('answer', ''),
                    key_findings=colbert_data.get('sources', [])[:5],
                    confidence_score=colbert_data.get('confidence', 0.8),
                    agent_id=f"ColBERTv2_{self._flow_id}",
                    sources=colbert_data.get('sources', [])
                )
                
                self.state.results.append(research_result.model_dump())
                self.state.tools_performance["colbert_v2"] = colbert_data.get('confidence', 0.8)
                
                print(f"✅ ColBERTv2 search completed - confidence: {colbert_data.get('confidence', 0.8)}")
                print(f"   📊 Retrieved {colbert_data.get('retrieved_documents', 0)} professional documents")
                print(f"   🔗 Found {len(colbert_data.get('sources', []))} high-quality sources")
                
                return {"colbert_complete": True, "confidence": colbert_data.get('confidence', 0.8)}
            else:
                print(f"⚠️ ColBERTv2 search failed: {enhanced_result.error}")
                return {"colbert_complete": False, "error": enhanced_result.error}
                
        except Exception as e:
            error_msg = f"ColBERTv2 enhanced search failed: {str(e)}"
            print(f"❌ {error_msg}")
            return {"colbert_complete": False, "error": error_msg}
    
    @listen(conduct_colbert_research)
    async def conduct_react_research(self):
        """Conduct research using ReAct reasoning and action with DSPy optimization."""
        print("🤖 Phase 2: ReAct Reasoning and Action Research (DSPy Enhanced)...")
        self.state.current_iteration = 2
        
        try:
            # DSPy OPTIMIZATION: Compile ReAct specialist if optimizer available
            react_specialist = self._reliable_react_search
            
            if self._specialist_optimizer:
                print("🔧 [DSPy-Enhanced] Compiling ReAct specialist...")
                react_specialist = await self._specialist_optimizer.compile_specialist(
                    specialist_module=self._react_search,  # Use original, not reliability wrapper
                    specialist_name="ReActSearchSpecialist",
                    context={
                        'query': self.state.query,
                        'flow_type': 'enhanced_researcher',
                        'previous_research': len(self.state.results)
                    }
                )
                print("🔧 [DSPy-Enhanced] ReAct specialist compilation complete")
            
            # Use ReAct search specialist for reasoning-based research
            from ...core.interfaces.task_interface import TaskSpec, TaskType
            
            react_task = TaskSpec(
                task_id=f"react_search_{self._flow_id}",
                task_type=TaskType.RESEARCH,
                description=f"Use step-by-step reasoning to research: {self.state.query}",
                context={
                    "reasoning_required": True,
                    "tool_usage": True,
                    "max_reasoning_steps": 8,
                    "previous_research": self.state.results[-1] if self.state.results else {},
                    "dspy_optimized": self._specialist_optimizer is not None
                }
            )
            
            react_result = await react_specialist.execute_task(react_task)
            
            if react_result.is_success:
                react_data = react_result.output
                
                # Extract ReAct research details
                research_result = ResearchResult(
                    query=self.state.query,
                    summary=react_data.get('answer', ''),
                    key_findings=react_data.get('reasoning_steps', [])[:5],
                    confidence_score=react_data.get('confidence', 0.8),
                    agent_id=f"ReAct_{self._flow_id}",
                    sources=react_data.get('sources', [])
                )
                
                self.state.results.append(research_result.model_dump())
                self.state.tools_performance["react_reasoning"] = react_data.get('confidence', 0.8)
                
                # DSPy OPTIMIZATION: Collect training example for specialist
                if self._specialist_optimizer:
                    await self._specialist_optimizer.collect_specialist_example(
                        specialist_name="ReActSearchSpecialist",
                        query=self.state.query,
                        result={
                            'success': True,
                            'confidence': react_data.get('confidence', 0.8),
                            'answer': react_data.get('answer', ''),
                            'reasoning_steps': len(react_data.get('reasoning_steps', [])),
                            'method': react_data.get('method', 'unknown')
                        },
                        session_id=self._flow_id
                    )
                
                print(f"✅ [DSPy-Enhanced] ReAct research completed - confidence: {react_data.get('confidence', 0.8)}")
                print(f"   🧠 Reasoning steps: {len(react_data.get('reasoning_steps', []))}")
                print(f"   🔧 Tools used: {react_data.get('method', 'unknown')}")
                
                return {"react_complete": True, "reasoning_steps": len(react_data.get('reasoning_steps', []))}
            else:
                # DSPy OPTIMIZATION: Collect failure example too
                if self._specialist_optimizer:
                    await self._specialist_optimizer.collect_specialist_example(
                        specialist_name="ReActSearchSpecialist",
                        query=self.state.query,
                        result={
                            'success': False,
                            'confidence': 0.0,
                            'error': react_result.error
                        },
                        session_id=self._flow_id
                    )
                
                print(f"⚠️ [DSPy-Enhanced] ReAct research failed: {react_result.error}")
                return {"react_complete": False, "error": react_result.error}
                
        except Exception as e:
            error_msg = f"ReAct reasoning research failed: {str(e)}"
            print(f"❌ [DSPy-Enhanced] {error_msg}")
            return {"react_complete": False, "error": error_msg}
    
    @listen(conduct_react_research)
    def synthesize_enhanced_research(self):
        """Synthesize research using standard CrewAI with professional tools."""
        print("📝 Phase 3: Professional Synthesis with CrewAI Tools...")
        self.state.current_iteration = 3
        
        # Combine all research results
        all_research = ""
        for result in self.state.results:
            all_research += f"\n\nSource: {result.get('agent_id', 'Unknown')}\n"
            all_research += f"Summary: {result.get('summary', '')}\n"
            all_research += f"Key Findings: {', '.join(result.get('key_findings', []))}\n"
            all_research += f"Confidence: {result.get('confidence_score', 0.0)}\n"
        
        synthesis_task = Task(
            description=f"""
            Synthesize the following enhanced research findings into a comprehensive report:
            
            Original Query: "{self.state.query}"
            
            Enhanced Research Results:
            {all_research}
            
            Your synthesis should:
            1. Combine insights from ColBERTv2 professional-grade retrieval
            2. Integrate ReAct reasoning and step-by-step analysis
            3. Validate information quality and confidence scores
            4. Create a comprehensive, well-structured summary
            5. Highlight the most reliable and current information
            6. Note the advanced methods used in research
            
            Use the professional web search tools if additional verification is needed.
            """,
            expected_output="Comprehensive research synthesis combining enhanced AI capabilities",
            agent=self._researcher_agent,
            tools=self._standard_tools
        )
        
        crew = Crew(
            agents=[self._researcher_agent],
            tasks=[synthesis_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        
        # Create final synthesis result
        synthesis_result = ResearchResult(
            query=self.state.query,
            summary=result.raw,
            key_findings=self._extract_key_findings(result.raw),
            confidence_score=self._calculate_combined_confidence(),
            agent_id=f"Enhanced_Synthesis_{self._flow_id}",
            sources=self._collect_all_sources()
        )
        
        self.state.results.append(synthesis_result.model_dump())
        self.state.final_output = result.raw
        
        # Collect reliability statistics
        self.state.reliability_stats = {
            "enhanced_search_stats": self._reliable_enhanced_search.get_validation_stats(),
            "react_search_stats": self._reliable_react_search.get_validation_stats(),
            "total_results": len(self.state.results),
            "average_confidence": self._calculate_combined_confidence()
        }
        
        print("✅ Enhanced research synthesis completed")
        print(f"   📊 Combined confidence: {self._calculate_combined_confidence():.2f}")
        print(f"   🔧 Enhanced agents used: {len(self.state.enhanced_agents_used)}")
        print(f"   📈 Reliability stats available")
        
        return {"synthesis_complete": True, "enhanced_features_used": True}
    
    def _extract_key_findings(self, research_text: str) -> List[str]:
        """Extract key findings from research text."""
        # Simple extraction based on common patterns
        findings = []
        lines = research_text.split('\n')
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['key', 'important', 'significant', 'finding', 'conclusion']):
                if len(line) > 20 and len(line) < 200:
                    findings.append(line)
        return findings[:5]  # Return top 5 findings
    
    def _calculate_combined_confidence(self) -> float:
        """Calculate combined confidence from all research results."""
        if not self.state.results:
            return 0.0
        
        confidences = [result.get('confidence_score', 0.0) for result in self.state.results]
        return sum(confidences) / len(confidences)
    
    def _collect_all_sources(self) -> List[str]:
        """Collect all sources from research results."""
        all_sources = []
        for result in self.state.results:
            sources = result.get('sources', [])
            if isinstance(sources, list):
                all_sources.extend(sources)
            elif isinstance(sources, str):
                all_sources.append(sources)
        return list(set(all_sources))  # Remove duplicates
    
    async def kickoff_async(self, inputs=None):
        """Enhanced async kickoff with quick wins integration."""
        self._inputs = inputs
        print(f"🚀 Starting Enhanced Researcher Flow with Quick Wins")
        
        # Set query from inputs
        if inputs and 'query' in inputs:
            self.state.query = inputs['query']
        
        # Execute the flow
        result = await super().kickoff_async(inputs)
        
        print(f"✅ Enhanced Researcher Flow completed with {len(self.state.results)} results")
        
        # Return structured data for main workflow
        return {
            'flow_type': 'research',
            'results': self.state.results,
            'final_output': self.state.final_output,
            'enhanced_agents_used': self.state.enhanced_agents_used,
            'reliability_stats': self.state.reliability_stats,
            'tools_performance': self.state.tools_performance,
            'success': len(self.state.results) > 0
        }


# Keep the old name for compatibility but use enhanced version
ResearcherFlow = EnhancedResearcherFlow


class EnhancedLibrarianFlow(Flow[SpecialistState]):
    """
    Enhanced Librarian flow with Quick Wins integration.
    
    Features:
    - Math and code execution capabilities (MathEnabledKnowledgeSpecialist)
    - ReAct reasoning for complex document analysis
    - Reliability wrappers with automatic retry and validation
    - Professional CrewAI tools for document processing
    """
    
    def __init__(self):
        # Initialize attributes first
        self._flow_id = str(uuid.uuid4())
        self._status = FlowStatus.PENDING
        self._specialist_type = "Enhanced Librarian"
        
        # QUICK WINS: Initialize professional tools manager
        self._tools_manager = ProfessionalToolsManager()
        
        # QUICK WINS: Create enhanced agents with proper config
        from ...core.interfaces.agent_interface import AgentConfig
        
        config = AgentConfig(
            max_iterations=5,
            timeout_seconds=300,
            parallel_tools=True,
            retry_attempts=3,
            delegation_enabled=True
        )
        
        # Initialize enhanced knowledge specialists
        self._math_knowledge = MathEnabledKnowledgeSpecialist(config)
        self._react_knowledge = ReActKnowledgeSpecialist(config)
        
        # DSPy OPTIMIZATION: Initialize specialist optimizer for enhanced flow
        try:
            from ...optimization.dspy.specialist_optimizer import get_specialist_optimizer
            self._specialist_optimizer = get_specialist_optimizer({
                'dspy_optimization_enabled': True,
                'enhanced_flow': True
            })
            print("🔧 [DSPy-Enhanced] Specialist optimizer initialized for librarian flow")
        except Exception as e:
            print(f"⚠️ [DSPy-Enhanced] Specialist optimizer unavailable: {str(e)[:50]}...")
            self._specialist_optimizer = None
        
        # QUICK WINS: Apply reliability wrappers
        reliable_agents = make_agents_reliable({
            "math_knowledge": self._math_knowledge,
            "react_knowledge": self._react_knowledge
        }, min_answer_length=30, confidence_threshold=0.6)
        
        self._reliable_math_knowledge = reliable_agents["math_knowledge"]
        self._reliable_react_knowledge = reliable_agents["react_knowledge"]
        
        # Standard tools for CrewAI agent
        self._standard_tools = [
            DocumentRetrievalTool(),
            *self._tools_manager.get_all_tools()  # Add professional tools
        ]
        
        # Initialize hybrid librarian agent
        self._librarian_agent = self._create_enhanced_librarian_agent()
        
        # Call super().__init__() after setting attributes
        super().__init__()
    
    def _create_enhanced_librarian_agent(self) -> Agent:
        """Create enhanced librarian specialist agent with mathematical capabilities."""
        
        original_backstory = """You are an expert knowledge librarian enhanced with mathematical and computational 
        capabilities. You have access to advanced mathematical processing, code execution capabilities,
        ReAct reasoning for complex problem solving, and comprehensive document retrieval systems.
        You excel at finding, analyzing, and synthesizing knowledge from various sources while applying
        mathematical rigor and computational validation."""
        
        return Agent(
            role="Enhanced Knowledge Librarian with Mathematical Capabilities",
            goal="""Retrieve and analyze knowledge using mathematical processing, code execution, and ReAct reasoning 
            to provide comprehensive and validated information""",
            backstory=enhance_backstory_with_current_date(original_backstory),
            verbose=True,
            allow_delegation=False,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("librarian"),
            tools=self._standard_tools
        )
    
    @start()
    def initialize_enhanced_knowledge_search(self):
        """Initialize document search with enhanced capabilities."""
        print("📚 Enhanced Librarian: Initializing with Quick Wins...")
        print("   ✅ Mathematical computation capabilities active")
        print("   ✅ ReAct reasoning for complex analysis active")
        print("   ✅ Reliability wrappers applied")
        print("   ✅ Professional document processing tools integrated")
        
        # Extract query from inputs
        inputs = getattr(self, '_inputs', {})
        query = inputs.get('query', self.state.query)
        
        if not query:
            query = "knowledge synthesis and analysis"
        
        self.state.query = query
        self.state.enhanced_agents_used.append("Math_Enabled_Knowledge")
        self.state.enhanced_agents_used.append("ReAct_Knowledge_Specialist")
        
        print(f"🎯 Enhanced knowledge target: {query}")
        return {"initialized": True, "query": query, "enhancements": "math_react_active"}
    
    @listen(initialize_enhanced_knowledge_search)
    async def conduct_mathematical_analysis(self):
        """Conduct analysis using mathematical and computational capabilities."""
        print("🧮 Phase 1: Mathematical & Computational Analysis...")
        self.state.current_iteration = 1
        
        try:
            # Use math-enabled knowledge specialist
            from ...core.interfaces.task_interface import TaskSpec, TaskType
            
            math_task = TaskSpec(
                task_id=f"math_analysis_{self._flow_id}",
                task_type=TaskType.ANALYSIS,
                description=self.state.query,
                context={
                    "analysis_type": "mathematical_computational",
                    "enable_code_execution": True,
                    "require_calculations": True
                }
            )
            
            math_result = await self._reliable_math_knowledge.execute_task(math_task)
            
            if math_result.is_success:
                math_data = math_result.output
                
                # Extract analysis details
                library_result = LibraryResult(
                    query=self.state.query,
                    documents=[{"source": "Mathematical Analysis", "content": math_data.get('answer', '')}],
                    relevant_excerpts=math_data.get('knowledge_areas', [])[:3],
                    document_count=1,
                    relevance_scores=[math_data.get('confidence', 0.8)],
                    agent_id=f"MathKnowledge_{self._flow_id}"
                )
                
                self.state.results.append(library_result.model_dump())
                self.state.tools_performance["math_computation"] = math_data.get('confidence', 0.8)
                
                print(f"✅ Mathematical analysis completed - confidence: {math_data.get('confidence', 0.8)}")
                print(f"   🧮 Knowledge areas: {len(math_data.get('knowledge_areas', []))}")
                print(f"   🔧 Computational analysis performed")
                
                return {"math_complete": True, "confidence": math_data.get('confidence', 0.8)}
            else:
                print(f"⚠️ Mathematical analysis failed: {math_result.error}")
                return {"math_complete": False, "error": math_result.error}
                
        except Exception as e:
            error_msg = f"Mathematical analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            return {"math_complete": False, "error": error_msg}
    
    @listen(conduct_mathematical_analysis)
    async def conduct_react_knowledge_synthesis(self):
        """Conduct knowledge synthesis using ReAct reasoning with DSPy optimization."""
        print("🤖 Phase 2: ReAct Knowledge Synthesis (DSPy Enhanced)...")
        self.state.current_iteration = 2
        
        try:
            # DSPy OPTIMIZATION: Compile ReAct knowledge specialist if optimizer available
            react_specialist = self._reliable_react_knowledge
            
            if self._specialist_optimizer:
                print("🔧 [DSPy-Enhanced] Compiling ReAct knowledge specialist...")
                react_specialist = await self._specialist_optimizer.compile_specialist(
                    specialist_module=self._react_knowledge,  # Use original, not reliability wrapper
                    specialist_name="ReActKnowledgeSpecialist",
                    context={
                        'query': self.state.query,
                        'flow_type': 'enhanced_librarian',
                        'previous_analysis': len(self.state.results)
                    }
                )
                print("🔧 [DSPy-Enhanced] ReAct knowledge specialist compilation complete")
            
            # Use ReAct knowledge specialist for reasoning-based synthesis
            from ...core.interfaces.task_interface import TaskSpec, TaskType
            
            react_task = TaskSpec(
                task_id=f"react_knowledge_{self._flow_id}",
                task_type=TaskType.ANALYSIS,
                description=f"Use step-by-step reasoning to synthesize knowledge about: {self.state.query}",
                context={
                    "reasoning_required": True,
                    "synthesis_mode": True,
                    "max_reasoning_steps": 10,
                    "previous_analysis": self.state.results[-1] if self.state.results else {},
                    "dspy_optimized": self._specialist_optimizer is not None
                }
            )
            
            react_result = await react_specialist.execute_task(react_task)
            
            if react_result.is_success:
                react_data = react_result.output
                
                # Extract ReAct knowledge details
                library_result = LibraryResult(
                    query=self.state.query,
                    documents=[{"source": "ReAct Knowledge Synthesis", "content": react_data.get('answer', '')}],
                    relevant_excerpts=react_data.get('sources', [])[:5],
                    document_count=1,
                    relevance_scores=[react_data.get('confidence', 0.8)],
                    agent_id=f"ReActKnowledge_{self._flow_id}"
                )
                
                self.state.results.append(library_result.model_dump())
                self.state.tools_performance["react_knowledge"] = react_data.get('confidence', 0.8)
                
                # DSPy OPTIMIZATION: Collect training example for knowledge specialist
                if self._specialist_optimizer:
                    await self._specialist_optimizer.collect_specialist_example(
                        specialist_name="ReActKnowledgeSpecialist",
                        query=self.state.query,
                        result={
                            'success': True,
                            'confidence': react_data.get('confidence', 0.8),
                            'answer': react_data.get('answer', ''),
                            'methodology': react_data.get('methodology', 'unknown'),
                            'sources_count': len(react_data.get('sources', []))
                        },
                        session_id=self._flow_id
                    )
                
                print(f"✅ [DSPy-Enhanced] ReAct knowledge synthesis completed - confidence: {react_data.get('confidence', 0.8)}")
                print(f"   🧠 Methodology: {react_data.get('methodology', 'unknown')}")
                print(f"   🔗 Sources: {len(react_data.get('sources', []))}")
                
                return {"react_complete": True, "methodology": react_data.get('methodology', 'unknown')}
            else:
                # DSPy OPTIMIZATION: Collect failure example too
                if self._specialist_optimizer:
                    await self._specialist_optimizer.collect_specialist_example(
                        specialist_name="ReActKnowledgeSpecialist",
                        query=self.state.query,
                        result={
                            'success': False,
                            'confidence': 0.0,
                            'error': react_result.error
                        },
                        session_id=self._flow_id
                    )
                
                print(f"⚠️ [DSPy-Enhanced] ReAct knowledge synthesis failed: {react_result.error}")
                return {"react_complete": False, "error": react_result.error}
                
        except Exception as e:
            error_msg = f"ReAct knowledge synthesis failed: {str(e)}"
            print(f"❌ [DSPy-Enhanced] {error_msg}")
            return {"react_complete": False, "error": error_msg}
    
    @listen(conduct_react_knowledge_synthesis)
    def synthesize_enhanced_knowledge(self):
        """Synthesize knowledge using standard CrewAI with professional tools."""
        print("📝 Phase 3: Professional Knowledge Synthesis...")
        self.state.current_iteration = 3
        
        # Combine all knowledge results
        all_knowledge = ""
        for result in self.state.results:
            documents = result.get('documents', [])
            for doc in documents:
                all_knowledge += f"\n\nSource: {doc.get('source', 'Unknown')}\n"
                all_knowledge += f"Content: {doc.get('content', '')}\n"
            all_knowledge += f"Excerpts: {', '.join(result.get('relevant_excerpts', []))}\n"
            all_knowledge += f"Relevance: {result.get('relevance_scores', [0.0])}\n"
        
        synthesis_task = Task(
            description=f"""
            Synthesize the following enhanced knowledge findings into a comprehensive analysis:
            
            Original Query: "{self.state.query}"
            
            Enhanced Knowledge Results:
            {all_knowledge}
            
            Your synthesis should:
            1. Combine insights from mathematical and computational analysis
            2. Integrate ReAct reasoning and step-by-step knowledge synthesis
            3. Validate information quality and relevance scores
            4. Create a comprehensive, well-structured knowledge summary
            5. Highlight mathematical relationships and computational insights
            6. Note the advanced analytical methods used
            
            Use the professional document processing tools if additional analysis is needed.
            """,
            expected_output="Comprehensive knowledge synthesis combining mathematical and reasoning capabilities",
            agent=self._librarian_agent,
            tools=self._standard_tools
        )
        
        crew = Crew(
            agents=[self._librarian_agent],
            tasks=[synthesis_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        
        # Create final synthesis result
        synthesis_result = LibraryResult(
            query=self.state.query,
            documents=[{"source": "Enhanced Knowledge Synthesis", "content": result.raw}],
            relevant_excerpts=self._extract_excerpts(result.raw),
            document_count=len(self.state.results) + 1,
            relevance_scores=[self._calculate_combined_relevance()],
            agent_id=f"Enhanced_Knowledge_Synthesis_{self._flow_id}"
        )
        
        self.state.results.append(synthesis_result.model_dump())
        self.state.final_output = result.raw
        
        # Collect reliability statistics
        self.state.reliability_stats = {
            "math_knowledge_stats": self._reliable_math_knowledge.get_validation_stats(),
            "react_knowledge_stats": self._reliable_react_knowledge.get_validation_stats(),
            "total_results": len(self.state.results),
            "average_relevance": self._calculate_combined_relevance()
        }
        
        print("✅ Enhanced knowledge synthesis completed")
        print(f"   📊 Combined relevance: {self._calculate_combined_relevance():.2f}")
        print(f"   🔧 Enhanced agents used: {len(self.state.enhanced_agents_used)}")
        print(f"   📈 Reliability stats available")
        
        return {"synthesis_complete": True, "enhanced_features_used": True}
    
    def _extract_excerpts(self, knowledge_text: str) -> List[str]:
        """Extract relevant excerpts from knowledge text."""
        excerpts = []
        sentences = knowledge_text.split('.')
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 30 and len(sentence) < 150:
                if any(keyword in sentence.lower() for keyword in ['important', 'key', 'significant', 'analysis', 'conclusion']):
                    excerpts.append(sentence + '.')
        return excerpts[:5]  # Return top 5 excerpts
    
    def _calculate_combined_relevance(self) -> float:
        """Calculate combined relevance from all knowledge results."""
        if not self.state.results:
            return 0.0
        
        all_scores = []
        for result in self.state.results:
            scores = result.get('relevance_scores', [])
            all_scores.extend(scores)
        
        return sum(all_scores) / len(all_scores) if all_scores else 0.0
    
    async def kickoff_async(self, inputs=None):
        """Enhanced async kickoff with quick wins integration."""
        self._inputs = inputs
        print(f"🚀 Starting Enhanced Librarian Flow with Quick Wins")
        
        # Set query from inputs
        if inputs and 'query' in inputs:
            self.state.query = inputs['query']
        
        # Execute the flow
        result = await super().kickoff_async(inputs)
        
        print(f"✅ Enhanced Librarian Flow completed with {len(self.state.results)} results")
        
        # Return structured data for main workflow
        return {
            'flow_type': 'library',
            'results': self.state.results,
            'final_output': self.state.final_output,
            'enhanced_agents_used': self.state.enhanced_agents_used,
            'reliability_stats': self.state.reliability_stats,
            'tools_performance': self.state.tools_performance,
            'success': len(self.state.results) > 0
        }


# Keep the old name for compatibility but use enhanced version
LibrarianFlow = EnhancedLibrarianFlow


class EnhancedDataProcessorFlow(Flow[SpecialistState]):
    """
    Enhanced Data Processor flow with Quick Wins integration.
    
    Features:
    - Mathematical computation and code execution capabilities
    - ReAct reasoning for complex data analysis
    - Reliability wrappers for consistent processing
    - Professional tools for data analysis
    """
    
    def __init__(self):
        # Initialize attributes first
        self._flow_id = str(uuid.uuid4())
        self._status = FlowStatus.PENDING
        self._specialist_type = "Enhanced Data Processor"
        
        # QUICK WINS: Initialize professional tools manager
        self._tools_manager = ProfessionalToolsManager()
        
        # QUICK WINS: Create enhanced agents with proper config
        from ...core.interfaces.agent_interface import AgentConfig
        
        config = AgentConfig(
            max_iterations=5,
            timeout_seconds=300,
            parallel_tools=True,
            retry_attempts=3,
            delegation_enabled=True
        )
        
        # Initialize enhanced specialists for data processing
        self._math_processor = MathEnabledKnowledgeSpecialist(config)
        self._react_processor = ReActKnowledgeSpecialist(config)
        
        # QUICK WINS: Apply reliability wrappers
        reliable_agents = make_agents_reliable({
            "math_processor": self._math_processor,
            "react_processor": self._react_processor
        }, min_answer_length=40, confidence_threshold=0.7)
        
        self._reliable_math_processor = reliable_agents["math_processor"]
        self._reliable_react_processor = reliable_agents["react_processor"]
        
        # Standard tools for CrewAI agent
        self._standard_tools = [
            DataAnalysisTool(),
            *self._tools_manager.get_all_tools()  # Add professional tools
        ]
        
        # Initialize hybrid data processor agent
        self._processor_agent = self._create_enhanced_processor_agent()
        
        # Call super().__init__() after setting attributes
        super().__init__()
    
    def _create_enhanced_processor_agent(self) -> Agent:
        """Create enhanced data processor agent with advanced analytics."""
        
        original_backstory = """You are an expert data processor enhanced with mathematical and computational 
        capabilities. You have access to advanced statistical analysis, mathematical processing, 
        ReAct reasoning for complex problem decomposition, and professional data analysis tools.
        You excel at processing complex datasets, performing statistical analysis, identifying patterns
        and trends, and providing data-driven insights."""
        
        return Agent(
            role="Enhanced Data Processor with Mathematical Analytics",
            goal="""Process and analyze data using advanced statistical methods, mathematical computation, and ReAct reasoning 
            to provide comprehensive data-driven insights and analysis""",
            backstory=enhance_backstory_with_current_date(original_backstory),
            verbose=True,
            allow_delegation=False,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("data_processor"),
            tools=self._standard_tools
        )
    
    @start()
    def initialize_enhanced_data_processing(self):
        """Initialize data processing with enhanced capabilities."""
        print("📊 Enhanced Data Processor: Initializing with Quick Wins...")
        print("   ✅ Mathematical computation and statistics active")
        print("   ✅ ReAct reasoning for complex analysis active")
        print("   ✅ Reliability wrappers applied")
        print("   ✅ Professional data processing tools integrated")
        
        # Extract query from inputs
        inputs = getattr(self, '_inputs', {})
        query = inputs.get('query', self.state.query)
        
        if not query:
            query = "data analysis and processing"
        
        self.state.query = query
        self.state.enhanced_agents_used.append("Math_Enabled_Processor")
        self.state.enhanced_agents_used.append("ReAct_Data_Processor")
        
        print(f"🎯 Enhanced data processing target: {query}")
        return {"initialized": True, "query": query, "enhancements": "math_react_data_active"}
    
    @listen(initialize_enhanced_data_processing)
    async def conduct_mathematical_data_analysis(self):
        """Conduct data analysis using mathematical and computational capabilities."""
        print("🧮 Phase 1: Mathematical & Statistical Data Analysis...")
        self.state.current_iteration = 1
        
        try:
            # Use math-enabled specialist for data processing
            from ...core.interfaces.task_interface import TaskSpec, TaskType
            
            math_task = TaskSpec(
                task_id=f"math_data_analysis_{self._flow_id}",
                task_type=TaskType.ANALYSIS,
                description=f"Perform mathematical and statistical analysis for: {self.state.query}",
                context={
                    "analysis_type": "statistical_mathematical",
                    "enable_code_execution": True,
                    "require_calculations": True,
                    "data_focus": True
                }
            )
            
            math_result = await self._reliable_math_processor.execute_task(math_task)
            
            if math_result.is_success:
                math_data = math_result.output
                
                # Extract analysis details
                analysis_result = AnalysisResult(
                    analysis_type="Mathematical Data Analysis",
                    input_data={"query": self.state.query},
                    findings=math_data.get('knowledge_areas', [])[:3],
                    insights=[math_data.get('answer', '')],
                    metrics={"confidence": math_data.get('confidence', 0.8)},
                    confidence_level=math_data.get('confidence', 0.8),
                    agent_id=f"MathDataProcessor_{self._flow_id}"
                )
                
                self.state.results.append(analysis_result.model_dump())
                self.state.tools_performance["math_data_analysis"] = math_data.get('confidence', 0.8)
                
                print(f"✅ Mathematical data analysis completed - confidence: {math_data.get('confidence', 0.8)}")
                print(f"   📊 Analysis areas: {len(math_data.get('knowledge_areas', []))}")
                print(f"   🔢 Statistical computations performed")
                
                return {"math_analysis_complete": True, "confidence": math_data.get('confidence', 0.8)}
            else:
                print(f"⚠️ Mathematical data analysis failed: {math_result.error}")
                return {"math_analysis_complete": False, "error": math_result.error}
                
        except Exception as e:
            error_msg = f"Mathematical data analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            return {"math_analysis_complete": False, "error": error_msg}
    
    @listen(conduct_mathematical_data_analysis)
    async def conduct_react_data_processing(self):
        """Conduct data processing using ReAct reasoning."""
        print("🤖 Phase 2: ReAct Data Processing & Insights...")
        self.state.current_iteration = 2
        
        try:
            # Use ReAct specialist for reasoning-based data processing
            from ...core.interfaces.task_interface import TaskSpec, TaskType
            
            react_task = TaskSpec(
                task_id=f"react_data_processing_{self._flow_id}",
                task_type=TaskType.ANALYSIS,
                description=f"Use step-by-step reasoning to analyze and process data for: {self.state.query}",
                context={
                    "reasoning_required": True,
                    "data_processing_mode": True,
                    "max_reasoning_steps": 8,
                    "previous_analysis": self.state.results[-1] if self.state.results else {}
                }
            )
            
            react_result = await self._reliable_react_processor.execute_task(react_task)
            
            if react_result.is_success:
                react_data = react_result.output
                
                # Extract ReAct processing details
                analysis_result = AnalysisResult(
                    analysis_type="ReAct Data Processing",
                    input_data={"query": self.state.query},
                    findings=react_data.get('sources', [])[:3],
                    insights=[react_data.get('answer', '')],
                    metrics={"confidence": react_data.get('confidence', 0.8), "methodology": react_data.get('methodology', '')},
                    confidence_level=react_data.get('confidence', 0.8),
                    agent_id=f"ReActDataProcessor_{self._flow_id}"
                )
                
                self.state.results.append(analysis_result.model_dump())
                self.state.tools_performance["react_data_processing"] = react_data.get('confidence', 0.8)
                
                print(f"✅ ReAct data processing completed - confidence: {react_data.get('confidence', 0.8)}")
                print(f"   🧠 Methodology: {react_data.get('methodology', 'unknown')}")
                print(f"   📈 Insights generated: {len(react_data.get('sources', []))}")
                
                return {"react_processing_complete": True, "methodology": react_data.get('methodology', 'unknown')}
            else:
                print(f"⚠️ ReAct data processing failed: {react_result.error}")
                return {"react_processing_complete": False, "error": react_result.error}
                
        except Exception as e:
            error_msg = f"ReAct data processing failed: {str(e)}"
            print(f"❌ {error_msg}")
            return {"react_processing_complete": False, "error": error_msg}
    
    @listen(conduct_react_data_processing)
    def synthesize_enhanced_data_insights(self):
        """Synthesize data insights using standard CrewAI with professional tools."""
        print("📝 Phase 3: Professional Data Insights Synthesis...")
        self.state.current_iteration = 3
        
        # Combine all analysis results
        all_analysis = ""
        for result in self.state.results:
            all_analysis += f"\n\nAnalysis Type: {result.get('analysis_type', 'Unknown')}\n"
            all_analysis += f"Findings: {', '.join(result.get('findings', []))}\n"
            all_analysis += f"Insights: {', '.join(result.get('insights', []))}\n"
            all_analysis += f"Confidence: {result.get('confidence_level', 0.0)}\n"
            all_analysis += f"Metrics: {result.get('metrics', {})}\n"
        
        synthesis_task = Task(
            description=f"""
            Synthesize the following enhanced data analysis results into comprehensive insights:
            
            Original Query: "{self.state.query}"
            
            Enhanced Data Analysis Results:
            {all_analysis}
            
            Your synthesis should:
            1. Combine insights from mathematical and statistical analysis
            2. Integrate ReAct reasoning and step-by-step data processing
            3. Validate analysis quality and confidence levels
            4. Create comprehensive, actionable data insights
            5. Highlight statistical relationships and computational findings
            6. Note the advanced analytical methods used
            7. Provide clear recommendations based on the data
            
            Use the professional data analysis tools if additional processing is needed.
            """,
            expected_output="Comprehensive data insights combining mathematical and reasoning capabilities",
            agent=self._processor_agent,
            tools=self._standard_tools
        )
        
        crew = Crew(
            agents=[self._processor_agent],
            tasks=[synthesis_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        
        # Create final synthesis result
        synthesis_result = AnalysisResult(
            analysis_type="Enhanced Data Insights Synthesis",
            input_data={"query": self.state.query, "total_analyses": len(self.state.results)},
            findings=self._extract_findings(result.raw),
            insights=self._extract_insights(result.raw),
            metrics={"combined_confidence": self._calculate_combined_confidence(), "analyses_count": len(self.state.results)},
            confidence_level=self._calculate_combined_confidence(),
            agent_id=f"Enhanced_Data_Synthesis_{self._flow_id}"
        )
        
        self.state.results.append(synthesis_result.model_dump())
        self.state.final_output = result.raw
        
        # Collect reliability statistics
        self.state.reliability_stats = {
            "math_processor_stats": self._reliable_math_processor.get_validation_stats(),
            "react_processor_stats": self._reliable_react_processor.get_validation_stats(),
            "total_results": len(self.state.results),
            "average_confidence": self._calculate_combined_confidence()
        }
        
        print("✅ Enhanced data insights synthesis completed")
        print(f"   📊 Combined confidence: {self._calculate_combined_confidence():.2f}")
        print(f"   🔧 Enhanced agents used: {len(self.state.enhanced_agents_used)}")
        print(f"   📈 Reliability stats available")
        
        return {"synthesis_complete": True, "enhanced_features_used": True}
    
    def _extract_findings(self, analysis_text: str) -> List[str]:
        """Extract findings from analysis text."""
        findings = []
        lines = analysis_text.split('\n')
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['finding', 'result', 'observation', 'pattern', 'trend']):
                if len(line) > 25 and len(line) < 200:
                    findings.append(line)
        return findings[:5]  # Return top 5 findings
    
    def _extract_insights(self, analysis_text: str) -> List[str]:
        """Extract insights from analysis text."""
        insights = []
        lines = analysis_text.split('\n')
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['insight', 'conclusion', 'recommendation', 'implication']):
                if len(line) > 25 and len(line) < 200:
                    insights.append(line)
        return insights[:5]  # Return top 5 insights
    
    def _calculate_combined_confidence(self) -> float:
        """Calculate combined confidence from all analysis results."""
        if not self.state.results:
            return 0.0
        
        confidences = [result.get('confidence_level', 0.0) for result in self.state.results]
        return sum(confidences) / len(confidences)
    
    async def kickoff_async(self, inputs=None):
        """Enhanced async kickoff with quick wins integration."""
        self._inputs = inputs
        print(f"🚀 Starting Enhanced Data Processor Flow with Quick Wins")
        
        # Set query from inputs
        if inputs and 'query' in inputs:
            self.state.query = inputs['query']
        
        # Execute the flow
        result = await super().kickoff_async(inputs)
        
        print(f"✅ Enhanced Data Processor Flow completed with {len(self.state.results)} results")
        
        # Return structured data for main workflow
        return {
            'flow_type': 'analysis',
            'results': self.state.results,
            'final_output': self.state.final_output,
            'enhanced_agents_used': self.state.enhanced_agents_used,
            'reliability_stats': self.state.reliability_stats,
            'tools_performance': self.state.tools_performance,
            'success': len(self.state.results) > 0
        }


# Keep the old name for compatibility but use enhanced version
DataProcessorFlow = EnhancedDataProcessorFlow


# Convenience functions for running specialist flows
async def run_researcher_flow(query: str, context: Dict[str, Any] = None):
    """Run Researcher flow with specific query."""
    flow = ResearcherFlow()
    flow.state.query = query
    if context:
        flow.state.context.update(context)
    result = await flow.kickoff_async()
    return result


async def run_librarian_flow(query: str, context: Dict[str, Any] = None):
    """Run Librarian flow with specific query."""
    flow = LibrarianFlow()
    flow.state.query = query
    if context:
        flow.state.context.update(context)
    result = await flow.kickoff_async()
    return result


async def run_data_processor_flow(query: str, context: Dict[str, Any] = None):
    """Run DataProcessor flow with specific query."""
    flow = DataProcessorFlow()
    flow.state.query = query
    if context:
        flow.state.context.update(context)
    result = await flow.kickoff_async()
    return result


if __name__ == "__main__":
    # For testing
    async def test_specialists():
        query = "artificial intelligence trends"
        
        # Test all specialists
        researcher_result = await run_researcher_flow(query)
        librarian_result = await run_librarian_flow(query)
        processor_result = await run_data_processor_flow(query)
        
        print("All specialist flows completed successfully!")
    
    asyncio.run(test_specialists()) 