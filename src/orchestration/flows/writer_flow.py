"""
Writer flow implementation using CrewAI 2025 Flows.

Handles final synthesis of all specialist results into a comprehensive,
well-structured answer with quality validation and formatting.
"""

import uuid
from typing import Any, Dict, List, Optional
from datetime import datetime
import re

from crewai.flow.flow import Flow, listen, start
from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, Field

from ...core.models.state_models import WorkflowState, ResearchResult, LibraryResult, AnalysisResult
from ...core.interfaces.flow_interface import IWriterFlow, FlowStatus
from src.infrastructure.config.settings import get_llm_for_agent, enhance_backstory_with_current_date


class WriterState(BaseModel):
    """State for Writer flow."""
    original_query: str = ""
    research_results: List[Dict[str, Any]] = Field(default_factory=list)
    library_results: List[Dict[str, Any]] = Field(default_factory=list)
    analysis_results: List[Dict[str, Any]] = Field(default_factory=list)
    synthesis_notes: List[str] = Field(default_factory=list)
    draft_answer: str = ""
    final_answer: str = ""
    quality_score: float = 0.0
    word_count: int = 0


class WriterFlow(Flow[WriterState]):
    """
    Writer flow for final synthesis and answer generation.
    
    Synthesizes results from all specialist flows into a comprehensive,
    well-structured final answer with quality validation.
    """
    
    def __init__(self):
        # Initialize attributes first
        self._flow_id = str(uuid.uuid4())
        self._status = FlowStatus.PENDING
        
        # Initialize Writer agents
        self._synthesis_agent = self._create_synthesis_agent()
        self._quality_agent = self._create_quality_agent()
        self._formatting_agent = self._create_formatting_agent()
        
        # Call super().__init__() after setting attributes
        super().__init__()
    
    def _create_synthesis_agent(self) -> Agent:
        """Create the content synthesis agent."""
        return Agent(
            role="Senior Content Synthesis Specialist",
            goal="Synthesize diverse research findings into comprehensive, coherent, and insightful responses",
            backstory=enhance_backstory_with_current_date("""You are an expert content synthesizer with exceptional ability to integrate information 
            from multiple sources into coherent, comprehensive responses. You excel at identifying key themes, 
            resolving contradictions, and presenting complex information in accessible formats. You have deep 
            experience in academic writing, research synthesis, and knowledge integration across diverse domains."""),
            verbose=True,
            allow_delegation=False,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("writer"),
            tools=[]  # Writer uses reasoning and synthesis, not external tools
        )
    
    def _create_quality_agent(self) -> Agent:
        """Create the quality assurance agent."""
        return Agent(
            role="Content Quality Assurance Expert",
            goal="Ensure all responses meet the highest standards of accuracy, completeness, and clarity",
            backstory=enhance_backstory_with_current_date("""You are a meticulous quality assurance expert with keen attention to detail and deep 
            understanding of content quality standards. You excel at identifying gaps, inconsistencies, and areas 
            for improvement in written content. You have extensive experience in editorial review, fact-checking, 
            and content optimization for maximum impact and clarity."""),
            verbose=True,
            allow_delegation=False,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("writer"),
            tools=[]
        )
    
    def _create_formatting_agent(self) -> Agent:
        """Create the content formatting agent."""
        return Agent(
            role="Content Formatting and Presentation Specialist",
            goal="Format content for optimal readability, structure, and professional presentation",
            backstory=enhance_backstory_with_current_date("""You are an expert in content formatting and presentation with exceptional skills in 
            structuring information for maximum readability and impact. You understand how to use headings, 
            bullet points, tables, and other formatting elements to enhance comprehension. You excel at adapting 
            content format to different audiences and use cases."""),
            verbose=True,
            allow_delegation=False,
            memory=True,
            max_iter=3,
            llm=get_llm_for_agent("writer"),
            tools=[]
        )
    
    @start()
    def initialize_synthesis(self):
        """Initialize synthesis with collected research data."""
        print("✍️ Writer: Initializing content synthesis...")
        
        # Extract data from inputs (set by MainWorkflowFlow)
        inputs = getattr(self, '_inputs', {})
        
        self.state.original_query = inputs.get('query', 'comprehensive analysis and insights')
        self.state.research_results = inputs.get('research_results', [])
        self.state.library_results = inputs.get('library_results', [])
        self.state.analysis_results = inputs.get('analysis_results', [])
        
        print(f"🎯 Synthesis target: {self.state.original_query}")
        print(f"📊 Data sources: {len(self.state.research_results)} research, {len(self.state.library_results)} library, {len(self.state.analysis_results)} analysis")
        
        return {
            "initialized": True,
            "query": self.state.original_query,
            "data_count": len(self.state.research_results) + len(self.state.library_results) + len(self.state.analysis_results)
        }
    
    @listen(initialize_synthesis)
    def synthesize_content(self):
        """Synthesize all collected information into a draft response."""
        print("🔗 Writer: Synthesizing content from all sources...")
        
        # Prepare consolidated input for synthesis
        consolidated_input = self._prepare_synthesis_input()
        
        synthesis_task = Task(
            description=f"""
            Synthesize the following research findings into a comprehensive response:
            
            Original Query: "{self.state.original_query}"
            
            RESEARCH FINDINGS:
            {consolidated_input['research_summary']}
            
            LIBRARY DOCUMENTS:
            {consolidated_input['library_summary']}
            
            ANALYSIS INSIGHTS:
            {consolidated_input['analysis_summary']}
            
            Your synthesis should:
            1. Provide a comprehensive answer to the original query
            2. Integrate findings from all sources coherently
            3. Identify and resolve any contradictions between sources
            4. Highlight the most important insights and findings
            5. Present information in logical, structured sections
            6. Include supporting evidence and source references
            7. Address different aspects and perspectives found
            8. Conclude with key takeaways and implications
            
            Structure your response with:
            - Executive Summary (2-3 sentences)
            - Main Content (organized by key themes or topics)
            - Key Insights and Trends
            - Supporting Evidence
            - Conclusions and Implications
            - Areas for Further Investigation (if any)
            
            Aim for a comprehensive yet accessible response that fully addresses the query.
            """,
            expected_output="Comprehensive synthesized response integrating all research findings with clear structure and conclusions",
            agent=self._synthesis_agent
        )
        
        crew = Crew(
            agents=[self._synthesis_agent],
            tasks=[synthesis_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        self.state.draft_answer = result.raw
        self.state.word_count = len(result.raw.split())
        
        print(f"✅ Content synthesis completed ({self.state.word_count} words)")
        return {"synthesis_complete": True, "word_count": self.state.word_count}
    
    @listen(synthesize_content)
    def validate_quality(self):
        """Validate and enhance content quality."""
        print("🔍 Writer: Validating content quality and completeness...")
        
        quality_task = Task(
            description=f"""
            Review and assess the quality of the following synthesized response:
            
            Original Query: "{self.state.original_query}"
            
            DRAFT RESPONSE:
            {self.state.draft_answer}
            
            Your quality assessment should evaluate:
            1. COMPLETENESS: Does it fully answer the original query?
            2. ACCURACY: Are facts and claims properly supported?
            3. CLARITY: Is the content clear and easy to understand?
            4. STRUCTURE: Is information well-organized and logical?
            5. COHERENCE: Do all parts work together effectively?
            6. DEPTH: Is the analysis sufficiently detailed and insightful?
            7. BALANCE: Are different perspectives fairly represented?
            8. CONCLUSION: Are key takeaways clearly stated?
            
            Provide:
            - Overall quality score (1-10)
            - Specific strengths identified
            - Areas needing improvement
            - Suggestions for enhancement
            - Recommended revisions if quality score < 8
            
            If the quality score is below 8, provide specific revision recommendations.
            If the quality score is 8 or above, approve the content as final.
            """,
            expected_output="Detailed quality assessment with score, strengths, improvements, and revision recommendations",
            agent=self._quality_agent
        )
        
        crew = Crew(
            agents=[self._quality_agent],
            tasks=[quality_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        
        # Extract quality score
        quality_score = self._extract_quality_score(result.raw)
        self.state.quality_score = quality_score
        
        # If quality is sufficient, use current draft
        if quality_score >= 8.0:
            self.state.final_answer = self.state.draft_answer
            print(f"✅ Quality validation passed (score: {quality_score}/10)")
        else:
            # Need to revise
            print(f"⚠️ Quality needs improvement (score: {quality_score}/10)")
            self.state.synthesis_notes.append(f"Quality review: {result.raw}")
        
        return {"quality_complete": True, "quality_score": quality_score, "needs_revision": quality_score < 8.0}
    
    @listen(validate_quality)
    def format_final_response(self):
        """Format the final response for optimal presentation."""
        print("📝 Writer: Formatting final response...")
        
        content_to_format = self.state.final_answer or self.state.draft_answer
        
        formatting_task = Task(
            description=f"""
            Format the following content for optimal presentation and readability:
            
            Original Query: "{self.state.original_query}"
            
            CONTENT TO FORMAT:
            {content_to_format}
            
            Apply professional formatting including:
            1. Clear hierarchical structure with appropriate headings
            2. Bullet points and numbered lists where appropriate
            3. Emphasis on key points using **bold** or *italic* text
            4. Logical paragraph breaks for readability
            5. Consistent formatting throughout
            6. Professional tone and style
            7. Clear transitions between sections
            8. Summary or conclusion prominently displayed
            
            Output format should be clean, professional, and optimized for readability.
            Use markdown formatting conventions for structure and emphasis.
            
            Ensure the final formatted response is publication-ready and professional.
            """,
            expected_output="Professionally formatted response with clear structure, emphasis, and optimal readability",
            agent=self._formatting_agent
        )
        
        crew = Crew(
            agents=[self._formatting_agent],
            tasks=[formatting_task],
            process=Process.sequential,
            verbose=True
        )
        
        result = crew.kickoff()
        self.state.final_answer = result.raw
        
        # Update final metrics
        self.state.word_count = len(self.state.final_answer.split())
        
        print("✅ Final response formatted and ready")
        return {
            "formatting_complete": True,
            "final_answer": self.state.final_answer,
            "word_count": self.state.word_count,
            "quality_score": self.state.quality_score
        }
    
    def _prepare_synthesis_input(self) -> Dict[str, str]:
        """Prepare consolidated input for synthesis."""
        
        # Consolidate research results
        research_summary = "RESEARCH FINDINGS:\n"
        for i, result in enumerate(self.state.research_results, 1):
            research_summary += f"\n{i}. Research Result:\n"
            research_summary += f"   Query: {result.get('query', '')}\n"
            research_summary += f"   Summary: {result.get('summary', '')}\n"
            research_summary += f"   Key Findings: {', '.join(result.get('key_findings', []))}\n"
            research_summary += f"   Confidence: {result.get('confidence_score', 0)}\n"
        
        # Consolidate library results
        library_summary = "LIBRARY DOCUMENTS:\n"
        for i, result in enumerate(self.state.library_results, 1):
            library_summary += f"\n{i}. Library Result:\n"
            library_summary += f"   Query: {result.get('query', '')}\n"
            library_summary += f"   Documents: {result.get('document_count', 0)}\n"
            library_summary += f"   Excerpts: {'; '.join(result.get('relevant_excerpts', []))}\n"
        
        # Consolidate analysis results
        analysis_summary = "ANALYSIS INSIGHTS:\n"
        for i, result in enumerate(self.state.analysis_results, 1):
            analysis_summary += f"\n{i}. Analysis Result:\n"
            analysis_summary += f"   Type: {result.get('analysis_type', '')}\n"
            analysis_summary += f"   Findings: {'; '.join(result.get('findings', []))}\n"
            analysis_summary += f"   Insights: {'; '.join(result.get('insights', []))}\n"
            analysis_summary += f"   Confidence: {result.get('confidence_level', 0)}\n"
        
        return {
            "research_summary": research_summary,
            "library_summary": library_summary,
            "analysis_summary": analysis_summary
        }
    
    def _extract_quality_score(self, quality_text: str) -> float:
        """Extract quality score from quality review text."""
        # Look for patterns like "8/10" or "score: 8"
        patterns = [
            r'(\d+(?:\.\d+)?)/10',
            r'score:?\s*(\d+(?:\.\d+)?)',
            r'rating:?\s*(\d+(?:\.\d+)?)',
            r'Overall.*?(\d+(?:\.\d+)?)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, quality_text, re.IGNORECASE)
            if match:
                score = float(match.group(1))
                # Convert from 10-point scale to 1-point scale
                if score > 1.0:
                    score = score / 10.0
                return min(max(score, 0.0), 1.0)  # Ensure between 0-1
        
        return 0.8  # Default score

    def kickoff_async(self, inputs=None):
        """Override kickoff_async to properly handle inputs."""
        if inputs:
            # Store inputs for flow methods that expect _inputs
            self._inputs = inputs
            
            # Also update state for direct access
            for key, value in inputs.items():
                if hasattr(self.state, key):
                    setattr(self.state, key, value)
        
        return super().kickoff_async()


# Convenience function for running Writer flow standalone
async def run_writer_flow(query: str, 
                         research_results: List[Dict[str, Any]] = None,
                         library_results: List[Dict[str, Any]] = None,
                         analysis_results: List[Dict[str, Any]] = None):
    """Run Writer flow with specific inputs."""
    flow = WriterFlow()
    flow.state.original_query = query
    
    if research_results:
        flow.state.research_results = research_results
    if library_results:
        flow.state.library_results = library_results
    if analysis_results:
        flow.state.analysis_results = analysis_results
    
    result = await flow.kickoff_async()
    return result


if __name__ == "__main__":
    import asyncio
    
    # Test with mock data
    async def test_writer():
        query = "What are the latest developments in renewable energy?"
        
        # Mock research results
        research_results = [{
            "query": query,
            "summary": "Recent developments in renewable energy include significant advances in solar panel efficiency, wind turbine technology, and energy storage solutions.",
            "key_findings": ["Solar efficiency up 15%", "Wind capacity doubled", "Battery costs down 50%"],
            "confidence_score": 0.9
        }]
        
        # Mock library results
        library_results = [{
            "query": query,
            "document_count": 5,
            "relevant_excerpts": ["Solar technology improvements", "Wind energy expansion", "Storage breakthroughs"]
        }]
        
        # Mock analysis results
        analysis_results = [{
            "analysis_type": "trend_analysis",
            "findings": ["Accelerating growth", "Cost reductions", "Policy support"],
            "insights": ["Market transformation", "Technology maturation"],
            "confidence_level": 0.8
        }]
        
        result = await run_writer_flow(query, research_results, library_results, analysis_results)
        print("Writer flow completed successfully!")
        print(f"Final answer length: {len(result.get('final_answer', ''))}")
    
    asyncio.run(test_writer()) 