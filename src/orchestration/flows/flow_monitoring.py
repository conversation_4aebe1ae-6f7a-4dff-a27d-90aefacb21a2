import asyncio
import time
from typing import Dict, Any, Callable, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging


@dataclass
class MonitoringConfig:
    """Configuration for flow monitoring."""
    enable_real_time_monitoring: bool = True
    performance_threshold_ms: int = 5000
    error_rate_threshold: float = 0.1
    alert_cooldown_minutes: int = 5
    max_memory_usage: float = 0.85
    max_cpu_usage: float = 0.90


@dataclass
class PerformanceMetrics:
    """Performance metrics for workflow execution."""
    avg_response_time: float
    success_rate: float
    error_count: int
    active_agents: int = 0
    throughput: float = 0.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class FlowMonitor:
    """
    Real-time monitoring and alerting for workflow execution.
    Provides performance tracking, error detection, and resource management.
    """
    
    def __init__(self, config: MonitoringConfig = None):
        self.config = config or MonitoringConfig()
        self.alert_thresholds = {
            'max_execution_time': self.config.performance_threshold_ms / 1000.0,
            'min_success_rate': 1.0 - self.config.error_rate_threshold,
            'max_error_rate': self.config.error_rate_threshold,
            'max_memory_usage': self.config.max_memory_usage
        }
        self.execution_history: List[Dict[str, Any]] = []
        self.performance_history: List[PerformanceMetrics] = []
        self.active_alerts: List[Dict[str, Any]] = []
        self.last_alert_time: Dict[str, datetime] = {}
        self.active_workflows = {}
        self.logger = logging.getLogger(__name__)
        
    async def monitor_workflow(self, workflow_id: str, state_callback: Callable):
        """Monitor workflow execution in real-time."""
        start_time = time.time()
        self.active_workflows[workflow_id] = {
            'start_time': start_time,
            'status': 'running',
            'last_check': start_time
        }
        
        self.logger.info(f"Started monitoring workflow {workflow_id}")
        
        while True:
            try:
                # Get current workflow state
                current_state = await state_callback()
                
                # Check for completion
                if current_state.workflow_completed:
                    await self._finalize_monitoring(workflow_id, current_state)
                    break
                
                # Performance checks
                execution_time = time.time() - start_time
                if execution_time > self.alert_thresholds['max_execution_time']:
                    await self._trigger_alert('timeout', workflow_id, execution_time)
                
                # Error rate checks
                if current_state.task_results:
                    error_rate = len(current_state.errors_encountered) / len(current_state.task_results)
                    if error_rate > self.alert_thresholds['max_error_rate']:
                        await self._trigger_alert('high_error_rate', workflow_id, error_rate)
                
                # Resource usage checks
                await self._check_resource_usage(workflow_id)
                
                # Update workflow tracking
                self.active_workflows[workflow_id]['last_check'] = time.time()
                self.active_workflows[workflow_id]['execution_time'] = execution_time
                
                # Wait before next check
                await asyncio.sleep(1.0)
                
            except Exception as e:
                self.logger.error(f"Monitoring error for workflow {workflow_id}: {e}")
                await asyncio.sleep(5.0)
    
    async def _trigger_alert(self, alert_type: str, workflow_id: str, value: Any):
        """Trigger monitoring alert with context."""
        alert = {
            'type': alert_type,
            'workflow_id': workflow_id,
            'value': value,
            'timestamp': datetime.now(),
            'severity': self._determine_severity(alert_type, value)
        }
        
        self.logger.warning(f"Flow Alert: {alert}")
        
        # Store alert in history
        self.metrics_history.append(alert)
        
        # Could integrate with external alerting systems here
        # await self._send_to_alerting_system(alert)
        
        print(f"🚨 ALERT [{alert['severity']}]: {alert_type} for workflow {workflow_id[:8]}... - Value: {value}")
    
    async def _finalize_monitoring(self, workflow_id: str, final_state):
        """Finalize monitoring for completed workflow."""
        if workflow_id in self.active_workflows:
            workflow_info = self.active_workflows[workflow_id]
            total_time = time.time() - workflow_info['start_time']
            
            # Log final metrics
            self.logger.info(f"Workflow {workflow_id} completed in {total_time:.2f}s")
            
            # Calculate final metrics
            final_metrics = {
                'workflow_id': workflow_id,
                'total_execution_time': total_time,
                'total_tasks': len(final_state.task_results),
                'successful_tasks': sum(1 for r in final_state.task_results if r.success),
                'error_count': len(final_state.errors_encountered),
                'completion_time': datetime.now()
            }
            
            self.metrics_history.append(final_metrics)
            
            # Clean up
            self.active_workflows[workflow_id]['status'] = 'completed'
            
            print(f"✅ Workflow {workflow_id[:8]}... monitoring completed: {final_metrics['successful_tasks']}/{final_metrics['total_tasks']} tasks successful")
    
    async def _check_resource_usage(self, workflow_id: str):
        """Check system resource usage."""
        try:
            import psutil
            
            # Check memory usage
            memory_percent = psutil.virtual_memory().percent / 100
            if memory_percent > self.alert_thresholds['max_memory_usage']:
                await self._trigger_alert('high_memory_usage', workflow_id, memory_percent)
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1) / 100
            if cpu_percent > 0.90:  # 90% CPU threshold
                await self._trigger_alert('high_cpu_usage', workflow_id, cpu_percent)
                
        except ImportError:
            # psutil not available, skip resource monitoring
            pass
        except Exception as e:
            self.logger.error(f"Resource monitoring error: {e}")
    
    def _determine_severity(self, alert_type: str, value: Any) -> str:
        """Determine alert severity based on type and value."""
        if alert_type == 'timeout':
            if value > 600:  # 10 minutes
                return 'CRITICAL'
            elif value > 300:  # 5 minutes
                return 'HIGH'
            else:
                return 'MEDIUM'
        
        elif alert_type == 'high_error_rate':
            if value > 0.20:  # 20% error rate
                return 'CRITICAL'
            elif value > 0.10:  # 10% error rate
                return 'HIGH'
            else:
                return 'MEDIUM'
        
        elif alert_type in ['high_memory_usage', 'high_cpu_usage']:
            if value > 0.95:  # 95%
                return 'CRITICAL'
            elif value > 0.85:  # 85%
                return 'HIGH'
            else:
                return 'MEDIUM'
        
        return 'MEDIUM'
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get current monitoring summary."""
        active_count = len([w for w in self.active_workflows.values() if w['status'] == 'running'])
        completed_count = len([w for w in self.active_workflows.values() if w['status'] == 'completed'])
        
        return {
            'active_workflows': active_count,
            'completed_workflows': completed_count,
            'total_alerts': len([m for m in self.metrics_history if 'type' in m]),
            'uptime': time.time() - min([w['start_time'] for w in self.active_workflows.values()]) if self.active_workflows else 0,
            'alert_thresholds': self.alert_thresholds
        }
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of specific workflow."""
        if workflow_id in self.active_workflows:
            info = self.active_workflows[workflow_id]
            return {
                'workflow_id': workflow_id,
                'status': info['status'],
                'execution_time': time.time() - info['start_time'],
                'last_check': info.get('last_check', info['start_time'])
            }
        return None

    def record_execution(self, task_id: str, duration: float, success: bool, agent_id: str):
        """Record execution metrics for analysis."""
        record = {
            "task_id": task_id,
            "duration": duration,
            "success": success,
            "agent_id": agent_id,
            "timestamp": datetime.now()
        }
        self.execution_history.append(record)
        
        # Keep only recent history (last 1000 records)
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-1000:]
    
    def get_current_performance(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        if not self.execution_history:
            return PerformanceMetrics(
                avg_response_time=0.0,
                success_rate=1.0,
                error_count=0
            )
        
        recent_records = self.execution_history[-100:]  # Last 100 executions
        
        total_duration = sum(r["duration"] for r in recent_records)
        avg_response_time = total_duration / len(recent_records)
        
        successful_count = sum(1 for r in recent_records if r["success"])
        success_rate = successful_count / len(recent_records)
        
        error_count = len(recent_records) - successful_count
        
        unique_agents = len(set(r["agent_id"] for r in recent_records))
        
        return PerformanceMetrics(
            avg_response_time=avg_response_time,
            success_rate=success_rate,
            error_count=error_count,
            active_agents=unique_agents
        )
    
    def check_alerts(self) -> List[Dict[str, Any]]:
        """Check for alert conditions and return new alerts."""
        new_alerts = []
        current_time = datetime.now()
        
        if not self.config.enable_real_time_monitoring:
            return new_alerts
        
        metrics = self.get_current_performance()
        
        # Check performance threshold
        if metrics.avg_response_time > self.config.performance_threshold_ms:
            alert_key = "performance_threshold"
            if self._should_trigger_alert(alert_key, current_time):
                alert = {
                    "id": f"alert-{int(current_time.timestamp())}",
                    "type": "performance",
                    "level": "warning",
                    "message": f"Average response time ({metrics.avg_response_time:.0f}ms) exceeds threshold ({self.config.performance_threshold_ms}ms)",
                    "timestamp": current_time,
                    "metric_value": metrics.avg_response_time,
                    "threshold": self.config.performance_threshold_ms
                }
                new_alerts.append(alert)
                self.active_alerts.append(alert)
                self.last_alert_time[alert_key] = current_time
        
        # Check error rate threshold
        if metrics.success_rate < (1.0 - self.config.error_rate_threshold):
            alert_key = "error_rate_threshold"
            if self._should_trigger_alert(alert_key, current_time):
                alert = {
                    "id": f"alert-{int(current_time.timestamp())}-error",
                    "type": "error_rate",
                    "level": "error",
                    "message": f"Success rate ({metrics.success_rate:.2%}) below threshold ({1.0 - self.config.error_rate_threshold:.2%})",
                    "timestamp": current_time,
                    "metric_value": metrics.success_rate,
                    "threshold": 1.0 - self.config.error_rate_threshold
                }
                new_alerts.append(alert)
                self.active_alerts.append(alert)
                self.last_alert_time[alert_key] = current_time
        
        return new_alerts
    
    def _should_trigger_alert(self, alert_key: str, current_time: datetime) -> bool:
        """Check if enough time has passed since last alert of this type."""
        if alert_key not in self.last_alert_time:
            return True
        
        cooldown_delta = timedelta(minutes=self.config.alert_cooldown_minutes)
        return current_time - self.last_alert_time[alert_key] > cooldown_delta 