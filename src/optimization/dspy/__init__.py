"""
DSPy optimization and reliability components.
"""

from .reliability_wrapper import (
    ReliableAgentWrapper,
    make_agent_reliable,
    make_agents_reliable,
    create_factual_consistency_reward,
    create_source_citation_reward
)

# DSPy Optimization Framework
from .base_optimizer import (
    BaseOptimizer,
    BootstrapOptimizer,
    RandomSearchOptimizer,
    OptimizationConfig,
    OptimizationResult,
    get_optimizer
)

# MIPROv2 Advanced Optimization
try:
    from .mipro_v2_optimizer import (
        MIPROv2Optimizer,
        MIPROv2Config,
        InstructionGenerator,
        AdaptiveSampler
    )
    MIPROV2_AVAILABLE = True
except ImportError:
    print("⚠️ MIPROv2 components not available - check dependencies")
    MIPROV2_AVAILABLE = False

__all__ = [
    'ReliableAgentWrapper',
    'make_agent_reliable', 
    'make_agents_reliable',
    'create_factual_consistency_reward',
    'create_source_citation_reward',
    
    # Base optimization framework
    'BaseOptimizer',
    'BootstrapOptimizer', 
    'RandomSearchOptimizer',
    'OptimizationConfig',
    'OptimizationResult',
    'get_optimizer',
    
    # MIPROv2 advanced optimization
    'MIPROv2Optimizer',
    'MIPROv2Config',
    'InstructionGenerator',
    'AdaptiveSampler',
    'MIPROV2_AVAILABLE'
] 