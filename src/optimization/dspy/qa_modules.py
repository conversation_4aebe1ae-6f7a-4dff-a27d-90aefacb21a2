"""
DSPy modules for multi-agent question answering system.

Implements optimized modules for research, library, and analysis modules with proper signatures and optimizations
"""

from typing import List, Dict, Any, Optional
import dspy
from dspy import Signature, InputField, OutputField, ChainOfThought, Module, Predict


class ResearchSignature(Signature):
    """Signature for web research module."""
    query: str = InputField(desc="The research question or topic to investigate")
    context: str = InputField(desc="Additional context or background information", default="")
    
    research_summary: str = OutputField(desc="Comprehensive research summary with key findings")
    key_insights: str = OutputField(desc="List of key insights and important discoveries")
    source_quality: str = OutputField(desc="Assessment of source quality and credibility")


class LibrarySignature(Signature):
    """Signature for document retrieval and library search module."""
    query: str = InputField(desc="The search query for document retrieval")
    research_context: str = InputField(desc="Research context from previous analysis", default="")
    
    relevant_documents: str = OutputField(desc="List of relevant documents with excerpts")
    document_analysis: str = OutputField(desc="Analysis of document relevance and quality")
    key_excerpts: str = OutputField(desc="Most important excerpts and passages")


class AnalysisSignature(Signature):
    """Signature for data analysis and insights generation module."""
    research_data: str = InputField(desc="Research findings and data to analyze")
    library_data: str = InputField(desc="Library documents and excerpts to analyze")
    analysis_focus: str = InputField(desc="Specific analysis focus or question", default="comprehensive analysis")
    
    analytical_insights: str = OutputField(desc="Structured analytical insights and findings")
    trends_patterns: str = OutputField(desc="Identified trends, patterns, and relationships")
    recommendations: str = OutputField(desc="Actionable recommendations based on analysis")


class SynthesisSignature(Signature):
    """Signature for final synthesis and answer generation module."""
    original_question: str = InputField(desc="The original question being answered")
    research_summary: str = InputField(desc="Research findings summary")
    library_passages: str = InputField(desc="Relevant library passages and documents")
    analytical_insights: str = InputField(desc="Analytical insights and findings")
    
    final_answer: str = OutputField(desc="Comprehensive, well-structured final answer")
    confidence_assessment: str = OutputField(desc="Assessment of answer confidence and limitations")
    supporting_evidence: str = OutputField(desc="Key supporting evidence and sources")


class ResearchModule(Module):
    """DSPy module for web research and information gathering."""
    
    def __init__(self, signature=ResearchSignature):
        super().__init__()
        self.research_chain = ChainOfThought(signature)
    
    def forward(self, query: str, context: str = "") -> dspy.Prediction:
        """
        Perform research on the given query.
        
        Args:
            query: Research question or topic
            context: Additional context information
            
        Returns:
            Research prediction with summary, insights, and quality assessment
        """
        return self.research_chain(query=query, context=context)


class LibraryModule(Module):
    """DSPy module for document retrieval and library search."""
    
    def __init__(self, signature=LibrarySignature):
        super().__init__()
        self.library_chain = ChainOfThought(signature)
    
    def forward(self, query: str, research_context: str = "") -> dspy.Prediction:
        """
        Search library and retrieve relevant documents.
        
        Args:
            query: Search query
            research_context: Context from research phase
            
        Returns:
            Library prediction with documents, analysis, and excerpts
        """
        return self.library_chain(query=query, research_context=research_context)


class AnalysisModule(Module):
    """DSPy module for data analysis and insights generation."""
    
    def __init__(self, signature=AnalysisSignature):
        super().__init__()
        self.analysis_chain = ChainOfThought(signature)
    
    def forward(self, 
                research_data: str, 
                library_data: str, 
                analysis_focus: str = "comprehensive analysis") -> dspy.Prediction:
        """
        Analyze collected data and generate insights.
        
        Args:
            research_data: Research findings
            library_data: Library documents and excerpts
            analysis_focus: Specific analysis focus
            
        Returns:
            Analysis prediction with insights, trends, and recommendations
        """
        return self.analysis_chain(
            research_data=research_data,
            library_data=library_data,
            analysis_focus=analysis_focus
        )


class SynthesisModule(Module):
    """DSPy module for final synthesis and answer generation."""
    
    def __init__(self, signature=SynthesisSignature):
        super().__init__()
        self.synthesis_chain = ChainOfThought(signature)
    
    def forward(self, 
                original_question: str,
                research_summary: str,
                library_passages: str,
                analytical_insights: str) -> dspy.Prediction:
        """
        Synthesize all collected information into a final answer.
        
        Args:
            original_question: The original question
            research_summary: Research findings
            library_passages: Library documents
            analytical_insights: Analysis results
            
        Returns:
            Synthesis prediction with final answer, confidence, and evidence
        """
        return self.synthesis_chain(
            original_question=original_question,
            research_summary=research_summary,
            library_passages=library_passages,
            analytical_insights=analytical_insights
        )


class MultiAgentQAModule(Module):
    """
    Complete multi-agent question answering module.
    
    Combines research, library, analysis, and synthesis modules
    for comprehensive question answering.
    """
    
    def __init__(self, 
                 use_research: bool = True,
                 use_library: bool = True,
                 use_analysis: bool = True):
        super().__init__()
        
        self.use_research = use_research
        self.use_library = use_library
        self.use_analysis = use_analysis
        
        # Initialize component modules
        if use_research:
            self.research_module = ResearchModule()
        if use_library:
            self.library_module = LibraryModule()
        if use_analysis:
            self.analysis_module = AnalysisModule()
        
        self.synthesis_module = SynthesisModule()
    
    def forward(self, question: str, context: str = "") -> dspy.Prediction:
        """
        Process question through complete multi-agent pipeline.
        
        Args:
            question: The question to answer
            context: Additional context information
            
        Returns:
            Final synthesis prediction with comprehensive answer
        """
        # Phase 1: Research
        research_summary = ""
        if self.use_research:
            research_result = self.research_module(query=question, context=context)
            research_summary = f"{research_result.research_summary}\n\nKey Insights: {research_result.key_insights}"
        
        # Phase 2: Library Search
        library_passages = ""
        if self.use_library:
            library_result = self.library_module(
                query=question, 
                research_context=research_summary
            )
            library_passages = f"{library_result.relevant_documents}\n\nKey Excerpts: {library_result.key_excerpts}"
        
        # Phase 3: Analysis
        analytical_insights = ""
        if self.use_analysis:
            analysis_result = self.analysis_module(
                research_data=research_summary,
                library_data=library_passages,
                analysis_focus=f"Analysis of: {question}"
            )
            analytical_insights = f"{analysis_result.analytical_insights}\n\nTrends: {analysis_result.trends_patterns}\n\nRecommendations: {analysis_result.recommendations}"
        
        # Phase 4: Synthesis
        final_result = self.synthesis_module(
            original_question=question,
            research_summary=research_summary,
            library_passages=library_passages,
            analytical_insights=analytical_insights
        )
        
        return final_result


def create_training_examples(questions_answers: List[Dict[str, str]]) -> List[dspy.Example]:
    """
    Create DSPy training examples from question-answer pairs.
    
    Args:
        questions_answers: List of dicts with 'question' and 'answer' keys
        
    Returns:
        List of DSPy examples for optimization
    """
    examples = []
    
    for qa in questions_answers:
        question = qa.get('question', '')
        answer = qa.get('answer', '')
        context = qa.get('context', '')
        
        if question and answer:
            example = dspy.Example(
                question=question,
                context=context,
                answer=answer
            ).with_inputs('question', 'context')
            
            examples.append(example)
    
    return examples


def create_qa_metric(similarity_threshold: float = 0.8) -> callable:
    """
    Create evaluation metric for QA module.
    
    Args:
        similarity_threshold: Threshold for answer similarity
        
    Returns:
        Evaluation metric function
    """
    def qa_metric(example, pred, trace=None) -> bool:
        """Evaluate QA prediction against ground truth."""
        if not hasattr(pred, 'final_answer') or not hasattr(example, 'answer'):
            return False
        
        predicted_answer = pred.final_answer.lower().strip()
        expected_answer = example.answer.lower().strip()
        
        # Simple keyword-based evaluation
        predicted_words = set(predicted_answer.split())
        expected_words = set(expected_answer.split())
        
        if not expected_words:
            return False
        
        # Calculate overlap similarity
        overlap = len(predicted_words.intersection(expected_words))
        similarity = overlap / len(expected_words)
        
        return similarity >= similarity_threshold
    
    return qa_metric


# Example usage and testing
if __name__ == "__main__":
    # Example of how to use the modules
    print("🧪 Testing DSPy QA Modules...")
    
    # Initialize the complete system
    qa_system = MultiAgentQAModule()
    
    # Test question
    test_question = "What are the latest developments in artificial intelligence?"
    
    try:
        # Run the system (requires DSPy configuration)
        result = qa_system(question=test_question)
        
        print(f"Question: {test_question}")
        print(f"Answer: {result.final_answer}")
        print(f"Confidence: {result.confidence_assessment}")
        
    except Exception as e:
        print(f"Note: Full test requires DSPy LM configuration. Error: {e}")
    
    print("✅ DSPy modules defined successfully!") 