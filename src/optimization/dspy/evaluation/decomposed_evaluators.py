"""
Decomposed Evaluation Metrics

This module provides specialized DSPy evaluators for different quality dimensions:
- Relevance: How well the answer addresses the question
- Coherence: Logical flow and consistency
- Instruction Following: Adherence to specific instructions
- Tool Usage: Efficiency of tool utilization

Each evaluator uses DSPy's ChainOfThought pattern for transparent reasoning.
"""

import dspy
from typing import List, Dict, Any, Optional
import json


class RelevanceEvaluator(dspy.Signature):
    """Evaluate relevance of answer to question."""
    question = dspy.InputField()
    answer = dspy.InputField()
    context = dspy.InputField()
    relevance_score = dspy.OutputField(desc="Score 0-1 for answer relevance to question")
    relevance_reasoning = dspy.OutputField(desc="Brief explanation of relevance score")


class CoherenceEvaluator(dspy.Signature):
    """Evaluate logical coherence and flow of the answer."""
    answer = dspy.InputField()
    coherence_score = dspy.OutputField(desc="Score 0-1 for logical coherence and flow")
    coherence_issues = dspy.OutputField(desc="List any coherence problems found")


class InstructionFollowingEvaluator(dspy.Signature):
    """Evaluate how well answer follows specific instructions."""
    task_instructions = dspy.InputField()
    answer = dspy.InputField()
    following_score = dspy.OutputField(desc="Score 0-1 for instruction adherence")
    missed_requirements = dspy.OutputField(desc="List any missed requirements")


class ToolUsageEvaluator(dspy.Signature):
    """Evaluate efficiency and appropriateness of tool usage."""
    available_tools = dspy.InputField()
    tools_used = dspy.InputField()
    answer_quality = dspy.InputField()
    efficiency_score = dspy.OutputField(desc="Score 0-1 for tool usage efficiency")
    improvement_suggestions = dspy.OutputField(desc="Suggestions for better tool usage")


class ComprehensiveEvaluator(dspy.Signature):
    """Comprehensive evaluation combining multiple quality dimensions."""
    question = dspy.InputField()
    answer = dspy.InputField()
    context = dspy.InputField()
    task_instructions = dspy.InputField(desc="Specific instructions for the task")
    tools_available = dspy.InputField(desc="Available tools for the task")
    tools_used = dspy.InputField(desc="Tools actually used")
    
    # Output fields for comprehensive assessment
    overall_quality = dspy.OutputField(desc="Overall quality score 0-1")
    relevance_assessment = dspy.OutputField(desc="Relevance to question (0-1)")
    coherence_assessment = dspy.OutputField(desc="Logical coherence (0-1)")
    instruction_compliance = dspy.OutputField(desc="Instruction following (0-1)")
    tool_efficiency = dspy.OutputField(desc="Tool usage efficiency (0-1)")
    
    # Detailed feedback
    strengths = dspy.OutputField(desc="Key strengths of the response")
    weaknesses = dspy.OutputField(desc="Areas for improvement")
    specific_feedback = dspy.OutputField(desc="Specific actionable feedback")


class EvaluatorFactory:
    """Factory for creating and managing evaluator instances."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize evaluator factory with configuration.

        Args:
            config: Configuration dict with evaluator settings
        """
        self.config = config
        # Handle both dict and SystemConfig objects
        if hasattr(config, 'get'):
            self.evaluator_config = config.get('dspy_evaluator', {})
        else:
            self.evaluator_config = getattr(config, 'dspy_evaluator', {}) if hasattr(config, 'dspy_evaluator') else {}
        
        # Create evaluator instances
        self._setup_evaluators()
    
    def _setup_evaluators(self):
        """Setup all evaluator instances with proper configuration."""
        # Configure DSPy settings for evaluation
        # Helper function to safely get values
        def safe_get(obj, key, default=None):
            if hasattr(obj, 'get'):
                return obj.get(key, default)
            else:
                return getattr(obj, key, default)

        model_name = safe_get(self.evaluator_config, 'model', 'gpt-4')
        temperature = safe_get(self.evaluator_config, 'temperature', 0.1)
        max_tokens = safe_get(self.evaluator_config, 'max_tokens', 10000)
        
        # Create ChainOfThought instances for each evaluator
        self.relevance_evaluator = dspy.ChainOfThought(RelevanceEvaluator)
        self.coherence_evaluator = dspy.ChainOfThought(CoherenceEvaluator)
        self.instruction_evaluator = dspy.ChainOfThought(InstructionFollowingEvaluator)
        self.tool_evaluator = dspy.ChainOfThought(ToolUsageEvaluator)
        self.comprehensive_evaluator = dspy.ChainOfThought(ComprehensiveEvaluator)
        
        print(f"✅ Evaluators initialized with model: {model_name}")
    
    def evaluate_relevance(self, question: str, answer: str, context: str) -> Dict[str, Any]:
        """Evaluate answer relevance to question."""
        try:
            result = self.relevance_evaluator(
                question=question,
                answer=answer,
                context=context
            )
            
            return {
                'score': self._parse_score(result.relevance_score),
                'reasoning': result.relevance_reasoning,
                'raw_result': result
            }
        except Exception as e:
            print(f"⚠️ Relevance evaluation failed: {e}")
            return {'score': 0.5, 'reasoning': f'Evaluation failed: {e}', 'raw_result': None}
    
    def evaluate_coherence(self, answer: str) -> Dict[str, Any]:
        """Evaluate logical coherence of answer."""
        try:
            result = self.coherence_evaluator(answer=answer)
            
            return {
                'score': self._parse_score(result.coherence_score),
                'issues': result.coherence_issues,
                'raw_result': result
            }
        except Exception as e:
            print(f"⚠️ Coherence evaluation failed: {e}")
            return {'score': 0.5, 'issues': f'Evaluation failed: {e}', 'raw_result': None}
    
    def evaluate_instruction_following(self, instructions: str, answer: str) -> Dict[str, Any]:
        """Evaluate instruction following."""
        try:
            result = self.instruction_evaluator(
                task_instructions=instructions,
                answer=answer
            )
            
            return {
                'score': self._parse_score(result.following_score),
                'missed_requirements': result.missed_requirements,
                'raw_result': result
            }
        except Exception as e:
            print(f"⚠️ Instruction following evaluation failed: {e}")
            return {'score': 0.5, 'missed_requirements': f'Evaluation failed: {e}', 'raw_result': None}
    
    def evaluate_tool_usage(self, available_tools: List[str], tools_used: List[str], 
                          answer_quality: str) -> Dict[str, Any]:
        """Evaluate tool usage efficiency."""
        try:
            result = self.tool_evaluator(
                available_tools=json.dumps(available_tools),
                tools_used=json.dumps(tools_used),
                answer_quality=answer_quality
            )
            
            return {
                'score': self._parse_score(result.efficiency_score),
                'suggestions': result.improvement_suggestions,
                'raw_result': result
            }
        except Exception as e:
            print(f"⚠️ Tool usage evaluation failed: {e}")
            return {'score': 0.5, 'suggestions': f'Evaluation failed: {e}', 'raw_result': None}
    
    def evaluate_comprehensive(self, question: str, answer: str, context: Dict[str, Any],
                             instructions: str = None, tools_used: List[str] = None) -> Dict[str, Any]:
        """Run comprehensive evaluation across all dimensions."""
        try:
            tools_available = context.get('available_tools', [])
            tools_used = tools_used or context.get('tools_used', [])
            
            result = self.comprehensive_evaluator(
                question=question,
                answer=answer,
                context=json.dumps(context),
                task_instructions=instructions or "Provide comprehensive analysis",
                tools_available=json.dumps(tools_available),
                tools_used=json.dumps(tools_used)
            )
            
            return {
                'overall_quality': self._parse_score(result.overall_quality),
                'relevance': self._parse_score(result.relevance_assessment),
                'coherence': self._parse_score(result.coherence_assessment),
                'instruction_compliance': self._parse_score(result.instruction_compliance),
                'tool_efficiency': self._parse_score(result.tool_efficiency),
                'strengths': result.strengths,
                'weaknesses': result.weaknesses,
                'specific_feedback': result.specific_feedback,
                'raw_result': result
            }
        except Exception as e:
            print(f"⚠️ Comprehensive evaluation failed: {e}")
            return {
                'overall_quality': 0.5,
                'relevance': 0.5,
                'coherence': 0.5,
                'instruction_compliance': 0.5,
                'tool_efficiency': 0.5,
                'strengths': 'Unable to evaluate',
                'weaknesses': f'Evaluation failed: {e}',
                'specific_feedback': 'Please check evaluation configuration',
                'raw_result': None
            }
    
    def _parse_score(self, score_str: str) -> float:
        """Parse score string to float with fallback."""
        try:
            # Handle various score formats
            if isinstance(score_str, (int, float)):
                return float(score_str)
            
            # Clean string and extract number
            clean_str = str(score_str).strip().lower()
            
            # Extract number from string like "0.8" or "Score: 0.8" or "8/10"
            import re
            
            # Try direct float conversion first
            try:
                return max(0.0, min(1.0, float(clean_str)))
            except ValueError:
                pass
            
            # Look for decimal numbers
            decimal_match = re.search(r'(\d+\.?\d*)', clean_str)
            if decimal_match:
                score = float(decimal_match.group(1))
                # If score is > 1, assume it's out of 10 or 100
                if score > 1:
                    if score <= 10:
                        score = score / 10.0
                    elif score <= 100:
                        score = score / 100.0
                return max(0.0, min(1.0, score))
            
            # Look for fraction format like "8/10"
            fraction_match = re.search(r'(\d+)\s*/\s*(\d+)', clean_str)
            if fraction_match:
                numerator = float(fraction_match.group(1))
                denominator = float(fraction_match.group(2))
                if denominator > 0:
                    return max(0.0, min(1.0, numerator / denominator))
            
            # Default fallback
            print(f"⚠️ Could not parse score: {score_str}, using 0.5")
            return 0.5
            
        except Exception as e:
            print(f"⚠️ Score parsing error: {e}, using 0.5")
            return 0.5


# Utility functions for direct evaluation
def quick_relevance_eval(question: str, answer: str, context: str = "", config: Dict[str, Any] = None) -> float:
    """Quick relevance evaluation with minimal setup."""
    factory = EvaluatorFactory(config or {})
    result = factory.evaluate_relevance(question, answer, context)
    return result['score']


def quick_coherence_eval(answer: str, config: Dict[str, Any] = None) -> float:
    """Quick coherence evaluation with minimal setup."""
    factory = EvaluatorFactory(config or {})
    result = factory.evaluate_coherence(answer)
    return result['score']


def quick_comprehensive_eval(question: str, answer: str, context: Dict[str, Any] = None, 
                           config: Dict[str, Any] = None) -> Dict[str, float]:
    """Quick comprehensive evaluation with minimal setup."""
    factory = EvaluatorFactory(config or {})
    result = factory.evaluate_comprehensive(question, answer, context or {})
    
    return {
        'overall_quality': result['overall_quality'],
        'relevance': result['relevance'],
        'coherence': result['coherence'],
        'instruction_compliance': result['instruction_compliance'],
        'tool_efficiency': result['tool_efficiency']
    } 