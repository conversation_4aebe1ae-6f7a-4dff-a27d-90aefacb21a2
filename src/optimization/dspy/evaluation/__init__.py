"""
DSPy Evaluation Framework

This module provides comprehensive automated evaluation capabilities for DSPy workflows,
including decomposed metrics, quality gates, and integration with the training pipeline.

Components:
- decomposed_evaluators: Individual metric evaluators (relevance, coherence, etc.)
- evaluation_pipeline: Composite evaluation pipeline
- quality_gates: Quality thresholds and automated escalation
"""

from typing import Dict, Any, Optional

__version__ = "1.0.0"
__all__ = [
    "AutomatedEvaluationPipeline",
    "QualityGateSystem", 
    "QualityThresholds",
    "RelevanceEvaluator",
    "CoherenceEvaluator",
    "InstructionFollowingEvaluator",
    "ToolUsageEvaluator"
]

# Lazy imports to avoid circular dependencies
def get_evaluation_pipeline(config: Dict[str, Any]) -> Optional["AutomatedEvaluationPipeline"]:
    """Get configured evaluation pipeline instance."""
    try:
        from .evaluation_pipeline import AutomatedEvaluationPipeline
        return AutomatedEvaluationPipeline(config)
    except ImportError as e:
        print(f"⚠️ Evaluation pipeline not available: {e}")
        return None

def get_quality_gates(thresholds: "QualityThresholds", config: Dict[str, Any]) -> Optional["QualityGateSystem"]:
    """Get configured quality gate system."""
    try:
        from .quality_gates import QualityGateSystem
        return QualityGateSystem(thresholds, config)
    except ImportError as e:
        print(f"⚠️ Quality gates not available: {e}")
        return None 