"""
Async session storage implementation for the DSPy Multi-Agent System API.

High-performance async storage using aiosqlite with connection pooling.
"""

import json
import asyncio
import logging
import aiosqlite
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, timezone
from pathlib import Path
import uuid
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class AsyncSessionStorage:
    """High-performance async session storage with connection pooling."""
    
    def __init__(self, db_path: str = "data/sessions.db", pool_size: int = 10):
        self.db_path = db_path
        self.pool_size = pool_size
        self._pool = asyncio.Queue(maxsize=pool_size)
        self._initialized = False
        self._init_lock = asyncio.Lock()
        
    async def _ensure_initialized(self):
        """Ensure database and connection pool are initialized."""
        if not self._initialized:
            async with self._init_lock:
                if not self._initialized:
                    await self._init_database()
                    await self._init_connection_pool()
                    self._initialized = True
    
    async def _init_database(self):
        """Initialize SQLite database with optimized settings."""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        async with aiosqlite.connect(self.db_path) as conn:
            # Enable WAL mode for better concurrency
            await conn.execute("PRAGMA journal_mode=WAL")
            await conn.execute("PRAGMA synchronous=NORMAL")
            await conn.execute("PRAGMA cache_size=10000")
            await conn.execute("PRAGMA temp_store=MEMORY")
            
            # Create optimized tables
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    expires_at TEXT
                ) WITHOUT ROWID
            """)
            
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS workflows (
                    workflow_id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    status TEXT NOT NULL,
                    data TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                ) WITHOUT ROWID
            """)
            
            # Create optimized indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_workflows_session ON workflows(session_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_workflows_status ON workflows(status)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_expires ON sessions(expires_at)")
            
            await conn.commit()
            
        logger.info(f"Async session storage initialized: {self.db_path}")
    
    async def _init_connection_pool(self):
        """Initialize connection pool for better performance."""
        for _ in range(self.pool_size):
            conn = await aiosqlite.connect(self.db_path)
            await conn.execute("PRAGMA journal_mode=WAL")
            await self._pool.put(conn)
    
    @asynccontextmanager
    async def _get_connection(self):
        """Get connection from pool with automatic return."""
        await self._ensure_initialized()
        conn = await self._pool.get()
        try:
            yield conn
        finally:
            await self._pool.put(conn)
    
    async def create_session(self, session_id: str, data: Dict[str, Any] = None) -> bool:
        """Create a new session with optimized async operations."""
        try:
            async with self._get_connection() as conn:
                now = datetime.now(timezone.utc).isoformat()
                expires_at = (datetime.now(timezone.utc) + timedelta(hours=24)).isoformat()
                
                await conn.execute("""
                    INSERT OR REPLACE INTO sessions (session_id, data, created_at, updated_at, expires_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    session_id,
                    json.dumps(data or {}),
                    now,
                    now,
                    expires_at
                ))
                
                await conn.commit()
                logger.debug(f"Session created: {session_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to create session {session_id}: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data with optimized query."""
        try:
            async with self._get_connection() as conn:
                async with conn.execute("""
                    SELECT data, expires_at FROM sessions 
                    WHERE session_id = ?
                """, (session_id,)) as cursor:
                    row = await cursor.fetchone()
                
                if not row:
                    return None
                    
                data, expires_at = row
                
                # Check expiration
                if expires_at:
                    expires = datetime.fromisoformat(expires_at)
                    if datetime.now(timezone.utc) > expires:
                        await self.delete_session(session_id)
                        return None
                
                return json.loads(data)
                
        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            return None
    
    async def store_workflow(self, workflow_id: str, session_id: str, status: str, data: Dict[str, Any]) -> bool:
        """Store workflow data with optimized async operations."""
        try:
            async with self._get_connection() as conn:
                now = datetime.now(timezone.utc).isoformat()
                
                await conn.execute("""
                    INSERT OR REPLACE INTO workflows 
                    (workflow_id, session_id, status, data, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    workflow_id,
                    session_id,
                    status,
                    json.dumps(self._make_json_serializable(data)),
                    now,
                    now
                ))
                
                await conn.commit()
                logger.debug(f"Workflow stored: {workflow_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to store workflow {workflow_id}: {e}")
            return False
    
    async def get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow data with optimized query."""
        try:
            async with self._get_connection() as conn:
                async with conn.execute("""
                    SELECT session_id, status, data, created_at, updated_at
                    FROM workflows WHERE workflow_id = ?
                """, (workflow_id,)) as cursor:
                    row = await cursor.fetchone()
                
                if not row:
                    return None
                    
                session_id, status, data, created_at, updated_at = row
                
                return {
                    "workflow_id": workflow_id,
                    "session_id": session_id,
                    "status": status,
                    "data": json.loads(data),
                    "created_at": created_at,
                    "updated_at": updated_at
                }
                
        except Exception as e:
            logger.error(f"Failed to get workflow {workflow_id}: {e}")
            return None
    
    async def update_workflow_status(self, workflow_id: str, status: str, data: Dict[str, Any] = None) -> bool:
        """Update workflow status with optimized async operations."""
        try:
            async with self._get_connection() as conn:
                now = datetime.now(timezone.utc).isoformat()
                
                if data is not None:
                    serializable_data = self._make_json_serializable(data)
                    await conn.execute("""
                        UPDATE workflows
                        SET status = ?, data = ?, updated_at = ?
                        WHERE workflow_id = ?
                    """, (status, json.dumps(serializable_data), now, workflow_id))
                else:
                    await conn.execute("""
                        UPDATE workflows 
                        SET status = ?, updated_at = ?
                        WHERE workflow_id = ?
                    """, (status, now, workflow_id))
                
                await conn.commit()
                logger.debug(f"Workflow status updated: {workflow_id} -> {status}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update workflow {workflow_id}: {e}")
            return False
    
    async def batch_update_workflows(self, updates: List[Dict[str, Any]]) -> bool:
        """Batch update multiple workflows for better performance."""
        try:
            async with self._get_connection() as conn:
                now = datetime.now(timezone.utc).isoformat()
                
                for update in updates:
                    workflow_id = update["workflow_id"]
                    status = update["status"]
                    data = update.get("data")
                    
                    if data is not None:
                        serializable_data = self._make_json_serializable(data)
                        await conn.execute("""
                            UPDATE workflows
                            SET status = ?, data = ?, updated_at = ?
                            WHERE workflow_id = ?
                        """, (status, json.dumps(serializable_data), now, workflow_id))
                    else:
                        await conn.execute("""
                            UPDATE workflows 
                            SET status = ?, updated_at = ?
                            WHERE workflow_id = ?
                        """, (status, now, workflow_id))
                
                await conn.commit()
                logger.debug(f"Batch updated {len(updates)} workflows")
                return True
                
        except Exception as e:
            logger.error(f"Failed to batch update workflows: {e}")
            return False
    
    def _make_json_serializable(self, obj):
        """Convert objects to JSON-serializable format."""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'isoformat'):
            return obj.isoformat()
        else:
            return obj
    
    async def close(self):
        """Close all connections in the pool."""
        while not self._pool.empty():
            conn = await self._pool.get()
            await conn.close()


# Global async session storage instance
_async_session_storage = None


async def get_async_session_storage() -> AsyncSessionStorage:
    """Get the global async session storage instance."""
    global _async_session_storage
    if _async_session_storage is None:
        _async_session_storage = AsyncSessionStorage()
    return _async_session_storage
