"""
Session storage implementation for the DSPy Multi-Agent System API.

Provides session management using SQLite as fallback when Redis is not available.
"""

import json
import sqlite3
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, timezone
from pathlib import Path
import uuid

logger = logging.getLogger(__name__)


class SessionStorage:
    """Session storage with SQLite fallback."""
    
    def __init__(self, db_path: str = "data/sessions.db"):
        self.db_path = db_path
        self._ensure_db_directory()
        self._init_database()
        
    def _ensure_db_directory(self):
        """Ensure database directory exists."""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
    def _init_database(self):
        """Initialize SQLite database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    expires_at TEXT
                )
            """)
            
            # Create workflows table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS workflows (
                    workflow_id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    status TEXT NOT NULL,
                    data TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                )
            """)
            
            # Create indexes
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_workflows_session ON workflows(session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_workflows_status ON workflows(status)")
            
            conn.commit()
            conn.close()
            
            logger.info(f"Session storage initialized: {self.db_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize session storage: {e}")
            raise
    
    async def create_session(self, session_id: str, data: Dict[str, Any] = None) -> bool:
        """Create a new session."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            now = datetime.now(timezone.utc).isoformat()
            expires_at = (datetime.now(timezone.utc) + timedelta(hours=24)).isoformat()
            
            cursor.execute("""
                INSERT OR REPLACE INTO sessions (session_id, data, created_at, updated_at, expires_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                session_id,
                json.dumps(data or {}),
                now,
                now,
                expires_at
            ))
            
            conn.commit()
            conn.close()
            
            logger.debug(f"Session created: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create session {session_id}: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT data, expires_at FROM sessions 
                WHERE session_id = ?
            """, (session_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                return None
                
            data, expires_at = row
            
            # Check if session is expired
            if expires_at:
                expires = datetime.fromisoformat(expires_at)
                if datetime.now(timezone.utc) > expires:
                    await self.delete_session(session_id)
                    return None
            
            return json.loads(data)
            
        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            return None
    
    async def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Update session data."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            now = datetime.now(timezone.utc).isoformat()
            
            cursor.execute("""
                UPDATE sessions 
                SET data = ?, updated_at = ?
                WHERE session_id = ?
            """, (json.dumps(data), now, session_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            
            if success:
                logger.debug(f"Session updated: {session_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update session {session_id}: {e}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session and its workflows."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Delete workflows first (foreign key constraint)
            cursor.execute("DELETE FROM workflows WHERE session_id = ?", (session_id,))
            
            # Delete session
            cursor.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))
            
            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            
            if success:
                logger.debug(f"Session deleted: {session_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            return False
    
    async def store_workflow(self, workflow_id: str, session_id: str, status: str, data: Dict[str, Any]) -> bool:
        """Store workflow data."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            now = datetime.now(timezone.utc).isoformat()
            
            cursor.execute("""
                INSERT OR REPLACE INTO workflows 
                (workflow_id, session_id, status, data, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                workflow_id,
                session_id,
                status,
                json.dumps(data),
                now,
                now
            ))
            
            conn.commit()
            conn.close()
            
            logger.debug(f"Workflow stored: {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store workflow {workflow_id}: {e}")
            return False
    
    async def get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow data."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT session_id, status, data, created_at, updated_at
                FROM workflows WHERE workflow_id = ?
            """, (workflow_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                return None
                
            session_id, status, data, created_at, updated_at = row
            
            return {
                "workflow_id": workflow_id,
                "session_id": session_id,
                "status": status,
                "data": json.loads(data),
                "created_at": created_at,
                "updated_at": updated_at
            }
            
        except Exception as e:
            logger.error(f"Failed to get workflow {workflow_id}: {e}")
            return None
    
    async def update_workflow_status(self, workflow_id: str, status: str, data: Dict[str, Any] = None) -> bool:
        """Update workflow status and optionally data."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            now = datetime.now(timezone.utc).isoformat()
            
            if data is not None:
                # Make data JSON serializable
                serializable_data = self._make_json_serializable(data)
                cursor.execute("""
                    UPDATE workflows
                    SET status = ?, data = ?, updated_at = ?
                    WHERE workflow_id = ?
                """, (status, json.dumps(serializable_data), now, workflow_id))
            else:
                cursor.execute("""
                    UPDATE workflows 
                    SET status = ?, updated_at = ?
                    WHERE workflow_id = ?
                """, (status, now, workflow_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            
            if success:
                logger.debug(f"Workflow status updated: {workflow_id} -> {status}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update workflow {workflow_id}: {e}")
            return False

    def _make_json_serializable(self, obj):
        """Convert datetime objects and other non-serializable objects to JSON-serializable format"""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'isoformat'):  # Handle other datetime-like objects
            return obj.isoformat()
        else:
            return obj

    async def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            now = datetime.now(timezone.utc).isoformat()
            
            # Delete expired sessions and their workflows
            cursor.execute("""
                DELETE FROM workflows WHERE session_id IN (
                    SELECT session_id FROM sessions 
                    WHERE expires_at < ?
                )
            """, (now,))
            
            cursor.execute("DELETE FROM sessions WHERE expires_at < ?", (now,))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} expired sessions")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired sessions: {e}")
            return 0


# Global session storage instance
_session_storage = None


def get_session_storage() -> SessionStorage:
    """Get the global session storage instance."""
    global _session_storage
    if _session_storage is None:
        _session_storage = SessionStorage()
    return _session_storage
