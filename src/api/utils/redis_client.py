"""
Redis client implementation for the DSPy Multi-Agent System API.

Provides Redis connectivity with fallback to in-memory storage.
"""

import os
import json
import asyncio
import logging
from typing import Dict, Any, Optional, Union
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Try to import Redis
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis not available, using in-memory fallback")


class RedisClient:
    """Redis client with in-memory fallback."""
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None,
        use_fallback: bool = True
    ):
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.use_fallback = use_fallback
        
        self.redis_client = None
        self.connected = False
        
        # In-memory fallback storage
        self._memory_store: Dict[str, Any] = {}
        self._expiry_times: Dict[str, datetime] = {}
        
        # Initialize connection
        asyncio.create_task(self._initialize())
    
    async def _initialize(self):
        """Initialize Redis connection."""
        if not REDIS_AVAILABLE:
            logger.info("Using in-memory storage (Redis not available)")
            return
            
        try:
            self.redis_client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            await self.redis_client.ping()
            self.connected = True
            logger.info(f"Connected to Redis at {self.host}:{self.port}")
            
        except Exception as e:
            logger.warning(f"Failed to connect to Redis: {e}")
            if self.use_fallback:
                logger.info("Using in-memory storage as fallback")
            else:
                raise
    
    async def set(
        self, 
        key: str, 
        value: Union[str, Dict, Any], 
        expire: Optional[int] = None
    ) -> bool:
        """Set a key-value pair."""
        try:
            # Serialize value if needed
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            elif not isinstance(value, str):
                value = str(value)
            
            if self.connected and self.redis_client:
                # Use Redis
                await self.redis_client.set(key, value, ex=expire)
                return True
            else:
                # Use in-memory fallback
                self._memory_store[key] = value
                if expire:
                    self._expiry_times[key] = datetime.utcnow() + timedelta(seconds=expire)
                return True
                
        except Exception as e:
            logger.error(f"Failed to set key {key}: {e}")
            return False
    
    async def get(self, key: str) -> Optional[str]:
        """Get a value by key."""
        try:
            if self.connected and self.redis_client:
                # Use Redis
                return await self.redis_client.get(key)
            else:
                # Use in-memory fallback
                self._cleanup_expired()
                return self._memory_store.get(key)
                
        except Exception as e:
            logger.error(f"Failed to get key {key}: {e}")
            return None
    
    async def hset(self, name: str, mapping: Dict[str, Any]) -> bool:
        """Set hash fields."""
        try:
            # Serialize values
            serialized_mapping = {}
            for k, v in mapping.items():
                if isinstance(v, (dict, list)):
                    serialized_mapping[k] = json.dumps(v)
                else:
                    serialized_mapping[k] = str(v)
            
            if self.connected and self.redis_client:
                # Use Redis
                await self.redis_client.hset(name, mapping=serialized_mapping)
                return True
            else:
                # Use in-memory fallback
                if name not in self._memory_store:
                    self._memory_store[name] = {}
                if not isinstance(self._memory_store[name], dict):
                    self._memory_store[name] = {}
                
                self._memory_store[name].update(serialized_mapping)
                return True
                
        except Exception as e:
            logger.error(f"Failed to hset {name}: {e}")
            return False
    
    async def hgetall(self, name: str) -> Dict[str, str]:
        """Get all hash fields."""
        try:
            if self.connected and self.redis_client:
                # Use Redis
                return await self.redis_client.hgetall(name)
            else:
                # Use in-memory fallback
                self._cleanup_expired()
                value = self._memory_store.get(name, {})
                if isinstance(value, dict):
                    return value
                return {}
                
        except Exception as e:
            logger.error(f"Failed to hgetall {name}: {e}")
            return {}
    
    async def delete(self, *keys: str) -> int:
        """Delete keys."""
        try:
            if self.connected and self.redis_client:
                # Use Redis
                return await self.redis_client.delete(*keys)
            else:
                # Use in-memory fallback
                deleted = 0
                for key in keys:
                    if key in self._memory_store:
                        del self._memory_store[key]
                        deleted += 1
                    if key in self._expiry_times:
                        del self._expiry_times[key]
                return deleted
                
        except Exception as e:
            logger.error(f"Failed to delete keys {keys}: {e}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        try:
            if self.connected and self.redis_client:
                # Use Redis
                return bool(await self.redis_client.exists(key))
            else:
                # Use in-memory fallback
                self._cleanup_expired()
                return key in self._memory_store
                
        except Exception as e:
            logger.error(f"Failed to check existence of key {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration for a key."""
        try:
            if self.connected and self.redis_client:
                # Use Redis
                return await self.redis_client.expire(key, seconds)
            else:
                # Use in-memory fallback
                if key in self._memory_store:
                    self._expiry_times[key] = datetime.utcnow() + timedelta(seconds=seconds)
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Failed to set expiration for key {key}: {e}")
            return False
    
    def _cleanup_expired(self):
        """Clean up expired keys in memory storage."""
        now = datetime.utcnow()
        expired_keys = [
            key for key, expiry in self._expiry_times.items()
            if expiry <= now
        ]
        
        for key in expired_keys:
            self._memory_store.pop(key, None)
            self._expiry_times.pop(key, None)
    
    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
            self.connected = False
            logger.info("Redis connection closed")


# Global Redis client instance
_redis_client = None


def get_redis_client() -> RedisClient:
    """Get the global Redis client instance."""
    global _redis_client
    if _redis_client is None:
        # Get configuration from environment
        host = os.getenv("REDIS_HOST", "localhost")
        port = int(os.getenv("REDIS_PORT", "6379"))
        db = int(os.getenv("REDIS_DB", "0"))
        password = os.getenv("REDIS_PASSWORD")
        
        _redis_client = RedisClient(
            host=host,
            port=port,
            db=db,
            password=password
        )
    
    return _redis_client


# Alias for backward compatibility
get_redis = get_redis_client
