"""
Response models for the DSPy Multi-Agent System API.

Defines Pydantic models for all API response payloads.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Literal
from datetime import datetime


class QuestionResponse(BaseModel):
    """Response model for question submission."""
    success: bool = Field(..., description="Whether the request was successful")
    workflow_id: str = Field(..., description="Unique workflow identifier")
    timestamp: datetime = Field(..., description="Request timestamp")


class WorkflowStatusResponse(BaseModel):
    """Response model for workflow status."""
    workflow_id: str = Field(..., description="Unique workflow identifier")
    status: str = Field(..., description="Current workflow status")
    current_phase: str = Field(..., description="Current execution phase")
    final_answer: str = Field(default="", description="Final answer if completed")
    progress: Dict[str, Any] = Field(default_factory=dict, description="Progress information")
    agents: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Agent status details")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="Execution metrics")
    real_time_updates: Optional[Dict[str, str]] = Field(None, description="WebSocket connection info")


class FileUploadResponse(BaseModel):
    """Response model for file upload."""
    success: bool = Field(..., description="Whether upload was successful")
    file_id: str = Field(..., description="Unique file identifier")
    file_name: str = Field(..., description="Uploaded filename")
    timestamp: datetime = Field(..., description="Upload timestamp")


class FileInfo(BaseModel):
    """Information about an uploaded file."""
    file_id: str = Field(..., description="Unique file identifier")
    file_name: str = Field(..., description="Original filename")
    upload_time: datetime = Field(..., description="Upload timestamp")
    processed: bool = Field(default=False, description="Whether file has been processed")
    file_size: Optional[int] = Field(None, description="File size in bytes")


class FileListResponse(BaseModel):
    """Response model for listing session files."""
    files: List[FileInfo] = Field(default_factory=list, description="List of uploaded files")


class HealthResponse(BaseModel):
    """Response model for health check."""
    status: str = Field(..., description="Overall system status")
    timestamp: datetime = Field(..., description="Health check timestamp")
    version: str = Field(..., description="API version")
    environment: str = Field(..., description="Deployment environment")
    components: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict, 
        description="Component health status"
    )
    system_info: Dict[str, Any] = Field(
        default_factory=dict, 
        description="System information"
    )


class ErrorResponse(BaseModel):
    """Standardized error response model."""
    error: str = Field(..., description="Error type/code")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(..., description="Error timestamp")
    request_id: str = Field(..., description="Unique request identifier")


class WorkflowCancelResponse(BaseModel):
    """Response model for workflow cancellation."""
    success: bool = Field(..., description="Whether cancellation was successful")
    workflow_id: str = Field(..., description="Cancelled workflow ID")
    status: str = Field(..., description="Final workflow status")
    message: str = Field(..., description="Cancellation message")
    cancellation_time: datetime = Field(..., description="Cancellation timestamp")


class WebSocketMessage(BaseModel):
    """Base model for WebSocket messages."""
    type: str = Field(..., description="Message type")
    workflow_id: str = Field(..., description="Associated workflow ID")
    timestamp: datetime = Field(..., description="Message timestamp")
    data: Dict[str, Any] = Field(..., description="Message payload")


class StatusUpdateMessage(WebSocketMessage):
    """WebSocket message for status updates."""
    type: Literal["status_update"] = "status_update"


class AgentUpdateMessage(WebSocketMessage):
    """WebSocket message for agent updates."""
    type: Literal["agent_update"] = "agent_update"


class FinalResultMessage(WebSocketMessage):
    """WebSocket message for final result streaming."""
    type: Literal["final_result"] = "final_result"


class WorkflowCompleteMessage(WebSocketMessage):
    """WebSocket message for workflow completion."""
    type: Literal["workflow_complete"] = "workflow_complete"


class ErrorNotificationMessage(WebSocketMessage):
    """WebSocket message for error notifications."""
    type: Literal["error"] = "error"
