"""
Application startup module for initializing components and optimizing performance.

This module handles:
- Pre-initialization of heavy system components
- Database setup and optimization
- Performance monitoring setup
- Health check initialization
"""

import asyncio
import logging
import time
from typing import Dict, Any
from pathlib import Path

# Import configuration
try:
    from src.infrastructure.config.settings import get_config
except ImportError:
    def get_config():
        return {}

# Import component manager and storage
from .services.component_manager import initialize_components_at_startup
from .utils.async_session_storage import get_async_session_storage

logger = logging.getLogger(__name__)


class ApplicationStartup:
    """Manages application startup and component initialization."""
    
    def __init__(self):
        self.startup_time = None
        self.components_initialized = False
        self.startup_metrics = {}
        
    async def initialize_application(self, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Initialize the entire application for optimal performance.
        
        Returns startup metrics and status.
        """
        startup_start = time.time()
        self.startup_time = startup_start
        
        logger.info("🚀 Starting application initialization...")
        logger.info("=" * 60)
        
        try:
            # Load configuration
            if config is None:
                config = get_config()
            
            # Initialize components in parallel where possible
            initialization_tasks = [
                self._initialize_database(),
                self._initialize_components(config),
                self._setup_monitoring(),
            ]
            
            results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
            
            # Check for initialization errors
            errors = [r for r in results if isinstance(r, Exception)]
            if errors:
                logger.error(f"❌ Initialization errors: {errors}")
                raise Exception(f"Startup failed with errors: {errors}")
            
            # Calculate startup metrics
            total_startup_time = time.time() - startup_start
            self.startup_metrics = {
                "total_startup_time": total_startup_time,
                "database_init_time": results[0],
                "components_init_time": results[1], 
                "monitoring_init_time": results[2],
                "startup_timestamp": startup_start,
                "components_initialized": True
            }
            
            self.components_initialized = True
            
            logger.info("=" * 60)
            logger.info(f"✅ Application initialization complete!")
            logger.info(f"   📊 Total startup time: {total_startup_time:.2f}s")
            logger.info(f"   🗄️  Database init: {results[0]:.2f}s")
            logger.info(f"   🔧 Components init: {results[1]:.2f}s") 
            logger.info(f"   📈 Monitoring init: {results[2]:.2f}s")
            logger.info("=" * 60)
            
            return self.startup_metrics
            
        except Exception as e:
            logger.error(f"❌ Application initialization failed: {e}")
            raise
    
    async def _initialize_database(self) -> float:
        """Initialize database and storage systems."""
        start_time = time.time()
        
        logger.info("🗄️  Initializing database systems...")
        
        try:
            # Initialize async session storage
            storage = await get_async_session_storage()
            await storage._ensure_initialized()
            
            # Run database optimization
            await self._optimize_database(storage)
            
            init_time = time.time() - start_time
            logger.info(f"   ✅ Database systems ready ({init_time:.2f}s)")
            return init_time
            
        except Exception as e:
            logger.error(f"   ❌ Database initialization failed: {e}")
            raise
    
    async def _optimize_database(self, storage):
        """Optimize database for better performance."""
        try:
            # Run database maintenance tasks
            async with storage._get_connection() as conn:
                # Analyze tables for query optimization
                await conn.execute("ANALYZE")
                
                # Vacuum database to optimize storage
                await conn.execute("VACUUM")
                
                # Update statistics
                await conn.execute("PRAGMA optimize")
                
            logger.info("   🔧 Database optimization complete")
            
        except Exception as e:
            logger.warning(f"   ⚠️  Database optimization failed: {e}")
    
    async def _initialize_components(self, config: Dict[str, Any]) -> float:
        """Initialize system components."""
        start_time = time.time()
        
        logger.info("🔧 Initializing system components...")
        
        try:
            # Initialize all heavy components
            await initialize_components_at_startup(config)
            
            init_time = time.time() - start_time
            logger.info(f"   ✅ System components ready ({init_time:.2f}s)")
            return init_time
            
        except Exception as e:
            logger.error(f"   ❌ Component initialization failed: {e}")
            raise
    
    async def _setup_monitoring(self) -> float:
        """Setup monitoring and health checks."""
        start_time = time.time()
        
        logger.info("📈 Setting up monitoring...")
        
        try:
            # Setup performance monitoring
            # This would integrate with monitoring systems
            
            # Setup health check endpoints
            # This would configure health check routes
            
            init_time = time.time() - start_time
            logger.info(f"   ✅ Monitoring ready ({init_time:.2f}s)")
            return init_time
            
        except Exception as e:
            logger.warning(f"   ⚠️  Monitoring setup failed: {e}")
            return time.time() - start_time  # Return time even if failed
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive application health check."""
        if not self.components_initialized:
            return {
                "status": "initializing",
                "components_initialized": False,
                "startup_time": None
            }
        
        try:
            # Check database health
            storage = await get_async_session_storage()
            async with storage._get_connection() as conn:
                await conn.execute("SELECT 1")
            db_healthy = True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            db_healthy = False
        
        # Check component health
        try:
            from .services.component_manager import get_component_manager
            manager = await get_component_manager()
            component_health = await manager.health_check()
            components_healthy = component_health.get("initialized", False)
        except Exception as e:
            logger.error(f"Component health check failed: {e}")
            components_healthy = False
        
        return {
            "status": "healthy" if (db_healthy and components_healthy) else "unhealthy",
            "components_initialized": self.components_initialized,
            "database_healthy": db_healthy,
            "components_healthy": components_healthy,
            "startup_metrics": self.startup_metrics,
            "uptime": time.time() - self.startup_time if self.startup_time else 0
        }
    
    async def shutdown(self):
        """Graceful application shutdown."""
        logger.info("🔄 Starting graceful shutdown...")
        
        try:
            # Shutdown components
            from .services.component_manager import shutdown_components
            await shutdown_components()
            
            # Close database connections
            storage = await get_async_session_storage()
            await storage.close()
            
            logger.info("✅ Graceful shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Shutdown error: {e}")


# Global startup manager
_startup_manager = None


async def get_startup_manager() -> ApplicationStartup:
    """Get the global startup manager instance."""
    global _startup_manager
    if _startup_manager is None:
        _startup_manager = ApplicationStartup()
    return _startup_manager


async def initialize_application(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Initialize the application at startup."""
    manager = await get_startup_manager()
    return await manager.initialize_application(config)


async def application_health_check() -> Dict[str, Any]:
    """Get application health status."""
    manager = await get_startup_manager()
    return await manager.health_check()


async def shutdown_application():
    """Shutdown the application gracefully."""
    global _startup_manager
    if _startup_manager:
        await _startup_manager.shutdown()
        _startup_manager = None
