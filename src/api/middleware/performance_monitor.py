"""
Performance monitoring middleware for tracking API response times and workflow performance.

Provides real-time metrics and performance insights for optimization.
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """Tracks performance metrics for API endpoints and workflows."""
    
    def __init__(self):
        self.metrics = {
            "api_requests": {
                "total_requests": 0,
                "avg_response_time": 0.0,
                "min_response_time": float('inf'),
                "max_response_time": 0.0,
                "error_count": 0,
                "endpoint_metrics": {}
            },
            "workflows": {
                "total_workflows": 0,
                "avg_execution_time": 0.0,
                "min_execution_time": float('inf'),
                "max_execution_time": 0.0,
                "success_rate": 0.0,
                "workflow_type_metrics": {}
            },
            "database": {
                "total_operations": 0,
                "avg_operation_time": 0.0,
                "connection_pool_usage": 0.0
            }
        }
        self.start_time = time.time()
    
    def track_api_request(self, endpoint: str, method: str, response_time: float, status_code: int):
        """Track API request performance."""
        api_metrics = self.metrics["api_requests"]
        
        # Update overall metrics
        api_metrics["total_requests"] += 1
        
        # Update response time metrics
        if response_time < api_metrics["min_response_time"]:
            api_metrics["min_response_time"] = response_time
        if response_time > api_metrics["max_response_time"]:
            api_metrics["max_response_time"] = response_time
        
        # Calculate rolling average
        total_requests = api_metrics["total_requests"]
        api_metrics["avg_response_time"] = (
            (api_metrics["avg_response_time"] * (total_requests - 1) + response_time) / total_requests
        )
        
        # Track errors
        if status_code >= 400:
            api_metrics["error_count"] += 1
        
        # Track per-endpoint metrics
        endpoint_key = f"{method} {endpoint}"
        if endpoint_key not in api_metrics["endpoint_metrics"]:
            api_metrics["endpoint_metrics"][endpoint_key] = {
                "requests": 0,
                "avg_response_time": 0.0,
                "min_response_time": float('inf'),
                "max_response_time": 0.0,
                "error_count": 0
            }
        
        endpoint_stats = api_metrics["endpoint_metrics"][endpoint_key]
        endpoint_stats["requests"] += 1
        
        if response_time < endpoint_stats["min_response_time"]:
            endpoint_stats["min_response_time"] = response_time
        if response_time > endpoint_stats["max_response_time"]:
            endpoint_stats["max_response_time"] = response_time
        
        endpoint_stats["avg_response_time"] = (
            (endpoint_stats["avg_response_time"] * (endpoint_stats["requests"] - 1) + response_time) / 
            endpoint_stats["requests"]
        )
        
        if status_code >= 400:
            endpoint_stats["error_count"] += 1
    
    def track_workflow_execution(self, workflow_type: str, execution_time: float, success: bool):
        """Track workflow execution performance."""
        workflow_metrics = self.metrics["workflows"]
        
        # Update overall metrics
        workflow_metrics["total_workflows"] += 1
        
        # Update execution time metrics
        if execution_time < workflow_metrics["min_execution_time"]:
            workflow_metrics["min_execution_time"] = execution_time
        if execution_time > workflow_metrics["max_execution_time"]:
            workflow_metrics["max_execution_time"] = execution_time
        
        # Calculate rolling average
        total_workflows = workflow_metrics["total_workflows"]
        workflow_metrics["avg_execution_time"] = (
            (workflow_metrics["avg_execution_time"] * (total_workflows - 1) + execution_time) / total_workflows
        )
        
        # Update success rate
        current_success_count = workflow_metrics["success_rate"] * (total_workflows - 1)
        if success:
            current_success_count += 1
        workflow_metrics["success_rate"] = current_success_count / total_workflows
        
        # Track per-workflow-type metrics
        if workflow_type not in workflow_metrics["workflow_type_metrics"]:
            workflow_metrics["workflow_type_metrics"][workflow_type] = {
                "executions": 0,
                "avg_execution_time": 0.0,
                "min_execution_time": float('inf'),
                "max_execution_time": 0.0,
                "success_rate": 0.0
            }
        
        type_stats = workflow_metrics["workflow_type_metrics"][workflow_type]
        type_stats["executions"] += 1
        
        if execution_time < type_stats["min_execution_time"]:
            type_stats["min_execution_time"] = execution_time
        if execution_time > type_stats["max_execution_time"]:
            type_stats["max_execution_time"] = execution_time
        
        type_stats["avg_execution_time"] = (
            (type_stats["avg_execution_time"] * (type_stats["executions"] - 1) + execution_time) / 
            type_stats["executions"]
        )
        
        # Update type-specific success rate
        current_type_success_count = type_stats["success_rate"] * (type_stats["executions"] - 1)
        if success:
            current_type_success_count += 1
        type_stats["success_rate"] = current_type_success_count / type_stats["executions"]
    
    def track_database_operation(self, operation_time: float):
        """Track database operation performance."""
        db_metrics = self.metrics["database"]
        
        db_metrics["total_operations"] += 1
        
        # Calculate rolling average
        total_ops = db_metrics["total_operations"]
        db_metrics["avg_operation_time"] = (
            (db_metrics["avg_operation_time"] * (total_ops - 1) + operation_time) / total_ops
        )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        uptime = time.time() - self.start_time
        
        # Calculate requests per second
        api_metrics = self.metrics["api_requests"]
        rps = api_metrics["total_requests"] / uptime if uptime > 0 else 0
        
        # Calculate error rate
        error_rate = (api_metrics["error_count"] / api_metrics["total_requests"]) if api_metrics["total_requests"] > 0 else 0
        
        return {
            "uptime_seconds": uptime,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "api_performance": {
                "requests_per_second": rps,
                "avg_response_time_ms": api_metrics["avg_response_time"] * 1000,
                "min_response_time_ms": api_metrics["min_response_time"] * 1000 if api_metrics["min_response_time"] != float('inf') else 0,
                "max_response_time_ms": api_metrics["max_response_time"] * 1000,
                "error_rate": error_rate,
                "total_requests": api_metrics["total_requests"]
            },
            "workflow_performance": {
                "avg_execution_time_seconds": self.metrics["workflows"]["avg_execution_time"],
                "min_execution_time_seconds": self.metrics["workflows"]["min_execution_time"] if self.metrics["workflows"]["min_execution_time"] != float('inf') else 0,
                "max_execution_time_seconds": self.metrics["workflows"]["max_execution_time"],
                "success_rate": self.metrics["workflows"]["success_rate"],
                "total_workflows": self.metrics["workflows"]["total_workflows"]
            },
            "database_performance": {
                "avg_operation_time_ms": self.metrics["database"]["avg_operation_time"] * 1000,
                "total_operations": self.metrics["database"]["total_operations"]
            },
            "detailed_metrics": self.metrics
        }
    
    def get_alerts(self) -> List[Dict[str, Any]]:
        """Get performance alerts based on thresholds."""
        alerts = []
        
        api_metrics = self.metrics["api_requests"]
        workflow_metrics = self.metrics["workflows"]
        
        # API response time alerts
        if api_metrics["avg_response_time"] > 1.0:  # > 1 second
            alerts.append({
                "type": "high_api_response_time",
                "severity": "warning",
                "message": f"Average API response time is {api_metrics['avg_response_time']:.2f}s",
                "threshold": 1.0,
                "current_value": api_metrics["avg_response_time"]
            })
        
        # Error rate alerts
        if api_metrics["total_requests"] > 0:
            error_rate = api_metrics["error_count"] / api_metrics["total_requests"]
            if error_rate > 0.1:  # > 10% error rate
                alerts.append({
                    "type": "high_error_rate",
                    "severity": "critical",
                    "message": f"API error rate is {error_rate:.1%}",
                    "threshold": 0.1,
                    "current_value": error_rate
                })
        
        # Workflow execution time alerts
        if workflow_metrics["avg_execution_time"] > 300:  # > 5 minutes
            alerts.append({
                "type": "slow_workflow_execution",
                "severity": "warning",
                "message": f"Average workflow execution time is {workflow_metrics['avg_execution_time']:.1f}s",
                "threshold": 300,
                "current_value": workflow_metrics["avg_execution_time"]
            })
        
        # Workflow success rate alerts
        if workflow_metrics["success_rate"] < 0.9:  # < 90% success rate
            alerts.append({
                "type": "low_workflow_success_rate",
                "severity": "critical",
                "message": f"Workflow success rate is {workflow_metrics['success_rate']:.1%}",
                "threshold": 0.9,
                "current_value": workflow_metrics["success_rate"]
            })
        
        return alerts


class PerformanceMiddleware(BaseHTTPMiddleware):
    """FastAPI middleware for automatic performance tracking."""
    
    def __init__(self, app, monitor: PerformanceMonitor):
        super().__init__(app)
        self.monitor = monitor
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Process request
        response = await call_next(request)
        
        # Calculate response time
        response_time = time.time() - start_time
        
        # Track metrics
        endpoint = request.url.path
        method = request.method
        status_code = response.status_code
        
        self.monitor.track_api_request(endpoint, method, response_time, status_code)
        
        # Add performance headers
        response.headers["X-Response-Time"] = f"{response_time:.3f}s"
        response.headers["X-Request-ID"] = str(id(request))
        
        return response


# Global performance monitor
_performance_monitor = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor
