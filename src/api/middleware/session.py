"""
Session management middleware for the DSPy Multi-Agent System API.

Handles session tracking and context management.
"""

import uuid
from typing import Op<PERSON>
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import logging

logger = logging.getLogger(__name__)


class SessionMiddleware(BaseHTTPMiddleware):
    """Middleware for session management and tracking."""
    
    def __init__(self, app):
        super().__init__(app)
        
    async def dispatch(self, request: Request, call_next):
        """Process request and manage session context."""
        # Generate unique request ID if not already set
        if not hasattr(request.state, 'request_id'):
            request.state.request_id = str(uuid.uuid4())
        
        # Extract session information from request
        session_info = await self._extract_session_info(request)
        request.state.session_info = session_info
        
        # Log request start
        logger.info(
            f"Request started: {request.method} {request.url.path} "
            f"[{request.state.request_id}] "
            f"Session: {session_info.get('session_id', 'none')}"
        )
        
        try:
            response = await call_next(request)
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request.state.request_id
            
            # Log successful request
            logger.info(
                f"Request completed: {request.method} {request.url.path} "
                f"[{request.state.request_id}] Status: {response.status_code}"
            )
            
            return response
            
        except Exception as e:
            # Log error
            logger.error(
                f"Request failed: {request.method} {request.url.path} "
                f"[{request.state.request_id}] Error: {str(e)}"
            )
            raise
    
    async def _extract_session_info(self, request: Request) -> dict:
        """Extract session information from request."""
        session_info = {}
        
        # Try to get session ID from various sources
        session_id = None
        
        # 1. From query parameters
        if "session_id" in request.query_params:
            session_id = request.query_params["session_id"]
        
        # 2. From headers
        elif "X-Session-ID" in request.headers:
            session_id = request.headers["X-Session-ID"]
        
        # 3. From request body (for POST requests)
        elif request.method in ["POST", "PUT", "PATCH"]:
            try:
                # This is a simplified approach - in practice you'd need to
                # handle different content types properly
                if hasattr(request, '_json'):
                    body = await request.json()
                    if isinstance(body, dict) and "session_id" in body:
                        session_id = body["session_id"]
            except:
                # If we can't parse the body, that's okay
                pass
        
        session_info["session_id"] = session_id
        session_info["client_ip"] = request.client.host if request.client else "unknown"
        session_info["user_agent"] = request.headers.get("User-Agent", "unknown")
        
        return session_info


class RequestContextMiddleware(BaseHTTPMiddleware):
    """Middleware to add request context information."""
    
    async def dispatch(self, request: Request, call_next):
        """Add context information to request."""
        # Add timing information
        import time
        start_time = time.time()
        request.state.start_time = start_time
        
        # Add request metadata
        request.state.method = request.method
        request.state.path = request.url.path
        request.state.query_params = dict(request.query_params)
        
        response = await call_next(request)
        
        # Calculate request duration
        duration = time.time() - start_time
        request.state.duration = duration
        
        # Add timing header
        response.headers["X-Response-Time"] = f"{duration:.3f}s"
        
        return response


def get_request_id(request: Request) -> str:
    """Get request ID from request state."""
    return getattr(request.state, 'request_id', 'unknown')


def get_session_id(request: Request) -> Optional[str]:
    """Get session ID from request state."""
    session_info = getattr(request.state, 'session_info', {})
    return session_info.get('session_id')


def get_client_info(request: Request) -> dict:
    """Get client information from request state."""
    session_info = getattr(request.state, 'session_info', {})
    return {
        'ip': session_info.get('client_ip', 'unknown'),
        'user_agent': session_info.get('user_agent', 'unknown'),
        'session_id': session_info.get('session_id')
    }
