"""
Component manager for pre-initializing heavy system components.

Manages lifecycle of MultiAgentSystem and other heavy components to avoid
initialization overhead during workflow execution.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


class ComponentManager:
    """Manages pre-initialized system components for optimal performance."""
    
    def __init__(self):
        self._components = {}
        self._initialization_times = {}
        self._health_status = {}
        self._initialized = False
        self._init_lock = asyncio.Lock()
        
    async def initialize_all_components(self, config: Dict[str, Any] = None):
        """Initialize all heavy components at startup."""
        if self._initialized:
            return
            
        async with self._init_lock:
            if self._initialized:
                return
                
            logger.info("🚀 Initializing system components for optimal performance...")
            start_time = time.time()
            
            try:
                # Initialize components in parallel where possible
                await asyncio.gather(
                    self._init_multi_agent_system(config),
                    self._init_workflow_flows(config),
                    self._init_evaluation_pipeline(config),
                    return_exceptions=True
                )
                
                self._initialized = True
                total_time = time.time() - start_time
                logger.info(f"✅ All components initialized in {total_time:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ Component initialization failed: {e}")
                raise
    
    async def _init_multi_agent_system(self, config: Dict[str, Any] = None):
        """Initialize MultiAgentSystem."""
        try:
            start_time = time.time()
            logger.info("   🔧 Initializing MultiAgentSystem...")

            # Import and initialize - this MUST work for the app to function
            from src.main import MultiAgentSystem
            system = MultiAgentSystem()

            # Store the initialized system
            self._components["multi_agent_system"] = system
            self._initialization_times["multi_agent_system"] = time.time() - start_time
            self._health_status["multi_agent_system"] = "healthy"

            logger.info(f"   ✅ MultiAgentSystem ready ({self._initialization_times['multi_agent_system']:.2f}s)")

        except Exception as e:
            logger.error(f"   ❌ MultiAgentSystem initialization failed: {e}")
            logger.error(f"   🔥 CRITICAL: Cannot start API without MultiAgentSystem!")
            self._health_status["multi_agent_system"] = "failed"
            # This is critical - we MUST have the MultiAgentSystem
            raise RuntimeError(f"CRITICAL: MultiAgentSystem initialization failed: {e}")
    
    async def _init_workflow_flows(self, config: Dict[str, Any] = None):
        """Initialize workflow flows."""
        try:
            start_time = time.time()
            logger.info("   🔧 Initializing workflow flows...")
            
            # Import flow classes
            from src.orchestration.flows.advanced_coordination_flow import AdvancedCoordinationFlow
            from src.orchestration.flows.main_workflow import MainWorkflowFlow
            
            # Pre-initialize flow classes (but not instances)
            self._components["advanced_coordination_flow_class"] = AdvancedCoordinationFlow
            self._components["main_workflow_flow_class"] = MainWorkflowFlow
            
            self._initialization_times["workflow_flows"] = time.time() - start_time
            self._health_status["workflow_flows"] = "healthy"
            
            logger.info(f"   ✅ Workflow flows ready ({self._initialization_times['workflow_flows']:.2f}s)")
            
        except Exception as e:
            logger.error(f"   ❌ Workflow flows initialization failed: {e}")
            self._health_status["workflow_flows"] = "failed"
            raise
    
    async def _init_evaluation_pipeline(self, config: Dict[str, Any] = None):
        """Initialize evaluation pipeline."""
        try:
            start_time = time.time()
            logger.info("   🔧 Initializing evaluation pipeline...")

            # Import evaluation components
            from src.optimization.dspy.evaluation.evaluation_pipeline import AutomatedEvaluationPipeline
            from src.optimization.dspy.evaluation.quality_gates import QualityGateSystem

            # Pre-initialize evaluation pipeline with config
            evaluation_config = config or {}
            evaluation_pipeline = AutomatedEvaluationPipeline(evaluation_config)

            # Initialize quality gates with default thresholds (optional component)
            try:
                from src.optimization.dspy.evaluation.quality_gates import QualityThresholds
                default_thresholds = QualityThresholds()
                quality_gates = QualityGateSystem(default_thresholds, evaluation_config)
            except Exception as qg_error:
                logger.warning(f"   ⚠️  Quality gates initialization failed: {qg_error}")
                quality_gates = None

            self._components["evaluation_pipeline"] = evaluation_pipeline
            self._components["quality_gates"] = quality_gates

            self._initialization_times["evaluation_pipeline"] = time.time() - start_time
            self._health_status["evaluation_pipeline"] = "healthy"

            logger.info(f"   ✅ Evaluation pipeline ready ({self._initialization_times['evaluation_pipeline']:.2f}s)")

        except Exception as e:
            logger.error(f"   ❌ Evaluation pipeline initialization failed: {e}")
            self._health_status["evaluation_pipeline"] = "failed"
            # Don't raise - evaluation is optional
            self._components["evaluation_pipeline"] = None
            self._components["quality_gates"] = None
    
    def get_multi_agent_system(self):
        """Get pre-initialized MultiAgentSystem."""
        if not self._initialized:
            raise RuntimeError("Components not initialized. Call initialize_all_components() first.")
        
        system = self._components.get("multi_agent_system")
        if not system:
            raise RuntimeError("MultiAgentSystem not available")
        
        return system
    
    def get_advanced_coordination_flow_class(self):
        """Get AdvancedCoordinationFlow class."""
        if not self._initialized:
            raise RuntimeError("Components not initialized. Call initialize_all_components() first.")
        
        flow_class = self._components.get("advanced_coordination_flow_class")
        if not flow_class:
            raise RuntimeError("AdvancedCoordinationFlow not available")
        
        return flow_class
    
    def get_evaluation_pipeline(self):
        """Get pre-initialized evaluation pipeline."""
        return self._components.get("evaluation_pipeline")
    
    async def health_check(self) -> Dict[str, Any]:
        """Get health status of all components."""
        return {
            "initialized": self._initialized,
            "components": self._health_status.copy(),
            "initialization_times": self._initialization_times.copy(),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    async def reinitialize_component(self, component_name: str, config: Dict[str, Any] = None):
        """Reinitialize a specific component."""
        logger.info(f"🔄 Reinitializing component: {component_name}")
        
        if component_name == "multi_agent_system":
            await self._init_multi_agent_system(config)
        elif component_name == "workflow_flows":
            await self._init_workflow_flows(config)
        elif component_name == "evaluation_pipeline":
            await self._init_evaluation_pipeline(config)
        else:
            raise ValueError(f"Unknown component: {component_name}")
    
    async def shutdown(self):
        """Shutdown all components gracefully."""
        logger.info("🔄 Shutting down components...")
        
        # Cleanup components if they have cleanup methods
        for name, component in self._components.items():
            try:
                if hasattr(component, 'shutdown'):
                    await component.shutdown()
                elif hasattr(component, 'close'):
                    await component.close()
            except Exception as e:
                logger.warning(f"Error shutting down {name}: {e}")
        
        self._components.clear()
        self._health_status.clear()
        self._initialization_times.clear()
        self._initialized = False
        
        logger.info("✅ Components shutdown complete")


# Global component manager instance
_component_manager = None


async def get_component_manager() -> ComponentManager:
    """Get the global component manager instance."""
    global _component_manager
    if _component_manager is None:
        _component_manager = ComponentManager()
    return _component_manager


async def initialize_components_at_startup(config: Dict[str, Any] = None):
    """Initialize all components at application startup."""
    manager = await get_component_manager()
    await manager.initialize_all_components(config)


async def shutdown_components():
    """Shutdown all components at application shutdown."""
    global _component_manager
    if _component_manager:
        await _component_manager.shutdown()
        _component_manager = None
