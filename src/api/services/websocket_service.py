"""
WebSocket service for the DSPy Multi-Agent System API.

Provides real-time workflow updates via WebSocket connections.
"""

import asyncio
import json
import logging
from typing import Dict, Set, Any, Optional
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect

from ..utils.async_session_storage import get_async_session_storage
from ..models.responses import (
    StatusUpdateMessage, AgentUpdateMessage, FinalResultMessage,
    WorkflowCompleteMessage, ErrorNotificationMessage
)

logger = logging.getLogger(__name__)


class WebSocketManager:
    """
    Manages WebSocket connections for real-time workflow updates.
    
    Monitors workflow status and sends updates to connected clients.
    """
    
    def __init__(self):
        # Active connections: workflow_id -> set of websockets
        self.active_connections: Dict[str, Set[WebSocket]] = {}

        # Connection metadata
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}

        # Storage for workflow monitoring (will be initialized async)
        self.storage = None

        # Monitoring tasks
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}

        logger.info("WebSocketManager initialized")

    async def _ensure_storage_initialized(self):
        """Ensure async storage is initialized."""
        if self.storage is None:
            self.storage = await get_async_session_storage()
    
    async def connect(self, websocket: WebSocket, workflow_id: str, api_key: Optional[str] = None):
        """Accept WebSocket connection and start monitoring."""
        try:
            # Ensure storage is initialized
            await self._ensure_storage_initialized()

            await websocket.accept()

            # Add to active connections
            if workflow_id not in self.active_connections:
                self.active_connections[workflow_id] = set()
            self.active_connections[workflow_id].add(websocket)
            
            # Store connection metadata
            self.connection_metadata[websocket] = {
                "workflow_id": workflow_id,
                "api_key": api_key,
                "connected_at": datetime.utcnow(),
                "last_ping": datetime.utcnow()
            }
            
            logger.info(f"WebSocket connected for workflow {workflow_id}")
            
            # Start monitoring workflow if not already monitoring
            if workflow_id not in self.monitoring_tasks:
                self.monitoring_tasks[workflow_id] = asyncio.create_task(
                    self._monitor_workflow(workflow_id)
                )
            
            # Send initial status
            await self._send_initial_status(websocket, workflow_id)
            
        except Exception as e:
            logger.error(f"Failed to connect WebSocket for workflow {workflow_id}: {e}")
            await websocket.close()
    
    async def disconnect(self, websocket: WebSocket):
        """Handle WebSocket disconnection."""
        try:
            metadata = self.connection_metadata.get(websocket, {})
            workflow_id = metadata.get("workflow_id")
            
            if workflow_id and workflow_id in self.active_connections:
                self.active_connections[workflow_id].discard(websocket)
                
                # If no more connections for this workflow, stop monitoring
                if not self.active_connections[workflow_id]:
                    del self.active_connections[workflow_id]
                    
                    if workflow_id in self.monitoring_tasks:
                        self.monitoring_tasks[workflow_id].cancel()
                        del self.monitoring_tasks[workflow_id]
            
            # Remove connection metadata
            if websocket in self.connection_metadata:
                del self.connection_metadata[websocket]
            
            logger.info(f"WebSocket disconnected for workflow {workflow_id}")
            
        except Exception as e:
            logger.error(f"Error during WebSocket disconnect: {e}")
    
    async def _send_initial_status(self, websocket: WebSocket, workflow_id: str):
        """Send initial workflow status to newly connected client."""
        try:
            workflow_data = await self.storage.get_workflow(workflow_id)
            
            if workflow_data:
                message = StatusUpdateMessage(
                    workflow_id=workflow_id,
                    timestamp=datetime.utcnow(),
                    data={
                        "status": workflow_data["status"],
                        "current_phase": workflow_data["data"].get("current_phase", "unknown"),
                        "progress": workflow_data["data"].get("progress", {}),
                        "message": f"Connected to workflow {workflow_id}"
                    }
                )
                
                await websocket.send_text(message.model_dump_json())
            
        except Exception as e:
            logger.error(f"Failed to send initial status for {workflow_id}: {e}")
    
    async def _monitor_workflow(self, workflow_id: str):
        """Monitor workflow status and send updates."""
        logger.info(f"Starting workflow monitoring for {workflow_id}")
        
        last_status = None
        last_update = None
        
        try:
            while workflow_id in self.active_connections:
                # Get current workflow data
                workflow_data = await self.storage.get_workflow(workflow_id)
                
                if not workflow_data:
                    await self._broadcast_error(
                        workflow_id,
                        "workflow_not_found",
                        f"Workflow {workflow_id} not found"
                    )
                    break
                
                current_status = workflow_data["status"]
                current_update = workflow_data.get("updated_at")
                
                # Check if status or data changed
                if current_status != last_status or current_update != last_update:
                    await self._broadcast_status_update(workflow_id, workflow_data)
                    last_status = current_status
                    last_update = current_update
                
                # Check if workflow is complete
                if current_status in ["completed", "failed", "cancelled"]:
                    await self._broadcast_workflow_complete(workflow_id, workflow_data)
                    break
                
                # Wait before next check
                await asyncio.sleep(1)
                
        except asyncio.CancelledError:
            logger.info(f"Workflow monitoring cancelled for {workflow_id}")
        except Exception as e:
            logger.error(f"Error monitoring workflow {workflow_id}: {e}")
            await self._broadcast_error(workflow_id, "monitoring_error", str(e))
    
    async def _broadcast_status_update(self, workflow_id: str, workflow_data: Dict[str, Any]):
        """Broadcast status update to all connected clients."""
        if workflow_id not in self.active_connections:
            return
        
        try:
            data = workflow_data["data"]
            
            message = StatusUpdateMessage(
                workflow_id=workflow_id,
                timestamp=datetime.utcnow(),
                data={
                    "status": workflow_data["status"],
                    "current_phase": data.get("current_phase", "unknown"),
                    "progress": data.get("progress", {}),
                    "latest_output": data.get("latest_output", ""),
                    "estimated_completion": data.get("estimated_completion")
                }
            )
            
            await self._send_to_all(workflow_id, message.model_dump_json())
            
        except Exception as e:
            logger.error(f"Failed to broadcast status update for {workflow_id}: {e}")
    
    async def _broadcast_workflow_complete(self, workflow_id: str, workflow_data: Dict[str, Any]):
        """Broadcast workflow completion."""
        if workflow_id not in self.active_connections:
            return
        
        try:
            data = workflow_data["data"]
            result = data.get("result", {})
            
            # Send final result if available
            if result.get("final_answer"):
                final_message = FinalResultMessage(
                    workflow_id=workflow_id,
                    timestamp=datetime.utcnow(),
                    data={
                        "chunk": result["final_answer"]
                    }
                )
                await self._send_to_all(workflow_id, final_message.model_dump_json())
            
            # Send completion message
            complete_message = WorkflowCompleteMessage(
                workflow_id=workflow_id,
                timestamp=datetime.utcnow(),
                data={
                    "status": workflow_data["status"],
                    "final_answer": result.get("final_answer", ""),
                    "execution_time": self._calculate_execution_time(workflow_data),
                    "quality_score": result.get("quality_metrics", {}).get("answer_quality_score", 0.0)
                }
            )
            
            await self._send_to_all(workflow_id, complete_message.model_dump_json())
            
        except Exception as e:
            logger.error(f"Failed to broadcast workflow completion for {workflow_id}: {e}")
    
    async def _broadcast_error(self, workflow_id: str, error_type: str, message: str):
        """Broadcast error notification."""
        if workflow_id not in self.active_connections:
            return
        
        try:
            error_message = ErrorNotificationMessage(
                workflow_id=workflow_id,
                timestamp=datetime.utcnow(),
                data={
                    "error_type": error_type,
                    "message": message,
                    "recoverable": False
                }
            )
            
            await self._send_to_all(workflow_id, error_message.model_dump_json())
            
        except Exception as e:
            logger.error(f"Failed to broadcast error for {workflow_id}: {e}")
    
    async def _send_to_all(self, workflow_id: str, message: str):
        """Send message to all connected clients for a workflow."""
        if workflow_id not in self.active_connections:
            return
        
        disconnected = set()
        
        for websocket in self.active_connections[workflow_id].copy():
            try:
                await websocket.send_text(message)
            except WebSocketDisconnect:
                disconnected.add(websocket)
            except Exception as e:
                logger.error(f"Failed to send message to WebSocket: {e}")
                disconnected.add(websocket)
        
        # Remove disconnected websockets
        for websocket in disconnected:
            await self.disconnect(websocket)
    
    def _calculate_execution_time(self, workflow_data: Dict[str, Any]) -> float:
        """Calculate workflow execution time."""
        try:
            created_at = datetime.fromisoformat(workflow_data["created_at"])
            
            if workflow_data["status"] == "completed":
                completed_at = datetime.fromisoformat(workflow_data["data"].get("completed_at", workflow_data["updated_at"]))
                return (completed_at - created_at).total_seconds()
            else:
                return (datetime.utcnow() - created_at).total_seconds()
                
        except Exception:
            return 0.0
    
    async def send_agent_update(self, workflow_id: str, agent_id: str, status: str, progress: float, output: str):
        """Send agent-specific update."""
        if workflow_id not in self.active_connections:
            return
        
        try:
            message = AgentUpdateMessage(
                workflow_id=workflow_id,
                timestamp=datetime.utcnow(),
                data={
                    "agent_id": agent_id,
                    "status": status,
                    "progress": progress,
                    "output": output,
                    "next_agent": None  # Could be determined based on workflow logic
                }
            )
            
            await self._send_to_all(workflow_id, message.model_dump_json())
            
        except Exception as e:
            logger.error(f"Failed to send agent update for {workflow_id}: {e}")
    
    def get_connection_count(self, workflow_id: str) -> int:
        """Get number of active connections for a workflow."""
        return len(self.active_connections.get(workflow_id, set()))
    
    def get_total_connections(self) -> int:
        """Get total number of active connections."""
        return sum(len(connections) for connections in self.active_connections.values())

    async def broadcast_status_update(self, workflow_id: str, workflow_data: Dict[str, Any]):
        """Broadcast status update to all connected clients."""
        await self._broadcast_status_update(workflow_id, workflow_data)

    async def broadcast_final_result(self, workflow_id: str, data: Dict[str, Any]):
        """Broadcast final result streaming."""
        if workflow_id not in self.active_connections:
            return

        try:
            message = FinalResultMessage(
                workflow_id=workflow_id,
                timestamp=datetime.utcnow(),
                data=data
            )

            await self._send_to_all(workflow_id, message.model_dump_json())

        except Exception as e:
            logger.error(f"Failed to broadcast final result for {workflow_id}: {e}")

    async def broadcast_workflow_complete(self, workflow_id: str, workflow_data: Dict[str, Any]):
        """Broadcast workflow completion."""
        await self._broadcast_workflow_complete(workflow_id, workflow_data)
