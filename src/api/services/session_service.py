"""
Session service for the DSPy Multi-Agent System API.

Manages session-based file storage and isolation using the existing vector database.
"""

import aiofiles
import uuid
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import UploadFile
from datetime import datetime

# Import existing components
import sys
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.infrastructure.storage.enterprise_vector_db import EnterpriseVectorDatabase
    from src.infrastructure.storage.enterprise_embedding_service import EnterpriseEmbeddingService
except ImportError as e:
    logging.error(f"Failed to import vector database components: {e}")
    # Create mock implementations for development
    class EnterpriseVectorDatabase:
        def __init__(self, config):
            self.config = config
        
        async def upsert_documents(self, documents):
            return True
        
        async def semantic_search(self, query_vector, limit=10, filters=None):
            return []
    
    class EnterpriseEmbeddingService:
        def __init__(self, config):
            self.config = config
        
        async def get_embedding_async(self, text):
            return [0.0] * 1536  # Mock embedding

from ..models.errors import InternalServerError

logger = logging.getLogger(__name__)


class SessionService:
    """
    Manages session-based file storage and isolation.
    
    Integration with existing system:
    - Uses EnterpriseVectorDatabase with metadata filtering for session isolation
    - Uses EnterpriseEmbeddingService for file processing
    - Reuses existing vector DB configuration
    """
    
    def __init__(self):
        self.upload_dir = Path("data/uploads")
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Reuse existing vector DB configuration (CORRECTED - Issue #2)
        # Instead of creating separate instance, we'll use session metadata filtering
        self.vector_db = EnterpriseVectorDatabase({
            "provider": "chroma",
            "collection_name": "multi_agent_kb",  # Same as main system
            "persist_directory": "./data/chroma_db",  # Same as main system
            "dimension": 1536
        })
        
        # Use existing embedding service configuration
        self.embedding_service = EnterpriseEmbeddingService({
            "provider": "openai",
            "model_name": "text-embedding-3-small",
            "dimension": 1536,
            "batch_size": 100,
            "cache_enabled": True
        })
        
        logger.info("SessionService initialized with existing vector DB configuration")
        
    async def store_session_file(
        self,
        session_id: str,
        file: UploadFile,
        custom_name: Optional[str] = None
    ) -> str:
        """
        Store file with session isolation using existing vector DB.
        
        Uses metadata filtering for session isolation instead of separate collections.
        """
        try:
            file_id = str(uuid.uuid4())
            filename = custom_name or file.filename or f"file_{file_id}"
            
            # Enhanced file validation (FIXED - Issue #8)
            content = await file.read()
            await self._validate_file_security(content, file.content_type, filename)
            
            # Save file to disk
            file_path = self.upload_dir / session_id / file_id
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            
            # Process text content for vector storage
            text_content = await self._extract_text_content(content, file.content_type, filename)
            
            # Generate embedding using existing service
            embedding = await self.embedding_service.get_embedding_async(text_content)
            
            # Store in vector DB with session metadata for isolation
            await self.vector_db.upsert_documents([{
                "id": file_id,
                "content": text_content,
                "embedding": embedding,
                "metadata": {
                    "session_id": session_id,  # KEY: Session isolation
                    "filename": filename,
                    "file_path": str(file_path),
                    "upload_time": datetime.utcnow().isoformat(),
                    "file_size": len(content),
                    "content_type": file.content_type or "application/octet-stream",
                    "document_type": "session_file"  # Distinguish from other documents
                },
                "source": f"session_upload_{session_id}",
                "document_type": "file"
            }])
            
            logger.info(f"File stored for session {session_id}: {filename} ({file_id})")
            return file_id
            
        except Exception as e:
            logger.error(f"Failed to store file for session {session_id}: {e}")
            raise InternalServerError(f"Failed to store file: {str(e)}")
        
    async def get_session_file_context(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get file context for session using vector DB metadata filtering.
        
        Uses existing vector DB with session-based filtering.
        """
        try:
            # Search with session filter using existing vector DB capability
            results = await self.vector_db.semantic_search(
                query_vector=[0.0] * 1536,  # Dummy vector for metadata-only search
                limit=50,
                filters={"session_id": session_id}  # Session isolation filter
            )
            
            file_context = []
            for result in results:
                if hasattr(result, 'metadata') and result.metadata.get("document_type") == "session_file":
                    file_context.append({
                        "content": result.content,
                        "filename": result.metadata.get("filename", "unknown"),
                        "file_id": result.metadata.get("id"),
                        "upload_time": result.metadata.get("upload_time"),
                        "file_size": result.metadata.get("file_size")
                    })
            
            logger.debug(f"Retrieved {len(file_context)} files for session {session_id}")
            return file_context
            
        except Exception as e:
            logger.error(f"Failed to get file context for session {session_id}: {e}")
            return []
    
    async def get_session_files(self, session_id: str) -> List[Dict[str, Any]]:
        """Get list of files uploaded for a session."""
        try:
            # Get file context and format for API response
            file_context = await self.get_session_file_context(session_id)
            
            files = []
            for file_info in file_context:
                files.append({
                    "file_id": file_info.get("file_id"),
                    "file_name": file_info.get("filename"),
                    "upload_time": file_info.get("upload_time"),
                    "processed": True,  # Files are processed when stored
                    "file_size": file_info.get("file_size")
                })
            
            return files
            
        except Exception as e:
            logger.error(f"Failed to get session files for {session_id}: {e}")
            return []
    
    async def _extract_text_content(self, content: bytes, content_type: str, filename: str) -> str:
        """Extract text content from uploaded file."""
        try:
            # Handle text files
            if content_type and content_type.startswith('text/'):
                return content.decode('utf-8', errors='ignore')
            
            # Handle common document formats
            if filename.lower().endswith('.txt'):
                return content.decode('utf-8', errors='ignore')
            elif filename.lower().endswith('.md'):
                return content.decode('utf-8', errors='ignore')
            elif filename.lower().endswith('.json'):
                return content.decode('utf-8', errors='ignore')
            elif filename.lower().endswith('.csv'):
                return content.decode('utf-8', errors='ignore')
            else:
                # For binary files, create a description
                return f"Binary file: {filename} (Size: {len(content)} bytes, Type: {content_type})"
                
        except Exception as e:
            logger.warning(f"Failed to extract text from {filename}: {e}")
            return f"File: {filename} (Content extraction failed)"
    
    async def delete_session_file(self, session_id: str, file_id: str) -> bool:
        """Delete a specific file from a session."""
        try:
            # This would require implementing delete functionality in the vector DB
            # For now, we'll mark it as deleted in metadata
            logger.warning(f"File deletion not fully implemented: {file_id}")
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete file {file_id} from session {session_id}: {e}")
            return False
    
    async def cleanup_session_files(self, session_id: str) -> bool:
        """Clean up all files for a session."""
        try:
            # Remove files from disk
            session_dir = self.upload_dir / session_id
            if session_dir.exists():
                import shutil
                shutil.rmtree(session_dir)
                logger.info(f"Cleaned up session directory: {session_dir}")
            
            # Vector DB cleanup would require additional implementation
            return True
            
        except Exception as e:
            logger.error(f"Failed to cleanup session files for {session_id}: {e}")
            return False


# Global session service instance
_session_service = None


def get_session_service() -> SessionService:
    """Get the global session service instance."""
    global _session_service
    if _session_service is None:
        _session_service = SessionService()
    return _session_service
