"""
Optimized FastAPI application for the DSPy Multi-Agent System API.

High-performance REST API with pre-initialized components and async optimizations.
"""

import logging
import sys
from pathlib import Path
from fastapi import FastAPI, Request, WebSocket, WebSocketDisconnect, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

# Add project root for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import routes
from .routes import questions, workflows, files, health

# Import middleware
from .middleware.auth import APIKeyMiddleware, get_api_key
from .middleware.session import SessionMiddleware, RequestContextMiddleware
from .middleware.error_handler import ErrorHandlerMiddleware
from .middleware.performance_monitor import PerformanceMiddleware, get_performance_monitor

# Import services
from .services.websocket_service import WebSocketManager

# Import startup and optimization modules
from .startup import initialize_application, application_health_check, shutdown_application

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global WebSocket manager
websocket_manager = WebSocketManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Optimized application lifespan management with component pre-initialization."""
    # Startup
    logger.info("🚀 Starting Optimized DSPy Multi-Agent System API")

    # Initialize all components for optimal performance
    try:
        logger.info("🔧 Initializing components for optimal performance...")
        startup_metrics = await initialize_application()

        logger.info("✅ Optimized API startup complete!")
        logger.info(f"   📊 Startup time: {startup_metrics['total_startup_time']:.2f}s")
        logger.info(f"   🗄️  Database: {startup_metrics['database_init_time']:.2f}s")
        logger.info(f"   🔧 Components: {startup_metrics['components_init_time']:.2f}s")

        # Store startup metrics in app state
        app.state.startup_metrics = startup_metrics

    except Exception as e:
        logger.error(f"❌ Failed to initialize optimized services: {e}")
        # Continue anyway - services will handle their own fallbacks
        app.state.startup_metrics = {"error": str(e)}

    yield

    # Shutdown
    logger.info("🔄 Shutting down Optimized DSPy Multi-Agent System API")

    # Cleanup tasks
    try:
        # Close WebSocket connections
        for workflow_id in list(websocket_manager.active_connections.keys()):
            connections = websocket_manager.active_connections[workflow_id].copy()
            for websocket in connections:
                try:
                    await websocket.close()
                except:
                    pass

        # Cancel monitoring tasks
        for task in websocket_manager.monitoring_tasks.values():
            task.cancel()

        # Shutdown optimized components
        await shutdown_application()

        logger.info("✅ Optimized cleanup completed")

    except Exception as e:
        logger.error(f"❌ Error during optimized cleanup: {e}")


# Create FastAPI application
app = FastAPI(
    title="DSPy Multi-Agent Question Answering API",
    description="""
    REST API for the DSPy Multi-Agent System providing:
    
    - **Question Answering**: Simple and advanced question processing
    - **File Upload**: Session-based file management with vector search
    - **Workflow Management**: Real-time status monitoring and control
    - **WebSocket Updates**: Live progress updates for long-running workflows
    - **Health Monitoring**: System status and component health checks
    
    ## Authentication
    
    All endpoints (except health checks) require API key authentication.
    Include your API key in the Authorization header:
    
    ```
    Authorization: Bearer your-api-key-here
    ```
    
    ## Session Management
    
    The API uses session-based isolation for file uploads and workflow context.
    Each request should include a `session_id` to maintain context across operations.
    
    ## Rate Limits
    
    - 60 requests per minute per API key
    - Maximum 5 concurrent workflows per session
    - File uploads limited to 10MB per file
    """,
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add middleware (order matters!)
app.add_middleware(ErrorHandlerMiddleware)  # First - catch all errors
app.add_middleware(PerformanceMiddleware, monitor=get_performance_monitor())  # Performance tracking
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)
app.add_middleware(RequestContextMiddleware)  # Add request context
app.add_middleware(SessionMiddleware)  # Session tracking
app.add_middleware(APIKeyMiddleware)  # Authentication (last - after context)

# Include routers
app.include_router(
    questions.router, 
    prefix="/api/v1", 
    tags=["questions"],
    responses={
        401: {"description": "Unauthorized - Invalid API key"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

app.include_router(
    workflows.router, 
    prefix="/api/v1", 
    tags=["workflows"],
    responses={
        401: {"description": "Unauthorized - Invalid API key"},
        404: {"description": "Workflow not found"},
        500: {"description": "Internal Server Error"}
    }
)

app.include_router(
    files.router, 
    prefix="/api/v1", 
    tags=["files"],
    responses={
        401: {"description": "Unauthorized - Invalid API key"},
        413: {"description": "File too large"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)

app.include_router(
    health.router, 
    prefix="/api/v1", 
    tags=["health"]
)


# WebSocket endpoint for real-time updates
@app.websocket("/api/v1/ws/workflows/{workflow_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    workflow_id: str,
    api_key: str = Query(None, description="API key for authentication")
):
    """
    WebSocket endpoint for real-time workflow updates.
    
    Provides live updates for workflow execution including:
    - Status changes
    - Agent progress updates  
    - Final result streaming
    - Error notifications
    
    **Authentication:** Include API key as query parameter: ?api_key=your-key
    """
    try:
        # Validate API key (simplified for WebSocket)
        import os
        expected_key = os.getenv("API_KEY", "dev-api-key-12345")
        if api_key != expected_key:
            await websocket.close(code=4001, reason="Invalid API key")
            return
        
        # Connect to WebSocket manager
        await websocket_manager.connect(websocket, workflow_id, api_key)
        
        # Keep connection alive and handle messages
        try:
            while True:
                # Wait for client messages (ping/pong, etc.)
                data = await websocket.receive_text()
                
                # Handle client messages if needed
                if data == "ping":
                    await websocket.send_text("pong")
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for workflow {workflow_id}")
        
    except Exception as e:
        logger.error(f"WebSocket error for workflow {workflow_id}: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass
    
    finally:
        # Ensure cleanup
        await websocket_manager.disconnect(websocket)


# Root endpoint
@app.get("/")
async def root():
    """API root endpoint with basic information."""
    return {
        "name": "DSPy Multi-Agent Question Answering API",
        "version": "1.0.0",
        "description": "Optimized REST API for the DSPy Multi-Agent System",
        "docs_url": "/docs",
        "health_check": "/api/v1/health",
        "performance_metrics": "/api/v1/performance",
        "endpoints": {
            "questions": "/api/v1/questions/",
            "workflows": "/api/v1/workflows/",
            "files": "/api/v1/files/",
            "websocket": "/api/v1/ws/workflows/{workflow_id}"
        },
        "authentication": "Bearer token required (except health endpoints)",
        "optimizations": [
            "Async database operations",
            "Pre-initialized components",
            "Connection pooling",
            "Real-time performance monitoring"
        ],
        "github": "https://github.com/your-repo/test-dspy"
    }


# Performance monitoring endpoint
@app.get("/api/v1/performance")
async def get_performance_metrics():
    """Get comprehensive performance metrics and alerts."""
    monitor = get_performance_monitor()

    performance_summary = monitor.get_performance_summary()
    alerts = monitor.get_alerts()

    # Add application health status
    health_status = await application_health_check()

    return {
        "performance": performance_summary,
        "alerts": alerts,
        "health": health_status,
        "startup_metrics": getattr(app.state, 'startup_metrics', {}),
        "timestamp": performance_summary["timestamp"]
    }


# Custom exception handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Custom 404 handler."""
    return JSONResponse(
        status_code=404,
        content={
            "error": "not_found",
            "message": f"Endpoint {request.url.path} not found",
            "timestamp": "2025-07-02T00:00:00Z",
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


@app.exception_handler(405)
async def method_not_allowed_handler(request: Request, exc):
    """Custom 405 handler."""
    return JSONResponse(
        status_code=405,
        content={
            "error": "method_not_allowed", 
            "message": f"Method {request.method} not allowed for {request.url.path}",
            "timestamp": "2025-07-02T00:00:00Z",
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


# Development server info
if __name__ == "__main__":
    import uvicorn
    
    logger.info("Starting development server...")
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
