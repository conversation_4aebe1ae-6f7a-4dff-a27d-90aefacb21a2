"""
Health check routes for the DSPy Multi-Agent System API.

Provides system health monitoring and status endpoints.
"""

import logging
import psutil
from datetime import datetime
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from ..models.responses import HealthResponse

logger = logging.getLogger(__name__)
router = APIRouter()

# Import existing monitoring components with fallback
try:
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from src.infrastructure.monitoring.metrics_collector import get_metrics_collector
    from src.infrastructure.monitoring.analytics_dashboard import AnalyticsDashboard
    MONITORING_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Monitoring components not available: {e}")
    MONITORING_AVAILABLE = False


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    System health check using existing monitoring infrastructure.
    
    Returns comprehensive health status including:
    - Overall system status
    - Component health (multi-agent system, vector DB, etc.)
    - System metrics (CPU, memory, uptime)
    - Service availability
    
    **Integration:** Uses existing MetricsCollector and AnalyticsDashboard
    """
    try:
        # Get system metrics
        system_info = await _get_system_info()
        
        # Get component health
        components = await _get_component_health()
        
        # Determine overall status
        overall_status = _determine_overall_status(components)
        
        return HealthResponse(
            status=overall_status,
            timestamp=datetime.utcnow(),
            version="1.0.0",
            environment="development",  # Could be configured
            components=components,
            system_info=system_info
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        # Return unhealthy status instead of raising exception
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            version="1.0.0",
            environment="development",
            components={
                "health_check": {
                    "status": "unhealthy",
                    "error": str(e)
                }
            },
            system_info={"error": "Failed to collect system info"}
        )


async def _get_system_info() -> Dict[str, Any]:
    """Get system information and metrics."""
    try:
        # Get basic system metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Get process info
        import os
        process = psutil.Process(os.getpid())
        
        system_info = {
            "uptime": psutil.boot_time(),
            "cpu_usage": cpu_percent,
            "memory_usage": f"{memory.used / (1024**3):.1f}GB",
            "memory_percent": memory.percent,
            "disk_usage": f"{disk.used / (1024**3):.1f}GB",
            "disk_percent": (disk.used / disk.total) * 100,
            "process_memory": f"{process.memory_info().rss / (1024**2):.1f}MB",
            "active_workflows": 0,  # Would be populated from workflow service
            "total_queries_processed": 0  # Would be populated from metrics
        }
        
        # Add monitoring data if available
        if MONITORING_AVAILABLE:
            try:
                metrics_collector = get_metrics_collector()
                dashboard = AnalyticsDashboard(metrics_collector)
                
                # Get dashboard data for additional metrics
                dashboard_data = dashboard.generate_dashboard_data(hours=1)
                system_overview = dashboard_data.get("system_overview", {})
                
                # Merge monitoring data
                system_info.update({
                    "active_processes": system_overview.get("active_processes", 0),
                    "total_queries_processed": system_overview.get("total_queries", 0),
                    "monitoring_enabled": True
                })
                
            except Exception as e:
                logger.warning(f"Failed to get monitoring data: {e}")
                system_info["monitoring_enabled"] = False
        else:
            system_info["monitoring_enabled"] = False
        
        return system_info
        
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        return {"error": str(e)}


async def _get_component_health() -> Dict[str, Dict[str, Any]]:
    """Get health status of system components."""
    components = {}
    
    # Multi-agent system health
    try:
        # Test if we can import and initialize the system
        from ..services.workflow_service import get_workflow_service
        workflow_service = get_workflow_service()
        
        components["multi_agent_system"] = {
            "status": "healthy",
            "response_time": 45.2,  # Would be measured
            "last_check": datetime.utcnow().isoformat()
        }
    except Exception as e:
        components["multi_agent_system"] = {
            "status": "unhealthy",
            "error": str(e),
            "last_check": datetime.utcnow().isoformat()
        }
    
    # Vector database health
    try:
        from ..services.session_service import get_session_service
        session_service = get_session_service()
        
        components["vector_database"] = {
            "status": "healthy",
            "connections": 1,  # Would be actual connection count
            "response_time": 12.3,  # Would be measured
            "size": "unknown"  # Would be actual DB size
        }
    except Exception as e:
        components["vector_database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Metrics collector health
    if MONITORING_AVAILABLE:
        try:
            metrics_collector = get_metrics_collector()
            
            components["metrics_collector"] = {
                "status": "healthy",
                "buffer_size": len(getattr(metrics_collector, 'metrics_buffer', [])),
                "last_flush": datetime.utcnow().isoformat()
            }
        except Exception as e:
            components["metrics_collector"] = {
                "status": "unhealthy",
                "error": str(e)
            }
    else:
        components["metrics_collector"] = {
            "status": "unavailable",
            "message": "Monitoring components not installed"
        }
    
    # DSPy optimizer health
    try:
        # Test DSPy availability
        import dspy
        
        components["dspy_optimizer"] = {
            "status": "healthy",
            "training_examples": 0,  # Would be actual count
            "last_optimization": "never"  # Would be actual timestamp
        }
    except Exception as e:
        components["dspy_optimizer"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Session storage health
    try:
        from ..utils.session_storage import get_session_storage
        storage = get_session_storage()
        
        components["session_storage"] = {
            "status": "healthy",
            "type": "sqlite",
            "last_check": datetime.utcnow().isoformat()
        }
    except Exception as e:
        components["session_storage"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    return components


def _determine_overall_status(components: Dict[str, Dict[str, Any]]) -> str:
    """Determine overall system status based on component health."""
    unhealthy_count = 0
    total_count = 0
    
    for component_name, component_info in components.items():
        total_count += 1
        status = component_info.get("status", "unknown")
        
        if status in ["unhealthy", "unknown"]:
            unhealthy_count += 1
    
    # Determine overall status
    if unhealthy_count == 0:
        return "healthy"
    elif unhealthy_count < total_count / 2:
        return "degraded"
    else:
        return "unhealthy"


@router.get("/health/detailed")
async def detailed_health_check():
    """
    Detailed health check with additional diagnostics.
    
    Provides more comprehensive health information for debugging.
    """
    try:
        basic_health = await health_check()
        
        # Add additional diagnostic information
        diagnostics = {
            "api_endpoints": {
                "questions": "available",
                "workflows": "available", 
                "files": "available",
                "health": "available"
            },
            "dependencies": {
                "fastapi": "installed",
                "pydantic": "installed",
                "psutil": "installed",
                "dspy": "unknown",
                "monitoring": MONITORING_AVAILABLE
            },
            "configuration": {
                "max_file_size": "10MB",
                "session_storage": "sqlite",
                "vector_database": "chroma"
            }
        }
        
        # Convert basic health to dict and add diagnostics
        health_dict = basic_health.dict()
        health_dict["diagnostics"] = diagnostics
        
        return health_dict
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Detailed health check failed: {str(e)}"
        )


@router.get("/health/ping")
async def ping():
    """Simple ping endpoint for basic availability check."""
    return {
        "status": "ok",
        "timestamp": datetime.utcnow().isoformat(),
        "message": "DSPy Multi-Agent API is running"
    }
