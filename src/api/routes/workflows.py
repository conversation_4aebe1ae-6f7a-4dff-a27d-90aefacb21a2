"""
Workflow management routes for the DSPy Multi-Agent System API.

Handles workflow status monitoring and management endpoints.
"""

import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, Request
from typing import Optional

from ..models.requests import WorkflowCancelRequest
from ..models.responses import WorkflowStatusResponse, WorkflowCancelResponse
from ..models.errors import WorkflowNotFoundError, ValidationError
from ..middleware.auth import get_api_key
from ..services.workflow_service import get_workflow_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/workflows/{workflow_id}/status", response_model=WorkflowStatusResponse)
async def get_workflow_status(
    workflow_id: str,
    api_key: str = Depends(get_api_key),
    workflow_service = Depends(get_workflow_service)
):
    """
    Get workflow status with detailed progress information.
    
    Returns comprehensive status including:
    - Current execution phase
    - Progress percentage and metrics
    - Individual agent status
    - Real-time WebSocket connection info
    - Final answer (if completed)
    
    **Integration:** Uses existing workflow state management
    """
    try:
        logger.info(f"Status request for workflow: {workflow_id}")
        
        # Validate workflow ID format
        if not workflow_id or len(workflow_id.strip()) == 0:
            raise ValidationError("Workflow ID cannot be empty")
        
        # Get workflow status from service
        status_data = await workflow_service.get_workflow_status(workflow_id)
        
        logger.debug(f"Workflow {workflow_id} status: {status_data.get('status')}")
        
        return WorkflowStatusResponse(**status_data)
        
    except WorkflowNotFoundError:
        # Re-raise as HTTP 404
        raise HTTPException(
            status_code=404,
            detail=f"Workflow {workflow_id} not found"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=422,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get workflow status for {workflow_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve workflow status: {str(e)}"
        )


@router.delete("/workflows/{workflow_id}", response_model=WorkflowCancelResponse)
async def cancel_workflow(
    workflow_id: str,
    request: Optional[WorkflowCancelRequest] = None,
    api_key: str = Depends(get_api_key),
    workflow_service = Depends(get_workflow_service)
):
    """
    Cancel a running workflow.
    
    This endpoint:
    - Cancels pending or executing workflows
    - Cannot cancel already completed/failed workflows
    - Optionally accepts a cancellation reason
    - Returns cancellation confirmation
    
    **Note:** This addresses Issue #4 from the validation - missing cancellation endpoint
    """
    try:
        logger.info(f"Cancel request for workflow: {workflow_id}")
        
        # Validate workflow ID
        if not workflow_id or len(workflow_id.strip()) == 0:
            raise ValidationError("Workflow ID cannot be empty")
        
        # Get cancellation reason if provided
        reason = None
        if request:
            reason = request.reason
        
        # Attempt to cancel workflow
        cancelled = await workflow_service.cancel_workflow(workflow_id, reason)
        
        if not cancelled:
            # Workflow exists but cannot be cancelled (already completed/failed)
            workflow_status = await workflow_service.get_workflow_status(workflow_id)
            current_status = workflow_status.get("status", "unknown")
            
            raise HTTPException(
                status_code=409,
                detail=f"Cannot cancel workflow in status: {current_status}"
            )
        
        logger.info(f"Workflow cancelled: {workflow_id}")
        
        return WorkflowCancelResponse(
            success=True,
            workflow_id=workflow_id,
            status="cancelled",
            message=f"Workflow {workflow_id} cancelled successfully",
            cancellation_time=datetime.utcnow()
        )
        
    except WorkflowNotFoundError:
        raise HTTPException(
            status_code=404,
            detail=f"Workflow {workflow_id} not found"
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Failed to cancel workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cancel workflow: {str(e)}"
        )


@router.get("/workflows/{workflow_id}/logs")
async def get_workflow_logs(
    workflow_id: str,
    limit: int = 100,
    api_key: str = Depends(get_api_key),
    workflow_service = Depends(get_workflow_service)
):
    """
    Get workflow execution logs (if available).
    
    **Note:** This is a placeholder for future log retrieval functionality.
    """
    try:
        # Verify workflow exists
        await workflow_service.get_workflow_status(workflow_id)
        
        # For now, return a placeholder response
        return {
            "workflow_id": workflow_id,
            "logs": [
                {
                    "timestamp": datetime.utcnow().isoformat(),
                    "level": "INFO",
                    "message": "Workflow logs not yet implemented",
                    "component": "api"
                }
            ],
            "total_logs": 1,
            "limit": limit,
            "note": "Detailed logging will be implemented in future versions"
        }
        
    except WorkflowNotFoundError:
        raise HTTPException(
            status_code=404,
            detail=f"Workflow {workflow_id} not found"
        )
    except Exception as e:
        logger.error(f"Failed to get logs for workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve workflow logs: {str(e)}"
        )


@router.get("/workflows/active")
async def get_active_workflows(
    api_key: str = Depends(get_api_key),
    workflow_service = Depends(get_workflow_service)
):
    """
    Get list of currently active workflows.
    
    Returns workflows in 'pending' or 'executing' status.
    """
    try:
        # This would require additional implementation in workflow service
        # For now, return the active workflows from the service
        active_workflows = getattr(workflow_service, 'active_workflows', {})
        
        workflows = []
        for workflow_id, info in active_workflows.items():
            workflows.append({
                "workflow_id": workflow_id,
                "status": info.get("status", "unknown"),
                "start_time": info.get("start_time", datetime.utcnow()).isoformat()
            })
        
        return {
            "active_workflows": workflows,
            "total_count": len(workflows),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get active workflows: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve active workflows: {str(e)}"
        )


@router.get("/workflows/stats")
async def get_workflow_stats(
    api_key: str = Depends(get_api_key)
):
    """
    Get workflow execution statistics.
    
    **Note:** This is a placeholder for future analytics functionality.
    """
    try:
        # Placeholder statistics
        return {
            "total_workflows": 0,
            "completed_workflows": 0,
            "failed_workflows": 0,
            "cancelled_workflows": 0,
            "average_execution_time": 0.0,
            "success_rate": 0.0,
            "timestamp": datetime.utcnow().isoformat(),
            "note": "Detailed statistics will be implemented in future versions"
        }
        
    except Exception as e:
        logger.error(f"Failed to get workflow stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve workflow statistics: {str(e)}"
        )
