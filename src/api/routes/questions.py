"""
Question answering routes for the DSPy Multi-Agent System API.

Handles simple and advanced question processing endpoints.
"""

import logging
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from typing import Optional

from ..models.requests import SimpleQuestionRequest, AdvancedQuestionRequest
from ..models.responses import QuestionResponse
from ..models.errors import ValidationError, InternalServerError
from ..middleware.auth import get_api_key
from ..middleware.session import get_request_id
from ..services.workflow_service import get_workflow_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/questions/simple", response_model=QuestionResponse)
async def simple_question(
    request: SimpleQuestionRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(get_api_key),
    workflow_service = Depends(get_workflow_service)
):
    """
    Process simple question using standard workflow.
    
    This endpoint:
    - Uses the standard workflow (MainWorkflowFlow)
    - Processes questions up to 1000 characters
    - Supports optional language specification
    - Returns immediately with workflow ID for status tracking
    
    **Integration:** Calls existing `answer_question()` function
    """
    try:
        logger.info(f"Simple question request: session={request.session_id}, question_length={len(request.question)}")
        
        # Validate session ID format
        if not request.session_id or len(request.session_id.strip()) == 0:
            raise ValidationError("Session ID cannot be empty", field="session_id")
        
        # Start workflow in background using existing system
        workflow_id = await workflow_service.start_workflow(
            question=request.question,
            session_id=request.session_id,
            workflow_type="standard",
            config={
                "language": request.language,
                "workflow_source": "api_simple"
            }
        )
        
        logger.info(f"Simple workflow started: {workflow_id}")
        
        return QuestionResponse(
            success=True,
            workflow_id=workflow_id,
            timestamp=datetime.now(timezone.utc)
        )
        
    except ValidationError:
        # Re-raise validation errors as-is
        raise
    except Exception as e:
        logger.error(f"Simple question processing failed: {e}")
        raise InternalServerError(f"Failed to process question: {str(e)}")


@router.post("/questions/advanced", response_model=QuestionResponse)  
async def advanced_question(
    request: AdvancedQuestionRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(get_api_key),
    workflow_service = Depends(get_workflow_service)
):
    """
    Process complex question with advanced configuration.
    
    This endpoint:
    - Supports both standard and enhanced workflows
    - Handles questions up to 2000 characters
    - Accepts advanced configuration options
    - Provides fine-grained control over execution
    
    **Integration:** Calls existing `run_workflow()` function
    """
    try:
        logger.info(
            f"Advanced question request: session={request.session_id}, "
            f"type={request.workflow_type}, question_length={len(request.question)}"
        )
        
        # Validate session ID
        if not request.session_id or len(request.session_id.strip()) == 0:
            raise ValidationError("Session ID cannot be empty", field="session_id")
        
        # Prepare advanced configuration
        execution_config = dict(request.config or {})
        execution_config.update({
            "workflow_source": "api_advanced",
            "api_request": True
        })
        
        # Determine workflow type
        workflow_type = "enhanced" if request.workflow_type == "enhanced" else "standard"
        
        logger.debug(f"Using workflow type: {workflow_type}")
        
        # Start workflow with advanced configuration
        workflow_id = await workflow_service.start_workflow(
            question=request.question,
            session_id=request.session_id,
            workflow_type=workflow_type,
            config=execution_config
        )
        
        logger.info(f"Advanced workflow started: {workflow_id} (type: {workflow_type})")
        
        return QuestionResponse(
            success=True,
            workflow_id=workflow_id,
            timestamp=datetime.now(timezone.utc)
        )
        
    except ValidationError:
        # Re-raise validation errors as-is
        raise
    except Exception as e:
        logger.error(f"Advanced question processing failed: {e}")
        raise InternalServerError(f"Failed to process advanced question: {str(e)}")


@router.get("/questions/health")
async def questions_health():
    """Health check for questions endpoints."""
    try:
        # Light health check - don't initialize heavy services
        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "endpoints": {
                "simple": "available",
                "advanced": "available"
            },
            "workflow_service": "ready"
        }
        
    except Exception as e:
        logger.error(f"Questions health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Questions service unhealthy: {str(e)}"
        )


# Additional utility endpoints

@router.get("/questions/limits")
async def get_question_limits():
    """Get question processing limits and constraints."""
    return {
        "simple_questions": {
            "min_length": 10,
            "max_length": 1000,
            "supported_languages": ["en", "es", "fr", "de", "it", "pt"],
            "workflow_type": "standard"
        },
        "advanced_questions": {
            "min_length": 10,
            "max_length": 2000,
            "workflow_types": ["standard", "enhanced"],
            "config_options": {
                "enable_optimization": "boolean",
                "max_iterations": "integer (1-10)",
                "vector_knowledge_enabled": "boolean",
                "parallel_execution": "boolean",
                "quality_threshold": "float (0.0-1.0)",
                "specialist_config": "object"
            }
        },
        "rate_limits": {
            "requests_per_minute": 60,
            "concurrent_workflows": 5
        }
    }


@router.get("/questions/examples")
async def get_question_examples():
    """Get example questions for testing."""
    return {
        "simple_examples": [
            "What are the latest developments in renewable energy?",
            "How does artificial intelligence impact healthcare?",
            "What are the benefits of electric vehicles?",
            "Explain quantum computing in simple terms.",
            "What is the future of remote work?"
        ],
        "advanced_examples": [
            {
                "question": "Analyze the economic impact of artificial intelligence on employment in the next decade",
                "workflow_type": "enhanced",
                "config": {
                    "enable_optimization": True,
                    "max_iterations": 5,
                    "vector_knowledge_enabled": True,
                    "parallel_execution": True,
                    "quality_threshold": 0.8
                }
            },
            {
                "question": "Compare the environmental benefits and challenges of solar vs wind energy",
                "workflow_type": "standard",
                "config": {
                    "specialist_config": {
                        "include_predictions": True,
                        "focus_areas": ["environmental", "economic", "technical"]
                    }
                }
            }
        ]
    }
