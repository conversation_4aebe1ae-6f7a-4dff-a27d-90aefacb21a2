"""
Flow interfaces for CrewAI 2025 Flows integration.

Defines contracts for workflow orchestration, state management,
and event-driven execution patterns.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable, Type, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum
from pydantic import BaseModel
import asyncio

class FlowStatus(Enum):
    """Flow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"
    PAUSED = "paused"

class FlowEventType(Enum):
    """Types of flow events."""
    STARTED = "started"
    STEP_COMPLETED = "step_completed"
    STEP_FAILED = "step_failed"
    PARALLEL_STARTED = "parallel_started"
    PARALLEL_COMPLETED = "parallel_completed"
    SUBFLOW_STARTED = "subflow_started"
    SUBFLOW_COMPLETED = "subflow_completed"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class FlowEvent:
    """Event emitted during flow execution."""
    event_type: FlowEventType
    flow_id: str
    step_name: Optional[str] = None
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=lambda: asyncio.get_event_loop().time())
    error: Optional[str] = None

@dataclass
class FlowConfig:
    """Configuration for flow execution."""
    timeout_seconds: Optional[int] = 300
    max_retries: int = 3
    parallel_execution: bool = True
    error_handling: str = "fail_fast"  # "fail_fast", "continue", "retry"
    enable_checkpoints: bool = True
    checkpoint_interval: int = 10  # seconds

class WorkflowState(BaseModel):
    """Base workflow state model."""
    query: str = ""
    plan: List[str] = field(default_factory=list)
    research_results: Dict[str, Any] = field(default_factory=dict)
    library_results: Dict[str, Any] = field(default_factory=dict)
    analysis_results: Dict[str, Any] = field(default_factory=dict)
    final_answer: str = ""
    current_step: str = ""
    step_count: int = 0
    errors: List[str] = field(default_factory=list)
    
    class Config:
        arbitrary_types_allowed = True

class IFlowStep(ABC):
    """Interface for individual flow steps."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Step name."""
        pass
    
    @property
    @abstractmethod
    def dependencies(self) -> List[str]:
        """List of step names this step depends on."""
        pass
    
    @abstractmethod
    async def execute(self, state: WorkflowState) -> Any:
        """Execute the step with current state."""
        pass
    
    @abstractmethod
    def can_execute(self, state: WorkflowState) -> bool:
        """Check if step can execute given current state."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources after step execution."""
        pass

class IFlowEventHandler(ABC):
    """Interface for handling flow events."""
    
    @abstractmethod
    async def on_flow_started(self, event: FlowEvent) -> None:
        """Handle flow started event."""
        pass
    
    @abstractmethod
    async def on_step_completed(self, event: FlowEvent) -> None:
        """Handle step completed event."""
        pass
    
    @abstractmethod
    async def on_step_failed(self, event: FlowEvent) -> None:
        """Handle step failed event."""
        pass
    
    @abstractmethod
    async def on_flow_completed(self, event: FlowEvent) -> None:
        """Handle flow completed event."""
        pass
    
    @abstractmethod
    async def on_flow_failed(self, event: FlowEvent) -> None:
        """Handle flow failed event."""
        pass

class IFlow(ABC):
    """Base interface for workflow flows."""
    
    @property
    @abstractmethod
    def flow_id(self) -> str:
        """Unique flow identifier."""
        pass
    
    @property
    @abstractmethod
    def status(self) -> FlowStatus:
        """Current flow status."""
        pass
    
    @property
    @abstractmethod
    def state(self) -> WorkflowState:
        """Current workflow state."""
        pass
    
    @abstractmethod
    async def kickoff_async(self, inputs: Optional[Dict[str, Any]] = None) -> Any:
        """Start flow execution asynchronously."""
        pass
    
    @abstractmethod
    async def pause(self) -> None:
        """Pause flow execution."""
        pass
    
    @abstractmethod
    async def resume(self) -> None:
        """Resume paused flow execution."""
        pass
    
    @abstractmethod
    async def cancel(self) -> None:
        """Cancel flow execution."""
        pass
    
    @abstractmethod
    def add_event_handler(self, handler: IFlowEventHandler) -> None:
        """Add event handler to flow."""
        pass
    
    @abstractmethod
    def remove_event_handler(self, handler: IFlowEventHandler) -> None:
        """Remove event handler from flow."""
        pass

class ISubflow(IFlow):
    """Interface for subflows that can be called from parent flows."""
    
    @property
    @abstractmethod
    def parent_flow_id(self) -> Optional[str]:
        """Parent flow ID if this is a subflow."""
        pass
    
    @abstractmethod
    async def set_parent_context(self, parent_state: WorkflowState) -> None:
        """Set context from parent flow."""
        pass
    
    @abstractmethod
    async def get_result_for_parent(self) -> Dict[str, Any]:
        """Get result data to pass back to parent flow."""
        pass

class ITaskManagerFlow(IFlow):
    """Interface for task management flows."""
    
    @abstractmethod
    async def create_execution_plan(self, query: str) -> List[str]:
        """Create execution plan for the given query."""
        pass
    
    @abstractmethod
    async def delegate_to_specialists(self, plan: List[str]) -> Dict[str, Any]:
        """Delegate tasks to specialist flows."""
        pass
    
    @abstractmethod
    async def monitor_specialists(self) -> Dict[str, FlowStatus]:
        """Monitor status of running specialist flows."""
        pass

class ISpecialistFlow(ISubflow):
    """Interface for specialist flows (Researcher, Librarian, etc.)."""
    
    @property
    @abstractmethod
    def specialist_type(self) -> str:
        """Type of specialist (researcher, librarian, etc.)."""
        pass
    
    @abstractmethod
    async def process_task(self, task_description: str, context: Dict[str, Any]) -> Any:
        """Process assigned task with given context."""
        pass
    
    @abstractmethod
    async def iterate_until_complete(self, max_iterations: int = 3) -> Any:
        """Iterate on task until completion or max iterations."""
        pass

class IWriterFlow(IFlow):
    """Interface for synthesis/writing flows."""
    
    @abstractmethod
    async def synthesize_results(self, 
                               query: str,
                               research_results: Dict[str, Any],
                               library_results: Dict[str, Any],
                               analysis_results: Dict[str, Any]) -> str:
        """Synthesize final answer from all results."""
        pass
    
    @abstractmethod
    async def format_response(self, content: str, format_type: str = "markdown") -> str:
        """Format the response in specified format."""
        pass

class IFlowOrchestrator(ABC):
    """Interface for flow orchestration and management."""
    
    @abstractmethod
    async def register_flow(self, flow: IFlow) -> None:
        """Register a flow with the orchestrator."""
        pass
    
    @abstractmethod
    async def execute_flow(self, flow_id: str, inputs: Dict[str, Any]) -> Any:
        """Execute a registered flow."""
        pass
    
    @abstractmethod
    async def execute_parallel_flows(self, flow_specs: List[Dict[str, Any]]) -> List[Any]:
        """Execute multiple flows in parallel."""
        pass
    
    @abstractmethod
    async def monitor_flows(self) -> Dict[str, FlowStatus]:
        """Monitor status of all active flows."""
        pass
    
    @abstractmethod
    async def cancel_flow(self, flow_id: str) -> None:
        """Cancel a running flow."""
        pass
    
    @abstractmethod
    async def get_flow_result(self, flow_id: str) -> Optional[Any]:
        """Get result from completed flow."""
        pass

class IFlowFactory(ABC):
    """Interface for creating flow instances."""
    
    @abstractmethod
    def create_task_manager_flow(self, config: FlowConfig) -> ITaskManagerFlow:
        """Create task manager flow."""
        pass
    
    @abstractmethod
    def create_researcher_flow(self, config: FlowConfig) -> ISpecialistFlow:
        """Create researcher specialist flow."""
        pass
    
    @abstractmethod
    def create_librarian_flow(self, config: FlowConfig) -> ISpecialistFlow:
        """Create librarian specialist flow."""
        pass
    
    @abstractmethod
    def create_writer_flow(self, config: FlowConfig) -> IWriterFlow:
        """Create writer flow."""
        pass
    
    @abstractmethod
    def create_custom_flow(self, flow_type: str, config: FlowConfig) -> IFlow:
        """Create custom flow of specified type."""
        pass

class IFlowMetrics(ABC):
    """Interface for flow execution metrics."""
    
    @abstractmethod
    def record_flow_start(self, flow_id: str) -> None:
        """Record flow start time."""
        pass
    
    @abstractmethod
    def record_flow_completion(self, flow_id: str, result: Any) -> None:
        """Record flow completion."""
        pass
    
    @abstractmethod
    def record_step_metrics(self, flow_id: str, step_name: str, execution_time: float) -> None:
        """Record step execution metrics."""
        pass
    
    @abstractmethod
    def get_flow_metrics(self, flow_id: str) -> Dict[str, Any]:
        """Get metrics for specific flow."""
        pass
    
    @abstractmethod
    def get_aggregate_metrics(self) -> Dict[str, Any]:
        """Get aggregate metrics across all flows."""
        pass 