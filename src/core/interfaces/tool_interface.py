"""
Tool interfaces for the multi-agent system.

Defines contracts for tool registration, execution, and management
supporting both CrewAI and DSPy tool patterns.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable, Type
from dataclasses import dataclass, field
from enum import Enum
import asyncio

class ToolType(Enum):
    """Types of tools in the system."""
    SEARCH = "search"              # Web search tools
    SCRAPING = "scraping"          # Website scraping tools  
    RETRIEVAL = "retrieval"        # Document retrieval tools
    ANALYSIS = "analysis"          # Data analysis tools
    PROCESSING = "processing"      # Data processing tools
    COMMUNICATION = "communication" # API communication tools
    UTILITY = "utility"           # General utility tools

class ToolStatus(Enum):
    """Tool execution status."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

@dataclass
class ToolConfig:
    """Configuration for tool behavior."""
    timeout_seconds: int = 60
    max_retries: int = 3
    parallel_execution: bool = True
    cache_results: bool = True
    rate_limit_per_minute: Optional[int] = None

@dataclass
class ToolSpec:
    """Specification for a tool."""
    name: str
    tool_type: ToolType
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    required_params: List[str] = field(default_factory=list)
    optional_params: List[str] = field(default_factory=list)
    config: ToolConfig = field(default_factory=ToolConfig)

@dataclass
class ToolResult:
    """Result from tool execution."""
    tool_name: str
    status: ToolStatus
    output: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_success(self) -> bool:
        """Check if tool execution was successful."""
        return self.status == ToolStatus.COMPLETED and self.error is None

class ITool(ABC):
    """Base interface for all tools."""
    
    @property
    @abstractmethod
    def spec(self) -> ToolSpec:
        """Tool specification."""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the tool with given parameters."""
        pass
    
    @abstractmethod
    def validate_inputs(self, **kwargs) -> bool:
        """Validate input parameters."""
        pass
    
    @abstractmethod
    async def can_execute(self) -> bool:
        """Check if tool can execute (API keys available, etc.)."""
        pass

class ISearchTool(ITool):
    """Interface for search tools."""
    
    @abstractmethod
    async def search(self, query: str, max_results: int = 10) -> ToolResult:
        """Perform search with given query."""
        pass
    
    @abstractmethod
    async def search_with_filters(self, 
                                query: str, 
                                filters: Dict[str, Any],
                                max_results: int = 10) -> ToolResult:
        """Perform filtered search."""
        pass

class IScrapingTool(ITool):
    """Interface for web scraping tools."""
    
    @abstractmethod
    async def scrape_url(self, url: str) -> ToolResult:
        """Scrape content from a URL."""
        pass
    
    @abstractmethod
    async def scrape_multiple_urls(self, urls: List[str]) -> ToolResult:
        """Scrape content from multiple URLs."""
        pass

class IRetrievalTool(ITool):
    """Interface for document retrieval tools."""
    
    @abstractmethod
    async def retrieve_documents(self, 
                               query: str, 
                               max_docs: int = 10) -> ToolResult:
        """Retrieve relevant documents."""
        pass
    
    @abstractmethod
    async def retrieve_with_metadata(self,
                                   query: str,
                                   metadata_filters: Dict[str, Any],
                                   max_docs: int = 10) -> ToolResult:
        """Retrieve documents with metadata filtering."""
        pass

class IAnalysisTool(ITool):
    """Interface for analysis tools."""
    
    @abstractmethod
    async def analyze_data(self, data: Any, analysis_type: str) -> ToolResult:
        """Analyze given data."""
        pass
    
    @abstractmethod
    async def extract_insights(self, data: Any) -> ToolResult:
        """Extract insights from data."""
        pass

class IToolRegistry(ABC):
    """Interface for tool registry management."""
    
    @abstractmethod
    def register_tool(self, tool: ITool) -> None:
        """Register a new tool."""
        pass
    
    @abstractmethod
    def unregister_tool(self, tool_name: str) -> None:
        """Unregister a tool."""
        pass
    
    @abstractmethod
    def get_tool(self, tool_name: str) -> Optional[ITool]:
        """Get tool by name."""
        pass
    
    @abstractmethod
    def get_tools_by_type(self, tool_type: ToolType) -> List[ITool]:
        """Get all tools of a specific type."""
        pass
    
    @abstractmethod
    def list_available_tools(self) -> List[str]:
        """List all available tool names."""
        pass
    
    @abstractmethod
    async def validate_tools(self) -> Dict[str, bool]:
        """Validate all registered tools."""
        pass

class IToolExecutor(ABC):
    """Interface for tool execution management."""
    
    @abstractmethod
    async def execute_tool(self, tool_name: str, **kwargs) -> ToolResult:
        """Execute a single tool."""
        pass
    
    @abstractmethod
    async def execute_parallel_tools(self, 
                                   tool_requests: List[Dict[str, Any]]) -> List[ToolResult]:
        """Execute multiple tools in parallel."""
        pass
    
    @abstractmethod
    async def execute_sequential_tools(self,
                                     tool_requests: List[Dict[str, Any]]) -> List[ToolResult]:
        """Execute multiple tools sequentially."""
        pass
    
    @abstractmethod
    def get_execution_status(self, execution_id: str) -> ToolStatus:
        """Get status of tool execution."""
        pass

class IToolFactory(ABC):
    """Interface for creating tools."""
    
    @abstractmethod
    def create_search_tool(self, provider: str, config: Dict[str, Any]) -> ISearchTool:
        """Create a search tool."""
        pass
    
    @abstractmethod
    def create_scraping_tool(self, config: Dict[str, Any]) -> IScrapingTool:
        """Create a scraping tool."""
        pass
    
    @abstractmethod
    def create_retrieval_tool(self, config: Dict[str, Any]) -> IRetrievalTool:
        """Create a retrieval tool."""
        pass
    
    @abstractmethod
    def create_analysis_tool(self, config: Dict[str, Any]) -> IAnalysisTool:
        """Create an analysis tool."""
        pass

class IToolMetrics(ABC):
    """Interface for tool execution metrics."""
    
    @abstractmethod
    def record_tool_execution(self, tool_name: str, result: ToolResult) -> None:
        """Record tool execution metrics."""
        pass
    
    @abstractmethod
    def get_tool_metrics(self, tool_name: str) -> Dict[str, Any]:
        """Get metrics for a specific tool."""
        pass
    
    @abstractmethod
    def get_aggregate_metrics(self, tool_type: Optional[ToolType] = None) -> Dict[str, Any]:
        """Get aggregate metrics across tools."""
        pass
    
    @abstractmethod
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        pass 