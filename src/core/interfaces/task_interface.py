"""
Task interfaces for the multi-agent system.

Defines contracts for task specification, execution, and result handling
in the hierarchical agent workflow.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import uuid

class TaskType(Enum):
    """Types of tasks in the system."""
    PLANNING = "planning"          # TaskManager planning tasks
    RESEARCH = "research"          # Web research tasks  
    RETRIEVAL = "retrieval"        # Document retrieval tasks
    ANALYSIS = "analysis"          # Data analysis tasks
    SYNTHESIS = "synthesis"        # Final answer synthesis
    TOOL_EXECUTION = "tool_execution"  # Individual tool execution

class TaskPriority(Enum):
    """Task priority levels."""
    LOW = 1
    NORMAL = 2  
    HIGH = 3
    CRITICAL = 4

class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"
    RETRYING = "retrying"

@dataclass
class TaskMetadata:
    """Metadata associated with a task."""
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_duration: Optional[int] = None  # seconds
    actual_duration: Optional[float] = None   # seconds
    retry_count: int = 0
    max_retries: int = 3
    tags: List[str] = field(default_factory=list)

@dataclass 
class TaskSpec:
    """Specification for a task to be executed."""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    task_type: TaskType = TaskType.PLANNING
    description: str = ""
    inputs: Dict[str, Any] = field(default_factory=dict)
    expected_output_type: str = "string"
    priority: TaskPriority = TaskPriority.NORMAL
    timeout_seconds: Optional[int] = 300
    dependencies: List[str] = field(default_factory=list)
    metadata: TaskMetadata = field(default_factory=TaskMetadata)
    context: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TaskResult:
    """Result from task execution."""
    task_id: str
    task_type: TaskType
    status: TaskStatus
    output: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: float = 0.0
    agent_id: Optional[str] = None
    
    @property
    def is_success(self) -> bool:
        """Check if task completed successfully."""
        return self.status == TaskStatus.COMPLETED and self.error is None
    
    @property 
    def is_failure(self) -> bool:
        """Check if task failed."""
        return self.status in [TaskStatus.FAILED, TaskStatus.TIMEOUT, TaskStatus.CANCELLED]

class ITask(ABC):
    """Base interface for tasks."""
    
    @property
    @abstractmethod
    def spec(self) -> TaskSpec:
        """Task specification."""
        pass
    
    @property
    @abstractmethod
    def status(self) -> TaskStatus:
        """Current task status."""
        pass
    
    @abstractmethod
    async def execute(self) -> TaskResult:
        """Execute the task and return result."""
        pass
    
    @abstractmethod
    def can_execute(self) -> bool:
        """Check if task can be executed (dependencies met, etc.)."""
        pass
    
    @abstractmethod
    def cancel(self) -> bool:
        """Cancel task execution."""
        pass

class ITaskExecutor(ABC):
    """Interface for task execution engines."""
    
    @abstractmethod
    async def execute_task(self, task: ITask) -> TaskResult:
        """Execute a single task."""
        pass
    
    @abstractmethod
    async def execute_parallel_tasks(self, tasks: List[ITask]) -> List[TaskResult]:
        """Execute multiple tasks in parallel."""
        pass
    
    @abstractmethod
    async def execute_sequential_tasks(self, tasks: List[ITask]) -> List[TaskResult]:
        """Execute multiple tasks sequentially."""
        pass
    
    @abstractmethod
    def get_task_status(self, task_id: str) -> TaskStatus:
        """Get status of a specific task."""
        pass

class ITaskQueue(ABC):
    """Interface for task queue management."""
    
    @abstractmethod
    async def enqueue(self, task: ITask) -> None:
        """Add task to queue."""
        pass
    
    @abstractmethod
    async def dequeue(self, priority: Optional[TaskPriority] = None) -> Optional[ITask]:
        """Remove and return next task from queue."""
        pass
    
    @abstractmethod
    async def peek(self) -> Optional[ITask]:
        """Look at next task without removing it."""
        pass
    
    @abstractmethod
    def size(self) -> int:
        """Get current queue size."""
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Clear all tasks from queue."""
        pass

class ITaskValidator(ABC):
    """Interface for task validation."""
    
    @abstractmethod
    def validate_task_spec(self, spec: TaskSpec) -> bool:
        """Validate task specification."""
        pass
    
    @abstractmethod
    def validate_task_result(self, result: TaskResult) -> bool:
        """Validate task result."""
        pass
    
    @abstractmethod
    def validate_dependencies(self, task: ITask, completed_tasks: List[TaskResult]) -> bool:
        """Validate that task dependencies are satisfied."""
        pass

class ITaskMetrics(ABC):
    """Interface for task execution metrics."""
    
    @abstractmethod
    def record_task_start(self, task_id: str) -> None:
        """Record task start time."""
        pass
    
    @abstractmethod
    def record_task_completion(self, task_id: str, result: TaskResult) -> None:
        """Record task completion."""
        pass
    
    @abstractmethod
    def get_task_metrics(self, task_id: str) -> Dict[str, Any]:
        """Get metrics for a specific task."""
        pass
    
    @abstractmethod
    def get_aggregate_metrics(self, task_type: Optional[TaskType] = None) -> Dict[str, Any]:
        """Get aggregate metrics across tasks."""
        pass 