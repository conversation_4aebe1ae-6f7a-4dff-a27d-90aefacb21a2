"""
Agent interfaces for the multi-agent system.

Defines contracts for different types of agents in the hierarchical system,
supporting CrewAI 2025 Flows integration and DSPy optimization.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
from dataclasses import dataclass
from enum import Enum

class AgentType(Enum):
    """Types of agents in the system."""
    ORCHESTRATOR = "orchestrator"  # TaskManager
    SPECIALIST = "specialist"      # <PERSON><PERSON>, <PERSON><PERSON><PERSON>, etc.
    SYNTHESIZER = "synthesizer"    # Writer

class AgentStatus(Enum):
    """Agent execution status."""
    IDLE = "idle"
    PLANNING = "planning"
    EXECUTING = "executing"
    DELEGATING = "delegating"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

@dataclass
class AgentConfig:
    """Configuration for agent behavior."""
    max_iterations: int = 5
    timeout_seconds: int = 300
    parallel_tools: bool = True
    retry_attempts: int = 3
    delegation_enabled: bool = True
    
@dataclass 
class TaskSpec:
    """Specification for a task to be executed."""
    task_id: str
    description: str
    inputs: Dict[str, Any]
    expected_output_type: str
    priority: int = 1
    timeout: Optional[int] = None
    dependencies: List[str] = None

@dataclass
class AgentResult:
    """Result from agent execution."""
    agent_id: str
    task_id: str
    status: AgentStatus
    output: Any
    metadata: Dict[str, Any]
    execution_time: float
    error: Optional[str] = None

class IAgent(ABC):
    """Base interface for all agents."""
    
    @property
    @abstractmethod
    def agent_id(self) -> str:
        """Unique identifier for the agent."""
        pass
    
    @property 
    @abstractmethod
    def agent_type(self) -> AgentType:
        """Type of the agent."""
        pass
    
    @property
    @abstractmethod
    def capabilities(self) -> List[str]:
        """List of capabilities this agent provides."""
        pass
    
    @abstractmethod
    async def execute(self, task: TaskSpec) -> AgentResult:
        """Execute a task and return result."""
        pass
    
    @abstractmethod
    def can_handle(self, task: TaskSpec) -> bool:
        """Check if agent can handle the given task."""
        pass

class IOrchestratorAgent(IAgent):
    """Interface for orchestrator agents (TaskManager)."""
    
    @abstractmethod
    async def plan_execution(self, query: str) -> List[TaskSpec]:
        """Create execution plan for the given query."""
        pass
    
    @abstractmethod
    async def delegate_task(self, task: TaskSpec, target_agent: str) -> AgentResult:
        """Delegate a task to a specialist agent."""
        pass
    
    @abstractmethod
    async def coordinate_parallel_execution(self, tasks: List[TaskSpec]) -> List[AgentResult]:
        """Coordinate parallel execution of multiple tasks."""
        pass
    
    @abstractmethod
    async def handle_agent_failure(self, failed_task: TaskSpec, error: str) -> TaskSpec:
        """Handle agent failure and potentially retry with different approach."""
        pass

class ISpecialistAgent(IAgent):
    """Interface for specialist agents (Researcher, Librarian, etc.)."""
    
    @property
    @abstractmethod
    def specialization(self) -> str:
        """The domain of specialization."""
        pass
    
    @abstractmethod
    async def process_with_tools(self, task: TaskSpec) -> AgentResult:
        """Process task using available tools."""
        pass
    
    @abstractmethod
    async def iterative_refinement(self, task: TaskSpec, previous_result: AgentResult) -> AgentResult:
        """Refine previous result through iterative processing."""
        pass
    
    @abstractmethod
    def validate_output(self, output: Any) -> bool:
        """Validate that output meets quality criteria."""
        pass

class ISynthesizerAgent(IAgent):
    """Interface for synthesizer agents (Writer)."""
    
    @abstractmethod
    async def synthesize_results(self, 
                               query: str, 
                               specialist_results: List[AgentResult]) -> AgentResult:
        """Synthesize final answer from specialist results."""
        pass
    
    @abstractmethod
    def determine_output_style(self, query: str, context: Dict[str, Any]) -> str:
        """Determine appropriate output style and length."""
        pass
    
    @abstractmethod
    async def format_response(self, content: str, style: str) -> str:
        """Format the final response according to style guidelines."""
        pass 