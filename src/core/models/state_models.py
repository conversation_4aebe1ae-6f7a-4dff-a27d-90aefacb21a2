"""
Pydantic state models for the multi-agent workflow system.

Defines structured state management for CrewAI Flows integration
with type safety and validation.
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, field_validator, ConfigDict
from datetime import datetime
from enum import Enum

class TaskPhase(Enum):
    """Current phase of task execution."""
    INITIALIZATION = "initialization"
    PLANNING = "planning"
    RESEARCH = "research"
    RETRIEVAL = "retrieval"
    ANALYSIS = "analysis"
    SYNTHESIS = "synthesis"
    FINALIZATION = "finalization"

class AgentStatus(Enum):
    """Status of individual agents."""
    IDLE = "idle"
    WORKING = "working"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

class PlanStep(BaseModel):
    """Individual step in execution plan."""
    step_id: str
    description: str
    assigned_agent: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)
    estimated_time: Optional[int] = None  # seconds
    status: AgentStatus = AgentStatus.IDLE
    result: Optional[str] = None
    error: Optional[str] = None

class ResearchResult(BaseModel):
    """Research results from web search and scraping."""
    query: str
    sources: List[Dict[str, str]] = Field(default_factory=list)
    summary: str = ""
    key_findings: List[str] = Field(default_factory=list)
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    timestamp: datetime = Field(default_factory=datetime.now)
    agent_id: str = ""

class LibraryResult(BaseModel):
    """Document retrieval results."""
    query: str
    documents: List[Dict[str, Any]] = Field(default_factory=list)
    relevant_excerpts: List[str] = Field(default_factory=list)
    document_count: int = 0
    relevance_scores: List[float] = Field(default_factory=list)
    timestamp: datetime = Field(default_factory=datetime.now)
    agent_id: str = ""

class AnalysisResult(BaseModel):
    """Data analysis results."""
    analysis_type: str
    input_data: Dict[str, Any] = Field(default_factory=dict)
    findings: List[str] = Field(default_factory=list)
    insights: List[str] = Field(default_factory=list)
    metrics: Dict[str, float] = Field(default_factory=dict)
    confidence_level: float = Field(default=0.0, ge=0.0, le=1.0)
    timestamp: datetime = Field(default_factory=datetime.now)
    agent_id: str = ""

class AgentProgress(BaseModel):
    """Progress tracking for individual agents."""
    agent_id: str
    agent_type: str
    current_task: str = ""
    status: AgentStatus = AgentStatus.IDLE
    progress_percentage: float = Field(default=0.0, ge=0.0, le=100.0)
    iterations_completed: int = 0
    max_iterations: int = 3
    start_time: Optional[datetime] = None
    last_update: datetime = Field(default_factory=datetime.now)
    current_output: str = ""
    errors: List[str] = Field(default_factory=list)

class WorkflowMetrics(BaseModel):
    """Performance metrics for the workflow."""
    total_execution_time: float = 0.0
    planning_time: float = 0.0
    research_time: float = 0.0
    retrieval_time: float = 0.0
    analysis_time: float = 0.0
    synthesis_time: float = 0.0
    parallel_efficiency: float = 0.0
    success_rate: float = 0.0
    error_count: int = 0
    retry_count: int = 0

class WorkflowState(BaseModel):
    """Complete workflow state for CrewAI Flows."""
    
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        validate_assignment=True
    )
    
    # Core Query Information
    original_query: str = ""
    processed_query: str = ""
    query_complexity: str = "medium"  # low, medium, high
    expected_response_type: str = "comprehensive"
    
    # Current Execution Status
    current_phase: TaskPhase = TaskPhase.INITIALIZATION
    workflow_id: str = ""
    start_time: datetime = Field(default_factory=datetime.now)
    last_update: datetime = Field(default_factory=datetime.now)
    
    # Execution Plan
    execution_plan: List[PlanStep] = Field(default_factory=list)
    current_step_index: int = 0
    completed_steps: List[str] = Field(default_factory=list)
    failed_steps: List[str] = Field(default_factory=list)
    
    # Agent Progress Tracking
    agent_progress: Dict[str, AgentProgress] = Field(default_factory=dict)
    
    # Results from Each Agent Type
    research_results: List[ResearchResult] = Field(default_factory=list)
    library_results: List[LibraryResult] = Field(default_factory=list)
    analysis_results: List[AnalysisResult] = Field(default_factory=list)
    
    # Synthesis and Final Output
    synthesis_notes: List[str] = Field(default_factory=list)
    final_answer: str = ""
    answer_quality_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # Error Handling
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    retry_attempts: Dict[str, int] = Field(default_factory=dict)
    
    # Performance Metrics
    metrics: WorkflowMetrics = Field(default_factory=WorkflowMetrics)
    
    # Context and Configuration
    context: Dict[str, Any] = Field(default_factory=dict)
    config: Dict[str, Any] = Field(default_factory=dict)
    
    @field_validator('query_complexity')
    @classmethod
    def validate_complexity(cls, v):
        if v not in ['low', 'medium', 'high']:
            raise ValueError('Query complexity must be low, medium, or high')
        return v
    
    @field_validator('expected_response_type')
    @classmethod
    def validate_response_type(cls, v):
        valid_types = ['brief', 'comprehensive', 'technical', 'summary']
        if v not in valid_types:
            raise ValueError(f'Response type must be one of {valid_types}')
        return v
    
    def add_research_result(self, result: ResearchResult) -> None:
        """Add research result and update metrics."""
        self.research_results.append(result)
        self.last_update = datetime.now()
    
    def add_library_result(self, result: LibraryResult) -> None:
        """Add library result and update metrics."""
        self.library_results.append(result)
        self.last_update = datetime.now()
    
    def add_analysis_result(self, result: AnalysisResult) -> None:
        """Add analysis result and update metrics."""
        self.analysis_results.append(result)
        self.last_update = datetime.now()
    
    def update_agent_progress(self, agent_id: str, progress: AgentProgress) -> None:
        """Update progress for specific agent."""
        self.agent_progress[agent_id] = progress
        self.last_update = datetime.now()
    
    def advance_phase(self, new_phase: TaskPhase) -> None:
        """Advance to next workflow phase."""
        self.current_phase = new_phase
        self.last_update = datetime.now()
    
    def add_error(self, error_message: str, agent_id: Optional[str] = None) -> None:
        """Add error to workflow state."""
        error_entry = f"[{datetime.now().isoformat()}]"
        if agent_id:
            error_entry += f" [{agent_id}]"
        error_entry += f" {error_message}"
        self.errors.append(error_entry)
        self.metrics.error_count += 1
        self.last_update = datetime.now()
    
    def add_warning(self, warning_message: str, agent_id: Optional[str] = None) -> None:
        """Add warning to workflow state."""
        warning_entry = f"[{datetime.now().isoformat()}]"
        if agent_id:
            warning_entry += f" [{agent_id}]"
        warning_entry += f" {warning_message}"
        self.warnings.append(warning_entry)
        self.last_update = datetime.now()
    
    def get_completion_percentage(self) -> float:
        """Calculate overall workflow completion percentage."""
        if not self.execution_plan:
            return 0.0
        
        completed = len(self.completed_steps)
        total = len(self.execution_plan)
        return (completed / total) * 100.0 if total > 0 else 0.0
    
    def get_active_agents(self) -> List[str]:
        """Get list of currently active agent IDs."""
        return [
            agent_id for agent_id, progress in self.agent_progress.items()
            if progress.status == AgentStatus.WORKING
        ]
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of current workflow state."""
        return {
            "workflow_id": self.workflow_id,
            "current_phase": self.current_phase.value,
            "completion_percentage": self.get_completion_percentage(),
            "active_agents": self.get_active_agents(),
            "research_count": len(self.research_results),
            "library_count": len(self.library_results),
            "analysis_count": len(self.analysis_results),
            "error_count": len(self.errors),
            "warning_count": len(self.warnings),
            "execution_time": (datetime.now() - self.start_time).total_seconds()
        }

class FlowResult(BaseModel):
    """Final result from flow execution."""
    flow_id: str
    flow_type: str
    final_state: WorkflowState
    success: bool
    final_answer: str
    execution_time: float
    metrics: WorkflowMetrics
    timestamp: datetime = Field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "flow_id": self.flow_id,
            "flow_type": self.flow_type,
            "success": self.success,
            "final_answer": self.final_answer,
            "execution_time": self.execution_time,
            "metrics": self.metrics.dict(),
            "summary": self.final_state.get_summary(),
            "timestamp": self.timestamp.isoformat()
        } 