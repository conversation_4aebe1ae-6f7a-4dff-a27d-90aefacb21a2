#!/usr/bin/env python3
"""
Training Database Inspector

This script inspects the training data database to verify what's being stored
and provides detailed analysis of the collected training examples.

Features:
- Database schema inspection
- Training examples listing with details
- Quality metrics analysis
- Data integrity verification
- Export capabilities
"""

import sqlite3
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# Add src to path for imports
sys.path.insert(0, 'src')

@dataclass
class TrainingExample:
    """Training example data structure."""
    id: str
    session_id: str
    timestamp: str
    original_query: str
    optimized_query: str
    final_answer: str
    workflow_result: Dict[str, Any]
    context: Dict[str, Any]
    quality_metrics: Dict[str, Any]
    feedback_data: Optional[Dict[str, Any]] = None

class TrainingDatabaseInspector:
    """Inspector for training data database."""
    
    def __init__(self, db_path: str = "data/training_data.db"):
        """
        Initialize database inspector.
        
        Args:
            db_path: Path to the training data database
        """
        self.db_path = Path(db_path)
        self.connection = None
        
    def connect(self) -> bool:
        """Connect to the database."""
        try:
            if not self.db_path.exists():
                print(f"❌ Database not found: {self.db_path}")
                return False
                
            self.connection = sqlite3.connect(str(self.db_path))
            self.connection.row_factory = sqlite3.Row  # Enable column access by name
            print(f"✅ Connected to database: {self.db_path}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False
    
    def inspect_schema(self) -> Dict[str, Any]:
        """Inspect database schema and structure."""
        if not self.connection:
            return {}
            
        schema_info = {}
        cursor = self.connection.cursor()
        
        try:
            # Get table list
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            schema_info['tables'] = tables
            
            print(f"📋 Database Tables: {tables}")
            
            # Get schema for each table
            for table in tables:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                schema_info[table] = {
                    'columns': [{'name': col[1], 'type': col[2], 'not_null': bool(col[3]), 'primary_key': bool(col[5])} for col in columns],
                    'column_count': len(columns)
                }
                
                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                schema_info[table]['row_count'] = row_count
                
                print(f"\n📊 Table: {table}")
                print(f"   Rows: {row_count}")
                print(f"   Columns: {len(columns)}")
                for col in columns:
                    print(f"     - {col[1]}: {col[2]} {'(PK)' if col[5] else ''} {'(NOT NULL)' if col[3] else ''}")
            
            return schema_info
            
        except Exception as e:
            print(f"❌ Schema inspection failed: {e}")
            return {}
    
    def get_training_examples(self, limit: int = 10, quality_threshold: float = None) -> List[TrainingExample]:
        """Get training examples from database."""
        if not self.connection:
            return []
            
        examples = []
        cursor = self.connection.cursor()
        
        try:
            # Base query - use correct column names from actual schema
            query = """
                SELECT example_id, session_id, timestamp, original_query, optimized_query, 
                       final_answer, context, quality_score
                FROM training_examples 
                ORDER BY timestamp DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            print(f"\n📚 Retrieved {len(rows)} training examples:")
            
            for row in rows:
                try:
                    # Parse JSON fields - adjust for actual schema
                    context = json.loads(row['context']) if row['context'] else {}
                    quality_score = row['quality_score'] if row['quality_score'] else 0.0
                    
                    # Apply quality threshold filter if specified
                    if quality_threshold is not None:
                        if quality_score < quality_threshold:
                            continue
                    
                    # Extract quality_metrics from context (where they're actually stored)
                    context_quality_metrics = context.get('quality_metrics', {})
                    
                    # Create quality_metrics dict - prioritize context data over DB column
                    quality_metrics = {
                        'answer_quality_score': context_quality_metrics.get('answer_quality_score', quality_score),
                        'relevance_score': context_quality_metrics.get('relevance_score', 0.0),
                        'coherence_score': context_quality_metrics.get('coherence_score', 0.0),
                        'instruction_following_score': context_quality_metrics.get('instruction_following_score', 0.0),
                        'tool_efficiency_score': context_quality_metrics.get('tool_efficiency_score', 0.0)
                    }
                    
                    example = TrainingExample(
                        id=row['example_id'],
                        session_id=row['session_id'],
                        timestamp=row['timestamp'],
                        original_query=row['original_query'],
                        optimized_query=row['optimized_query'],
                        final_answer=row['final_answer'],
                        workflow_result={},  # Not available in this schema version
                        context=context,
                        quality_metrics=quality_metrics
                    )
                    
                    examples.append(example)
                    
                except Exception as e:
                    print(f"   ⚠️ Failed to parse row {row['example_id']}: {e}")
                    continue
            
            return examples
            
        except Exception as e:
            print(f"❌ Failed to retrieve training examples: {e}")
            return []
    
    def analyze_quality_metrics(self, examples: List[TrainingExample]) -> Dict[str, Any]:
        """Analyze quality metrics across training examples."""
        if not examples:
            return {}
        
        analysis = {
            'total_examples': len(examples),
            'quality_scores': [],
            'metric_averages': {},
            'metric_distributions': {},
            'evaluation_status': {'with_evaluation': 0, 'without_evaluation': 0}
        }
        
        # Collect all quality scores
        all_metrics = {
            'answer_quality_score': [],
            'relevance_score': [],
            'coherence_score': [],
            'instruction_following_score': [],
            'tool_efficiency_score': []
        }
        
        for example in examples:
            quality_metrics = example.quality_metrics
            
            # Check if evaluation was performed (look for any non-zero metric)
            has_evaluation = (
                quality_metrics.get('answer_quality_score', 0) > 0 or
                quality_metrics.get('relevance_score', 0) > 0 or
                quality_metrics.get('coherence_score', 0) > 0 or
                quality_metrics.get('instruction_following_score', 0) > 0
            )
            
            if has_evaluation:
                analysis['evaluation_status']['with_evaluation'] += 1
            else:
                analysis['evaluation_status']['without_evaluation'] += 1
            
            # Collect metric values
            for metric_name in all_metrics.keys():
                value = quality_metrics.get(metric_name, 0.0)
                all_metrics[metric_name].append(value)
        
        # Calculate averages and distributions
        for metric_name, values in all_metrics.items():
            if values:
                avg_value = sum(values) / len(values)
                analysis['metric_averages'][metric_name] = avg_value
                
                # Distribution analysis
                distribution = {
                    'min': min(values),
                    'max': max(values),
                    'avg': avg_value,
                    'count_above_0.5': sum(1 for v in values if v > 0.5),
                    'count_above_0.7': sum(1 for v in values if v > 0.7),
                    'count_above_0.8': sum(1 for v in values if v > 0.8),
                }
                analysis['metric_distributions'][metric_name] = distribution
        
        return analysis
    
    def check_data_integrity(self, examples: List[TrainingExample]) -> Dict[str, Any]:
        """Check data integrity and completeness."""
        integrity_report = {
            'total_examples': len(examples),
            'complete_examples': 0,
            'missing_fields': {},
            'json_parse_errors': 0,
            'workflow_types': {},
            'session_distribution': {}
        }
        
        required_fields = ['id', 'session_id', 'original_query', 'final_answer']
        
        for example in examples:
            # Check required fields
            complete = True
            for field in required_fields:
                if not getattr(example, field, None):
                    if field not in integrity_report['missing_fields']:
                        integrity_report['missing_fields'][field] = 0
                    integrity_report['missing_fields'][field] += 1
                    complete = False
            
            if complete:
                integrity_report['complete_examples'] += 1
            
            # Workflow type distribution
            workflow_type = example.workflow_result.get('workflow_type', 'Unknown')
            if workflow_type not in integrity_report['workflow_types']:
                integrity_report['workflow_types'][workflow_type] = 0
            integrity_report['workflow_types'][workflow_type] += 1
            
            # Session distribution
            session_id = example.session_id
            if session_id not in integrity_report['session_distribution']:
                integrity_report['session_distribution'][session_id] = 0
            integrity_report['session_distribution'][session_id] += 1
        
        return integrity_report
    
    def print_example_details(self, example: TrainingExample, show_full_answer: bool = False):
        """Print detailed information about a training example."""
        print(f"\n🔍 Training Example: {example.id[:8]}...")
        print(f"   📅 Timestamp: {example.timestamp}")
        print(f"   🔗 Session: {example.session_id[:8]}...")
        print(f"   ❓ Query: {example.original_query[:100]}...")
        print(f"   🔧 Optimized: {example.optimized_query[:100]}...")
        
        if show_full_answer:
            print(f"   💡 Answer: {example.final_answer}")
        else:
            print(f"   💡 Answer: {example.final_answer[:100]}...")
        
        # Quality metrics
        print(f"   📊 Quality Metrics:")
        print(f"      Answer Quality: {example.quality_metrics.get('answer_quality_score', 0.0):.3f}")
        print(f"      Relevance: {example.quality_metrics.get('relevance_score', 0.0):.3f}")
        print(f"      Coherence: {example.quality_metrics.get('coherence_score', 0.0):.3f}")
        print(f"      Instruction Following: {example.quality_metrics.get('instruction_following_score', 0.0):.3f}")
        print(f"      Tool Efficiency: {example.quality_metrics.get('tool_efficiency_score', 0.0):.3f}")
        
        # Workflow info
        print(f"   🔄 Workflow Type: {example.context.get('workflow_type', 'Unknown')}")
        print(f"   🛠️ Tools Used: {len(example.context.get('tools_used', []))}")
        print(f"   🤖 Enhanced Agents: {len(example.context.get('enhanced_agents_used', []))}")
        
        # Check if we have evaluation results stored in context
        context_quality = example.context.get('quality_metrics', {})
        if context_quality:
            print(f"   ✅ Context Quality Metrics Found:")
            print(f"      Answer Quality: {context_quality.get('answer_quality_score', 0.0):.3f}")
            print(f"      Relevance: {context_quality.get('relevance_score', 0.0):.3f}")
            print(f"      Coherence: {context_quality.get('coherence_score', 0.0):.3f}")
            print(f"      Instruction Following: {context_quality.get('instruction_following_score', 0.0):.3f}")
            print(f"      Tool Efficiency: {context_quality.get('tool_efficiency_score', 0.0):.3f}")
        else:
            print(f"   ❌ No Quality Metrics in Context")
    
    def export_to_json(self, examples: List[TrainingExample], filename: str = None) -> str:
        """Export training examples to JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"training_data_export_{timestamp}.json"
        
        try:
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'total_examples': len(examples),
                'examples': []
            }
            
            for example in examples:
                export_data['examples'].append({
                    'id': example.id,
                    'session_id': example.session_id,
                    'timestamp': example.timestamp,
                    'original_query': example.original_query,
                    'optimized_query': example.optimized_query,
                    'final_answer': example.final_answer,
                    'workflow_result': example.workflow_result,
                    'context': example.context,
                    'quality_metrics': example.quality_metrics
                })
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ Exported {len(examples)} examples to {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Export failed: {e}")
            return ""
    
    def close(self):
        """Close database connection."""
        if self.connection:
            self.connection.close()
            print("🔒 Database connection closed")


def main():
    """Main inspection function."""
    print("🔍 Training Database Inspector")
    print("=" * 50)
    
    # Initialize inspector
    inspector = TrainingDatabaseInspector()
    
    if not inspector.connect():
        return
    
    try:
        # 1. Inspect database schema
        print("\n📋 1. DATABASE SCHEMA INSPECTION")
        print("-" * 40)
        schema_info = inspector.inspect_schema()
        
        # 2. Get training examples
        print("\n📚 2. TRAINING EXAMPLES RETRIEVAL")
        print("-" * 40)
        examples = inspector.get_training_examples(limit=20)
        
        if not examples:
            print("❌ No training examples found")
            return
        
        # 3. Quality metrics analysis
        print("\n📊 3. QUALITY METRICS ANALYSIS")
        print("-" * 40)
        quality_analysis = inspector.analyze_quality_metrics(examples)
        
        print(f"Total Examples: {quality_analysis['total_examples']}")
        print(f"With Evaluation: {quality_analysis['evaluation_status']['with_evaluation']}")
        print(f"Without Evaluation: {quality_analysis['evaluation_status']['without_evaluation']}")
        
        print(f"\nAverage Quality Scores:")
        for metric, avg in quality_analysis.get('metric_averages', {}).items():
            print(f"  {metric}: {avg:.3f}")
        
        print(f"\nQuality Distributions:")
        for metric, dist in quality_analysis.get('metric_distributions', {}).items():
            print(f"  {metric}:")
            print(f"    Range: {dist['min']:.3f} - {dist['max']:.3f}")
            print(f"    Above 0.7: {dist['count_above_0.7']}/{quality_analysis['total_examples']}")
        
        # 4. Data integrity check
        print("\n🔧 4. DATA INTEGRITY CHECK")
        print("-" * 40)
        integrity = inspector.check_data_integrity(examples)
        
        print(f"Complete Examples: {integrity['complete_examples']}/{integrity['total_examples']}")
        if integrity['missing_fields']:
            print(f"Missing Fields: {integrity['missing_fields']}")
        
        print(f"Workflow Types: {integrity['workflow_types']}")
        print(f"Unique Sessions: {len(integrity['session_distribution'])}")
        
        # 5. Show detailed examples
        print("\n🔍 5. DETAILED EXAMPLE ANALYSIS")
        print("-" * 40)
        
        # Show latest example
        if examples:
            latest_example = examples[0]  # Already sorted by timestamp DESC
            print("Latest Example:")
            inspector.print_example_details(latest_example, show_full_answer=False)
        
        # Show example with highest quality (if any)
        quality_examples = [ex for ex in examples if ex.quality_metrics.get('answer_quality_score', 0) > 0]
        if quality_examples:
            best_example = max(quality_examples, key=lambda x: x.quality_metrics.get('answer_quality_score', 0))
            print("\nHighest Quality Example:")
            inspector.print_example_details(best_example, show_full_answer=False)
        
        # 6. Export option
        print("\n💾 6. EXPORT OPTION")
        print("-" * 40)
        export_choice = input("Export all training data to JSON? (y/n): ").strip().lower()
        if export_choice in ['y', 'yes']:
            filename = inspector.export_to_json(examples)
            if filename:
                print(f"📁 Data exported to: {filename}")
        
        print("\n" + "=" * 50)
        print("🎉 Training Database Inspection Complete!")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Inspection failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        inspector.close()


if __name__ == "__main__":
    main() 