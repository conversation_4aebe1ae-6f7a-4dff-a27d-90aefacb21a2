version: '3.8'

services:
  # DSPy Multi-Agent System API
  dspy-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: dspy-multi-agent-api
    restart: unless-stopped
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      # API Configuration
      - API_KEY=${API_KEY:-dev-api-key-12345}
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - WORKERS=${WORKERS:-1}
      
      # AI Service API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SERPER_API_KEY=${SERPER_API_KEY}
      
      # Redis Configuration (optional)
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      
      # File Upload Configuration
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-10485760}
      - UPLOAD_DIR=/app/data/uploads
      
      # System Configuration
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    volumes:
      # Persistent data storage
      - dspy_data:/app/data
      - dspy_logs:/app/logs
      - dspy_cache:/app/cache
      - dspy_checkpoints:/app/checkpoints
      - dspy_monitoring:/app/monitoring
      
      # Optional: Mount config file
      - ./config.yaml:/app/config.yaml:ro
    depends_on:
      - redis
    networks:
      - dspy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis (optional but recommended for production)
  redis:
    image: redis:7-alpine
    container_name: dspy-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    networks:
      - dspy-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: >
      sh -c "
        if [ -n \"$$REDIS_PASSWORD\" ]; then
          redis-server --requirepass $$REDIS_PASSWORD --appendonly yes
        else
          redis-server --appendonly yes
        fi
      "

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: dspy-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - dspy-api
    networks:
      - dspy-network
    profiles:
      - production

volumes:
  # Application data volumes
  dspy_data:
    driver: local
  dspy_logs:
    driver: local
  dspy_cache:
    driver: local
  dspy_checkpoints:
    driver: local
  dspy_monitoring:
    driver: local
  
  # Redis data volume
  redis_data:
    driver: local
  
  # Nginx logs volume
  nginx_logs:
    driver: local

networks:
  dspy-network:
    driver: bridge
