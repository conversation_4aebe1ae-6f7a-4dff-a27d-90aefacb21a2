import sys
sys.path.append('src')
import dspy
from optimization.dspy.qa_modules import MultiAgentQAModule, create_training_examples
from optimization.dspy.base_optimizer import get_optimizer

def test_dspy_modules():
    """Test DSPy optimization modules independently."""
    
    # Configure DSPy with OpenAI (using new gpt-4.1-mini)
    lm = dspy.LM(model='gpt-4.1-mini')
    dspy.configure(lm=lm)
    
    print('🔥 Testing DSPy Modules...')
    
    # Test 1: Create QA Module
    qa_module = MultiAgentQAModule()
    print('✅ MultiAgentQAModule created')
    
    # Test 2: Create training examples
    sample_qa_data = [
        {
            "question": "What are the benefits of machine learning?",
            "answer": "Machine learning offers benefits like automation, improved accuracy, predictive analytics, personalization, and data-driven decision making."
        },
        {
            "question": "How does artificial intelligence impact business?",
            "answer": "AI impacts business through process automation, enhanced customer service, improved decision making, cost reduction, and competitive advantages."
        },
        {
            "question": "What is natural language processing?",
            "answer": "Natural language processing (NLP) is a branch of AI that helps computers understand, interpret, and generate human language."
        }
    ]
    training_examples = create_training_examples(sample_qa_data)
    print(f'✅ Training examples created: {len(training_examples)} examples')
    
    # Test 3: Run a simple prediction
    question = 'What are the benefits of machine learning?'
    print(f'🤔 Question: {question}')
    
    prediction = qa_module(question=question)
    print(f'🎯 DSPy Answer: {prediction.final_answer[:500]}...')
    print(f'📊 Confidence: {prediction.confidence_assessment}')
    print(f'📚 Evidence: {prediction.supporting_evidence[:200]}...')
    
    # Test 4: Test optimizer
    optimizer = get_optimizer(dataset_size=len(training_examples))
    print(f'✅ Optimizer created: {optimizer.__class__.__name__}')
    
    print('🎉 DSPy modules working!')

if __name__ == "__main__":
    test_dspy_modules() 