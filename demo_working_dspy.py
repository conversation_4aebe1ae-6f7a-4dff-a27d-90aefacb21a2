#!/usr/bin/env python3
"""
Demonstration of DSPy working properly with real data
This bypasses the CrewAI workflow to show what should happen
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def demonstrate_working_dspy():
    """Show DSPy working with real data."""
    
    print("🔥 DSPy Working Demonstration")
    print("="*60)
    
    # Check API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY not set")
        return
    
    try:
        import dspy
        from optimization.dspy.qa_modules import MultiAgentQAModule
        from tools.search.web_search_tool import WebSearchTool
        from tools.processors.data_analysis_tool import DataAnalysisTool
        
        # Step 1: Configure DSPy
        print("📡 Step 1: Configuring DSPy with gpt-4.1-mini...")
        lm = dspy.LM(model='gpt-4.1-mini', max_tokens=1500)
        dspy.configure(lm=lm)
        print("✅ DSPy configured")
        
        # Step 2: Get real data from tools
        print("\n🔧 Step 2: Gathering real data from tools...")
        
        web_tool = WebSearchTool()
        analysis_tool = DataAnalysisTool()
        
        question = "What are the benefits of artificial intelligence?"
        
        # Get real web search data
        print("   🔍 Searching web...")
        web_data = web_tool._run(question + " 2024 2025", max_results=5)
        print(f"   ✅ Got {len(web_data)} chars of web data")
        
        # Get analysis of the question
        print("   📊 Analyzing question...")
        analysis_data = analysis_tool._run(question, analysis_type="detailed")
        print(f"   ✅ Got {len(analysis_data)} chars of analysis")
        
        # Step 3: Create context with real data
        print("\n🧠 Step 3: Creating rich context...")
        context = f"""
        Web Research Results:
        {web_data}
        
        Question Analysis:
        {analysis_data}
        
        Additional Context: This question asks about AI benefits in 2024-2025.
        """
        
        print(f"   ✅ Created context with {len(context)} characters")
        
        # Step 4: Use DSPy with real data
        print("\n🤖 Step 4: Processing with DSPy MultiAgentQAModule...")
        qa_system = MultiAgentQAModule(use_research=True, use_library=False, use_analysis=True)
        
        # Pass real context to DSPy
        result = qa_system(question=question, context=context)
        
        # Step 5: Display the proper results
        print(f"\n🎯 Step 5: DSPy Results with Real Data!")
        print("="*60)
        
        print(f"❓ QUESTION:")
        print(f"{question}")
        
        print(f"\n✨ FINAL ANSWER:")
        print(f"{result.final_answer}")
        
        print(f"\n📊 CONFIDENCE:")
        print(f"{result.confidence}")
        
        print(f"\n📚 EVIDENCE:")
        print(f"{result.evidence}")
        
        # Step 6: Explain the difference
        print(f"\n🔍 Step 6: What Made This Work:")
        print("="*60)
        print("✅ Real web search data (not empty)")
        print("✅ Real analysis results (not placeholders)")
        print("✅ Rich context passed to DSPy")
        print("✅ DSPy applied reasoning to actual information")
        print("✅ Structured output with real insights")
        
        print(f"\n💡 Contrast with Your Previous Run:")
        print("❌ Previous: Empty research findings")
        print("❌ Previous: Empty library documents") 
        print("❌ Previous: Empty analysis insights")
        print("❌ Previous: Placeholder content like '[specific topic]'")
        print("✅ This demo: Real data → Real insights")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demonstrate_working_dspy() 