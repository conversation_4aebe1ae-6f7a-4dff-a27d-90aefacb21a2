version: '3.8'

# Production overrides for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

services:
  dspy-api:
    # Production optimizations
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
    environment:
      # Production environment variables
      - LOG_LEVEL=warning
      - WORKERS=4
      - DEBUG_MODE=false
      
      # Performance optimizations
      - PYTHONOPTIMIZE=1
      - PYTHONHASHSEED=random
      
      # Security
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    
    # Production health check (more frequent)
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 30s
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # Read-only root filesystem (except for writable volumes)
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /var/tmp:noexec,nosuid,size=50m

  redis:
    # Production Redis configuration
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Redis production settings
    command: >
      sh -c "
        if [ -n \"$$REDIS_PASSWORD\" ]; then
          redis-server 
            --requirepass $$REDIS_PASSWORD 
            --appendonly yes 
            --appendfsync everysec 
            --maxmemory 512mb 
            --maxmemory-policy allkeys-lru
            --tcp-keepalive 60
            --timeout 300
        else
          redis-server 
            --appendonly yes 
            --appendfsync everysec 
            --maxmemory 512mb 
            --maxmemory-policy allkeys-lru
            --tcp-keepalive 60
            --timeout 300
        fi
      "
    
    # Production health check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 15s
      timeout: 3s
      retries: 5
    
    # Security
    security_opt:
      - no-new-privileges:true

  nginx:
    # Production Nginx configuration
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    
    # Additional production volumes
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates
      - nginx_logs:/var/log/nginx
      - nginx_cache:/var/cache/nginx
    
    # Production health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3
    
    # Security
    security_opt:
      - no-new-privileges:true

# Additional production volumes
volumes:
  nginx_cache:
    driver: local
  
  # SSL certificates volume
  ssl_certs:
    driver: local

# Production network configuration
networks:
  dspy-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
