"""
DSPy + CrewAI + Lang<PERSON>hain Multi-Agent Pipeline
=============================================
A production-ready multi-agent system where DSPy handles optimization and CrewAI orchestrates 
task execution. Follows 2025 best practices for security, performance, and maintainability.

Architecture:
- Task Manager (Plans work)  
- Researcher (Web search via Serper/DuckDuckGo with Redis caching)
- <PERSON><PERSON><PERSON> (Internal doc retrieval via FAISS)  
- Writer (Synthesis & final answer)

Requirements:
- Set OPENAI_API_KEY and SERPER_API_KEY environment variables
- Place documents in ./docs/ folder
- Redis server running on localhost:6379 (optional, will gracefully degrade)

TODO for Production:
- Move to YAML configuration files
- Implement proper dependency injection container
- Add comprehensive logging and monitoring
- Modularize into separate files/packages
- Add unit tests and integration tests
"""

import os
import sys
from pathlib import Path
from typing import Optional
import logging
from dataclasses import dataclass
import warnings

# Configure logging early
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('pipeline.log', mode='a')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class Config:
    """Configuration management following 2025 best practices"""
    openai_api_key: str
    serper_api_key: Optional[str]
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    docs_dir: Path = Path("./docs")
    cache_dir: Path = Path("./cache")
    
    # Model configurations - using 2025 current models
    dspy_model: str = "openai/gpt-4.1-nano"  # Optimized for compilation
    crewai_model: str = "gpt-4.1-nano"       # Balanced cost/performance for agents
    
    # Performance settings
    max_retries: int = 3
    request_timeout: float = 30.0
    redis_timeout: float = 5.0
    
    @classmethod
    def from_env(cls) -> 'Config':
        """Load configuration from environment variables with validation"""
        openai_key = os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")
        if not openai_key:
            raise ValueError(
                "OPENAI_API_KEY environment variable is required. "
                "Set it with: export OPENAI_API_KEY='your-key-here'"
            )
        
        serper_key = os.getenv("SERPER_API_KEY", "95df768820e787e50423168ddc98b9f3784a7a52")
        if not serper_key:
            logger.warning(
                "SERPER_API_KEY not set. Web search will fall back to DuckDuckGo only. "
                "For better results, set: export SERPER_API_KEY='your-key-here'"
            )
        
        config = cls(
            openai_api_key=openai_key,
            serper_api_key=serper_key,
            redis_host=os.getenv("REDIS_HOST", "localhost"),
            redis_port=int(os.getenv("REDIS_PORT", "6379")),
            redis_db=int(os.getenv("REDIS_DB", "0")),
        )
        
        # Ensure directories exist
        config.docs_dir.mkdir(exist_ok=True)
        config.cache_dir.mkdir(exist_ok=True)
        
        return config

# Initialize configuration
try:
    config = Config.from_env()
    logger.info("Configuration loaded successfully")
except ValueError as e:
    logger.error(f"Configuration error: {e}")
    sys.exit(1)

# Set DSPy cache directory
os.environ["DSPY_CACHEDIR"] = str(config.cache_dir.resolve())
logger.info(f"DSPy cache directory: {config.cache_dir}")

# === IMPORTS (Organized by functionality) ===
# Core framework imports
import dspy
from langchain_openai import ChatOpenAI

# CrewAI and tools
from crewai_tools import ScrapeWebsiteTool, SerperDevTool, WebsiteSearchTool
from langchain.tools import tool
from langchain_core.tools import BaseTool
from langchain_community.tools import DuckDuckGoSearchRun, WikipediaQueryRun  
from langchain_community.utilities import DuckDuckGoSearchAPIWrapper, WikipediaAPIWrapper

# Document processing and vector search
from langchain_community.document_loaders import DirectoryLoader, TextLoader
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS

# Redis and caching
import redis
from redis.exceptions import ConnectionError as RedisConnectionError
import hashlib

# Utilities
import openai
import re
import functools
import json
import inspect
from pydantic import BaseModel, Field
from typing import Type, Dict, Any, List, Callable, Optional, Union
import time
from contextlib import contextmanager

# === LLM CONFIGURATION (2025 Best Practices) ===

@contextmanager
def error_handler(operation: str):
    """Context manager for consistent error handling"""
    try:
        yield
    except Exception as e:
        logger.error(f"Error in {operation}: {type(e).__name__}: {e}")
        raise

class LLMManager:
    """Centralized LLM configuration and management"""
    
    def __init__(self, config: Config):
        self.config = config
        
        # Set environment variables for libraries that access them directly
        os.environ["OPENAI_API_KEY"] = config.openai_api_key
        if config.serper_api_key:
            os.environ["SERPER_API_KEY"] = config.serper_api_key
            
        openai.api_key = config.openai_api_key
        
        # Initialize LLMs with proper error handling and retry logic
        self.llm_crewai = self._create_crewai_llm()
        self.llm_dspy = self._create_dspy_llm()
        
        # Configure DSPy globally
        dspy.configure(lm=self.llm_dspy)
        
                # Enhanced logging for debugging (can be disabled in production)
        dspy.settings.log_prompts = True
        dspy.settings.log_generated_outputs = True
        
        logger.info(f"LLMs initialized - CrewAI: {config.crewai_model}, DSPy: {config.dspy_model}")
    
    def _create_crewai_llm(self) -> ChatOpenAI:
        """Create ChatOpenAI instance with retry logic and proper configuration"""
        return ChatOpenAI(
            model_name=self.config.crewai_model,
            temperature=0,  # Deterministic for consistency
            openai_api_key=self.config.openai_api_key,
            timeout=self.config.request_timeout,
            max_retries=self.config.max_retries,
            request_timeout=self.config.request_timeout,
        )
    
    def _create_dspy_llm(self) -> dspy.LM:
        """Create DSPy LM with caching and proper configuration"""
        return dspy.LM(
            self.config.dspy_model,
            api_key=self.config.openai_api_key,
            cache=True,  # Enable caching for compilation efficiency
            timeout=self.config.request_timeout,
        )

# Initialize LLM manager
with error_handler("LLM initialization"):
    llm_manager = LLMManager(config)
    
    # Use consistent naming throughout codebase
    crew_llm = llm_manager.llm_crewai
    dspy_llm = llm_manager.llm_dspy
    
    # Maintain backward compatibility for existing references
    LLM_lc = crew_llm
    LLM_dspy = dspy_llm

# === REDIS CONNECTION MANAGER (2025 Best Practices) ===

class RedisManager:
    """Redis connection manager with connection pooling and graceful degradation"""
    
    def __init__(self, config: Config):
        self.config = config
        self.client = None
        self.available = False
        self._initialize_connection()
    
    def _initialize_connection(self):
        """Initialize Redis connection with proper error handling"""
        try:
            # Use connection pooling for better performance
            pool = redis.ConnectionPool(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                socket_timeout=self.config.redis_timeout,
                socket_connect_timeout=self.config.redis_timeout,
                max_connections=10,  # Connection pooling
                retry_on_timeout=True
            )
            
            self.client = redis.Redis(connection_pool=pool)
            
            # Test connection
            self.client.ping()
            self.available = True
            logger.info(f"Redis connected successfully at {self.config.redis_host}:{self.config.redis_port}")
            
        except (RedisConnectionError, redis.TimeoutError, ConnectionRefusedError) as e:
            logger.warning(f"Redis connection failed: {e}. Caching disabled, will continue without Redis.")
            self.available = False
            self.client = None
    
    def get(self, key: str) -> Optional[str]:
        """Get value from Redis with error handling"""
        if not self.available:
            return None
        
        try:
            result = self.client.get(key)
            return result.decode('utf-8') if result else None
        except Exception as e:
            logger.warning(f"Redis GET error for key {key}: {e}")
            return None
    
    def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """Set value in Redis with error handling"""
        if not self.available:
            return False
        
        try:
            return self.client.set(key, value, ex=ex)
        except Exception as e:
            logger.warning(f"Redis SET error for key {key}: {e}")
            return False
    
    def generate_cache_key(self, prefix: str, data: str) -> str:
        """Generate consistent cache keys"""
        hash_value = hashlib.sha256(data.encode('utf-8')).hexdigest()[:16]
        return f"{prefix}:{hash_value}"

# Initialize Redis manager
redis_manager = RedisManager(config)

# === TOOL IMPLEMENTATIONS (2025 Best Practices) ===

class ToolManager:
    """Manages all search and scraping tools with proper error handling"""
    
    def __init__(self, config: Config, redis_manager: RedisManager):
        self.config = config
        self.redis = redis_manager
        
        # Initialize tools with proper error handling
        self._initialize_tools()
        
    def _initialize_tools(self):
        """Initialize all tools with proper configuration"""
        try:
            # Serper tool (premium web search)
            self.serper_tool = None
            if self.config.serper_api_key:
                self.serper_tool = SerperDevTool(api_key=self.config.serper_api_key)
                logger.info("Serper tool initialized successfully")
            
            # Fallback search tools
            search_wrapper = DuckDuckGoSearchAPIWrapper()
            self.duckduckgo_tool = DuckDuckGoSearchRun(api_wrapper=search_wrapper)
            
            wiki_wrapper = WikipediaAPIWrapper()
            self.wikipedia_tool = WikipediaQueryRun(api_wrapper=wiki_wrapper)
            
            # Website tools
            self.website_search_tool = WebsiteSearchTool()
            self.scrape_tool = ScrapeWebsiteTool()
            
            logger.info("All search tools initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing tools: {e}")
            raise

# Initialize tool manager
tool_manager = ToolManager(config, redis_manager)

# === DSPy Tool Functions (Updated to use new managers) ===

def dspy_search_tool_func(query: str) -> str:
    """A simple search tool for DSPy using DuckDuckGo. Input: query string. Output: search results string."""
    print(f"[DSPY_SEARCH_TOOL_FUNC_DEBUG] Querying DuckDuckGo with: {query}")
    results = tool_manager.duckduckgo_tool.run(query)
    print(f"[DSPY_SEARCH_TOOL_FUNC_DEBUG] DuckDuckGo Results (first 200 chars): {results[:200]}")
    return results

def dspy_serper_tool_func(query: str) -> str:
    """A simple search tool for DSPy using Serper if available, otherwise DuckDuckGo.
    Input: query string. Output: search results string."""
    cache_key = redis_manager.generate_cache_key("serper", query)
    cached = redis_manager.get(cache_key)
    if cached:
        print(f"[SERPER_CACHE] Cache hit for query: {query}")
        return cached
    
    if tool_manager.serper_tool:
        print(f"[DSPY_SERPER_TOOL_FUNC_DEBUG] Querying SerperDevTool with: {query}")
        try:
            results = tool_manager.serper_tool.run(search_query=query)
            print(f"[DSPY_SERPER_TOOL_FUNC_DEBUG] Serper raw results type: {type(results)}")
            print(f"[DSPY_SERPER_TOOL_FUNC_DEBUG] Serper raw results value: {results}")
            result_str = str(results) if results is not None else ""
            redis_manager.set(cache_key, result_str, ex=60*60*24)  # Cache for 24 hours
            return result_str
        except Exception as e:
            import traceback
            print(f"[DSPY_SERPER_TOOL_FUNC_DEBUG] Error using Serper or processing its results: {type(e).__name__} - {e}")
            print(traceback.format_exc())
            print(f"[DSPY_SERPER_TOOL_FUNC_DEBUG] Falling back to DuckDuckGo.")
            return dspy_search_tool_func(query)
    else:
        print(f"[DSPY_SERPER_TOOL_FUNC_DEBUG] Serper API key not available. Falling back to DuckDuckGo.")
        return dspy_search_tool_func(query)

def dspy_wiki_tool_func(query: str) -> str:
    """A simple website content search tool for DSPy. Input: query string. Output: website search results string."""
    return tool_manager.website_search_tool.run(query)

def dspy_scrape_tool_func(website_url: str) -> str:
    """Scrapes the content of a given website URL. Input: website_url string. Output: scraped content string."""
    return tool_manager.scrape_tool.run(website_url=website_url)

# === CrewAI Tool Functions (Decorated versions) ===

@tool
def crewai_search_tool_func(query: str) -> str:
    """A tool to search the web for current information using DuckDuckGo. Input should be a search query."""
    return tool_manager.duckduckgo_tool.run(query)

@tool
def crewai_wiki_tool_func(query: str) -> str:
    """A tool to search website content. Input should be a search query."""
    return tool_manager.website_search_tool.run(query)

# Create Tool instances for CrewAI from the simple functions - NO LONGER NEEDED, using decorated functions directly
# crew_search_tool = Tool(
#     name="Web Search",
#     func=simple_search_tool_func,
#     description="A tool to search the web for current information using DuckDuckGo. Input should be a search query."
# )
# crew_wiki_tool = Tool(
#     name="Wikipedia Search",
#     func=simple_wiki_tool_func,
#     description="A tool to search Wikipedia for encyclopedic information. Input should be a search query."
# )

# === VECTOR STORE INITIALIZATION (Updated with new config pattern) ===

class VectorStoreManager:
    """Manages FAISS vector store for document retrieval"""
    
    def __init__(self, config: Config):
        self.config = config
        self.vector_index = None
        self._initialize_vector_store()
    
    def _initialize_vector_store(self):
        """Initialize FAISS vector store from documents directory"""
        try:
            if not self.config.docs_dir.exists():
                logger.info(f"Creating docs directory: {self.config.docs_dir}")
                self.config.docs_dir.mkdir(parents=True, exist_ok=True)
            
            # Load documents
            loader = DirectoryLoader(
                str(self.config.docs_dir), 
                glob="**/*.txt", 
                loader_cls=TextLoader
            )
            documents = loader.load() if list(self.config.docs_dir.glob("**/*.txt")) else []
            
            if documents:
                logger.info(f"Found {len(documents)} documents in {self.config.docs_dir}")
                embeddings = OpenAIEmbeddings(openai_api_key=self.config.openai_api_key)
                self.vector_index = FAISS.from_documents(documents, embeddings)
                logger.info("FAISS vector index created successfully")
                
                # Configure DSPy retriever
                dspy.configure(rm=self.vector_index)
            else:
                logger.warning(f"No .txt documents found in {self.config.docs_dir}. Librarian will return empty results.")
                self.vector_index = None
                
        except Exception as e:
            logger.error(f"Error initializing vector store: {e}")
            self.vector_index = None

# Initialize vector store manager
vector_store_manager = VectorStoreManager(config)
vector_index = vector_store_manager.vector_index  # Maintain backward compatibility

# === DSPy MODULE DEFINITIONS (2025 Best Practices) ===

from dspy import ProgramOfThought, ReAct, Retrieve, Predict, Module, Prediction, Signature
from typing import Callable

class TaskPlanningSignature(Signature):
    """Generate a step-by-step execution plan for answering complex questions"""
    task = dspy.InputField(desc="The user's question or task to be planned")
    plan = dspy.OutputField(desc="JSON list of steps for task execution")

class ResearchSignature(Signature):
    """Conduct web research based on task and plan"""
    task = dspy.InputField(desc="The original user question or task")
    plan = dspy.InputField(desc="The execution plan as a list")
    research_summary = dspy.OutputField(desc="Comprehensive research findings")

class LibrarianSignature(Signature):
    """Retrieve relevant passages from internal knowledge base"""
    query = dspy.InputField(desc="Search query for internal documents")
    passages = dspy.OutputField(desc="Relevant document passages")

class WriterSignature(Signature):
    """Synthesize final answer from all available information"""
    question = dspy.InputField(desc="Original user question")
    web = dspy.InputField(desc="Web research summary")
    docs = dspy.InputField(desc="Internal document passages")
    answer = dspy.OutputField(desc="Comprehensive final answer with citations")

class TaskManager(Predict):
    """Plan how to break the user question into sub‑tasks"""
    def __init__(self):
        super().__init__(TaskPlanningSignature)

class Researcher(ReAct):
    """Gather information using web search and scraping tools"""
    def __init__(self, tools=None, max_iters=3):
        super().__init__(ResearchSignature, tools=tools or [], max_iters=max_iters)

class Librarian(Retrieve):
    """Return passages from the internal knowledge base relevant to query"""
    def __init__(self, k=5):
        super().__init__(LibrarianSignature, k=k)
        # Vector index is configured globally in DSPy

class Writer(Predict):
    """Write a coherent answer that cites URLs & filenames"""
    def __init__(self):
        super().__init__(WriterSignature)

class Swarm(Module):
    """Multi-agent swarm orchestrator using DSPy modules"""
    
    def __init__(self):
        super().__init__()
        
        # Determine primary search tool based on availability
        primary_search_tool = dspy_serper_tool_func if tool_manager.serper_tool else dspy_search_tool_func
        research_tools = [primary_search_tool, dspy_scrape_tool_func]
        
        # Initialize modules with proper configurations
        self.tm = TaskManager()
        self.res = Researcher(tools=research_tools, max_iters=3)
        self.lib = Librarian(k=5)
        self.wrt = Writer()
        
        # Log tool configuration for debugging
        tool_names = [getattr(tool, '__name__', str(tool)) for tool in research_tools]
        logger.info(f"Swarm initialized with Researcher tools: {tool_names}")

    def forward(self, task: str) -> Prediction:
        """Execute the multi-agent workflow"""
        logger.debug(f"Swarm processing task: {task}")
        
        try:
            # Step 1: Generate execution plan
            with error_handler("Task planning"):
                plan_result = self.tm(task=task)
                plan = plan_result.plan
                logger.debug(f"Generated plan: {plan}")

            # Step 2: Conduct web research
            with error_handler("Web research"):
                research_result = self.res(task=task, plan=plan)
                web_summary = research_result.research_summary
                logger.debug(f"Research summary: {web_summary[:200]}...")

            # Step 3: Retrieve internal documents (if available)
            with error_handler("Document retrieval"):
                if vector_index:
                    doc_result = self.lib(query=task)
                    docs = doc_result.passages
                    logger.debug(f"Retrieved {len(docs) if isinstance(docs, list) else 0} document passages")
                else:
                    logger.info("No vector index available, skipping document retrieval")
                    docs = []

            # Step 4: Synthesize final answer
            with error_handler("Answer synthesis"):
                final_result = self.wrt(question=task, web=web_summary, docs=docs)
                final_answer = final_result.answer
                logger.debug(f"Final answer generated: {final_answer[:100]}...")

            return Prediction(answer=final_answer)

        except Exception as e:
            logger.error(f"Error in Swarm execution for task '{task}': {type(e).__name__}: {e}")
            # Return prediction with error info for metric evaluation
            return Prediction(answer="", error_in_forward=str(e))

# === DSPy COMPILATION SETUP (2025 Best Practices) ===

from dspy.teleprompt import BetterTogether, BootstrapFewShotWithRandomSearch

# Define development dataset for compilation
dev_set = [
    dspy.Example(
        task="What is the phone number for the hungarian company IDDQD?", 
        answer="+36 30 850 84 98"
    ).with_inputs("task")
]

logger.info(f"Development dataset size: {len(dev_set)}")

def exact_match(example, pred, trace=None, max_expected_tool_calls=3): # Added max_expected_tool_calls
    """Return a score combining correctness (phone number match) and efficiency (fewer tool calls)."""
    print(f"\n--- exact_match START ---")
    print(f"[EXACT_MATCH_DEBUG] example: {example}")
    print(f"[EXACT_MATCH_DEBUG] pred object type: {type(pred)}")
    print(f"[EXACT_MATCH_DEBUG] pred object stringified: {str(pred)}")
    
    # --- TRACE INSPECTION --- #
    print(f"[EXACT_MATCH_DEBUG] TRACE object type: {type(trace)}")
    # print(f"[EXACT_MATCH_DEBUG] TRACE object value (first 500 chars if long): {str(trace)[:500]}") # Can be very verbose
    if trace is not None and isinstance(trace, list) and len(trace) > 0 :
        print(f"[EXACT_MATCH_DEBUG] First element of TRACE type: {type(trace[0])}")
    # --- END TRACE INSPECTION --- #

    predicted_answer_val = "---PREDICTED_ANSWER_NOT_SET---"
    
    if hasattr(pred, 'answer'):
        # print(f"[EXACT_MATCH_DEBUG] pred HAS 'answer' attribute.") # Less verbose
        predicted_answer_val = pred.answer
        # print(f"[EXACT_MATCH_DEBUG] pred.answer type: {type(predicted_answer_val)}") # Less verbose
        # print(f"[EXACT_MATCH_DEBUG] pred.answer value (repr): {repr(predicted_answer_val)}") # Less verbose
    elif hasattr(pred, 'get') and callable(pred.get):
        # print(f"[EXACT_MATCH_DEBUG] pred HAS 'get' method.") # Less verbose
        predicted_answer_val = pred.get('answer', "---GET_DEFAULT---")
        # print(f"[EXACT_MATCH_DEBUG] pred.get('answer') type: {type(predicted_answer_val)}") # Less verbose
        # print(f"[EXACT_MATCH_DEBUG] pred.get('answer') value (repr): {repr(predicted_answer_val)}") # Less verbose
    else:
        print(f"[EXACT_MATCH_DEBUG] pred has NO 'answer' attribute AND NO 'get' method.")
        print(f"--- exact_match END ---\\n")
        return 0.0 # Cannot extract answer

    if not isinstance(predicted_answer_val, str):
        # print(f"[EXACT_MATCH_DEBUG] predicted_answer_val was not a string, attempting str(). Original type: {type(predicted_answer_val)}") # Less verbose
        predicted_answer_val = str(predicted_answer_val)
        # print(f"[EXACT_MATCH_DEBUG] predicted_answer_val after str() (repr): {repr(predicted_answer_val)}") # Less verbose
    
    target_phone_number = "+36 30 850 84 98"
    # print(f"[EXACT_MATCH_DEBUG] target_phone_number (repr): {repr(target_phone_number)}") # Less verbose
    # print(f"[EXACT_MATCH_DEBUG] predicted_answer_val (repr): {repr(predicted_answer_val)}") # Less verbose

    # Correctness Score
    correctness_score = 0.0
    phone_pattern = r"\+36\s*30\s*850\s*84\s*98" # Ensure regex is correctly escaped
    # print(f"[EXACT_MATCH_DEBUG] Regex pattern: {phone_pattern}") # Less verbose
    if re.search(phone_pattern, predicted_answer_val):
        # print(f"[EXACT_MATCH_DEBUG] Regex search FOUND a match for correctness.") # Less verbose
        correctness_score = 1.0
    # else:
        # print(f"[EXACT_MATCH_DEBUG] Regex search DID NOT find a match for correctness.") # Less verbose

    # Efficiency Score
    num_researcher_tool_calls = 0
    researcher_trajectory_found = False

    if trace and isinstance(trace, list):
        for step_module, step_inputs, step_prediction in trace:
            # Check if step_module is an instance of the Researcher class defined in this file.
            # Need to be careful if the Swarm's Researcher instance is wrapped or proxied.
            # Assuming step_module directly refers to instances like self.tm, self.res, etc.
            if hasattr(step_module, '__class__') and step_module.__class__.__name__ == 'Researcher':
                researcher_trajectory_found = True
                print(f"[EXACT_MATCH_DEBUG] Found Researcher step in trace.")
                # print(f"[EXACT_MATCH_DEBUG]   Researcher step_prediction type: {type(step_prediction)}") # Less verbose
                # if hasattr(step_prediction, '__dict__'): print(f"[EXACT_MATCH_DEBUG]   Researcher step_prediction dir: {list(step_prediction.__dict__.keys())}") # Less verbose
                
                if hasattr(step_prediction, 'trajectory') and isinstance(step_prediction.trajectory, list):
                    researcher_trajectory = step_prediction.trajectory
                    print(f"[EXACT_MATCH_DEBUG]   Researcher trajectory found (length {len(researcher_trajectory)}).")
                    for item_idx, item in enumerate(researcher_trajectory):
                        # item is (thought, action_tuple, observation)
                        # action_tuple is (tool_name, tool_input_dict)
                        # print(f"[EXACT_MATCH_DEBUG]     Trajectory item {item_idx}: {str(item)[:100]}") # Less verbose
                        if len(item) >= 2 and isinstance(item[1], tuple) and len(item[1]) > 0:
                            action_name = item[1][0]
                            if isinstance(action_name, str) and action_name.lower() != 'finish':
                                num_researcher_tool_calls += 1
                                # print(f"[EXACT_MATCH_DEBUG]     Counted tool call: {action_name}") # Less verbose
                    print(f"[EXACT_MATCH_DEBUG]   Num researcher tool calls from trajectory: {num_researcher_tool_calls}")
                else:
                    print(f"[EXACT_MATCH_DEBUG]   Researcher step_prediction has no 'trajectory' list attribute.")
                break # Assuming one Researcher step per Swarm trace
    # else:
        # print(f"[EXACT_MATCH_DEBUG] Trace is None, empty, or not a list.") # Less verbose

    if not researcher_trajectory_found and trace is not None:
        print(f"[EXACT_MATCH_DEBUG] Researcher module not found in trace, or trace structure unexpected.")


    efficiency_score_component = 1.0 # Default to perfect efficiency if no tool calls or trace issue
    if num_researcher_tool_calls > 0:
        # Higher calls = lower efficiency.
        # If actual_calls > max_calls, it becomes negative, clamp at 0.
        efficiency_score_component = max(0.0, (max_expected_tool_calls - num_researcher_tool_calls) / float(max_expected_tool_calls))
    elif researcher_trajectory_found: # Found trajectory but 0 tool calls (e.g. ReAct finished early)
        efficiency_score_component = 1.0 # Perfect if no tools were needed by Researcher
    
    print(f"[EXACT_MATCH_DEBUG] Num Researcher Tool Calls: {num_researcher_tool_calls} (Max expected: {max_expected_tool_calls})")
    print(f"[EXACT_MATCH_DEBUG] Efficiency Score Component: {efficiency_score_component:.2f}")

    # Combine scores: 95% correctness, 5% efficiency
    final_score = (0.95 * correctness_score) + (0.05 * efficiency_score_component)
    print(f"[EXACT_MATCH_DEBUG] Correctness: {correctness_score:.2f}, Efficiency Comp: {efficiency_score_component:.2f} => Final Score: {final_score:.4f}")
    print(f"--- exact_match END ---\\n")
    return final_score

# === DSPy COMPILATION PROCESS ===

logger.info("Starting DSPy swarm compilation...")

try:
    # Initialize student program
    student_program = Swarm()
    
    # Ensure all predictors have the correct LM assigned
    logger.info("Configuring language models for all predictors...")
    for name, predictor in student_program.named_predictors():
        predictor.lm = dspy_llm
        logger.debug(f"Assigned LM to {name}: {predictor.lm}")
    
    # Prepare metric function with researcher parameters
    max_research_iterations = 3  # Default from Researcher configuration
    logger.info(f"Setting max expected tool calls for efficiency metric: {max_research_iterations}")
    
    metric_fn = functools.partial(exact_match, max_expected_tool_calls=max_research_iterations)
    
    # Configure optimizer for fast iteration
    optimizer = BootstrapFewShotWithRandomSearch(
        metric=metric_fn,
        max_bootstrapped_demos=1,  # Minimal for testing
        max_labeled_demos=1,
        num_candidate_programs=1,
        num_threads=1  # Avoid API rate limits
    )
    
    # Compile the program
    logger.info("Running DSPy compilation (this may take a few LLM calls)...")
    with error_handler("DSPy compilation"):
        compiled_swarm = optimizer.compile(student_program, trainset=dev_set)
    
    logger.info("DSPy compilation completed successfully")

except Exception as e:
    logger.error(f"DSPy compilation failed: {type(e).__name__}: {e}")
    # Fallback to uncompiled program
    logger.warning("Using uncompiled program as fallback")
    compiled_swarm = student_program

# === COMPILATION HISTORY ANALYSIS ===

try:
    if hasattr(dspy_llm, 'history') and dspy_llm.history:
        logger.info(f"DSPy compilation generated {len(dspy_llm.history)} LLM interactions")
        
        # Log detailed history only in debug mode
        for i, interaction in enumerate(dspy_llm.history):
            logger.debug(f"LLM Interaction {i+1}:")
            logger.debug(f"  Prompt: {interaction.get('prompt', 'N/A')[:200]}...")
            logger.debug(f"  Response: {interaction.get('response', 'N/A')[:200]}...")
            if 'kwargs' in interaction:
                logger.debug(f"  Args: {interaction['kwargs']}")
    else:
        logger.info("No LLM history recorded during compilation")
        
except Exception as e:
    logger.warning(f"Error accessing LLM history: {e}")

# === CREWAI INTEGRATION LAYER ===

from crewai import Agent as CrewAgent, Task, Crew, Process
from crewai.tools import BaseTool

logger.info("Initializing CrewAI integration layer...")

# Input schemas for each tool
class TmToolInputSchema(BaseModel):
    task: str

class ResToolInputSchema(BaseModel):
    task: str
    plan: str

class LibToolInputSchema(BaseModel):
    query: str

class WrtToolInputSchema(BaseModel):
    question: str
    web: str
    docs: str

class DspySwarmTmTool(BaseTool):
    """
    CrewAI tool wrapper for DSPy TaskManager module.
    Integration pattern: DSPy module is called in _run, output is returned as dict with explicit key.
    """
    name: str = "DSPy Task Manager"
    description: str = "Generates a step-by-step plan to address a given task or question. Input should be the task or question string."
    compiled_swarm_module: Callable
    args_schema: Type[BaseModel] = TmToolInputSchema
    result_as_answer: bool = True  # 2025 best practice

    def _run(self, task: str) -> dict:
        try:
            # Normalize input
            task_str = task
            if isinstance(task, dict):
                if "task" in task and isinstance(task["task"], str):
                    task_str = task["task"]
                elif "description" in task and isinstance(task["description"], str):
                    task_str = task["description"]
                else:
                    task_str = str(task)
            print(f"[DspySwarmTmTool._run] Normalized input task: {task_str}")
            plan_list = self.compiled_swarm_module(task=task_str).plan
            print(f"[DspySwarmTmTool._run] Output plan list: {plan_list}")
            plan_json_string = json.dumps(plan_list)
            print(f"[DspySwarmTmTool._run] Output plan JSON string: {plan_json_string}")
            return {"plan": plan_json_string}
        except Exception as e:
            print(f"[DspySwarmTmTool._run] Error: {type(e).__name__} - {e}")
            return {"plan": "[]", "error": str(e)}

class DspySwarmResTool(BaseTool):
    """
    CrewAI tool wrapper for DSPy Researcher module.
    Integration pattern: DSPy module is called in _run, output is returned as dict with explicit key.
    """
    name: str = "DSPy Researcher"
    description: str = "Gathers information based on a task and a plan. Input must be a dict with two keys: 'task' and 'plan'."
    compiled_swarm_module: Callable
    args_schema: Type[BaseModel] = ResToolInputSchema
    result_as_answer: bool = True  # 2025 best practice

    def _run(self, task: str, plan: str) -> dict:
        try:
            # Normalize input
            task_str = task
            plan_str = plan
            if isinstance(task, dict):
                if "task" in task and isinstance(task["task"], str):
                    task_str = task["task"]
                elif "description" in task and isinstance(task["description"], str):
                    task_str = task["description"]
                else:
                    task_str = str(task)
            if isinstance(plan, dict):
                if "plan" in plan and isinstance(plan["plan"], str):
                    plan_str = plan["plan"]
                else:
                    plan_str = str(plan)
            print(f"[DspySwarmResTool._run] Normalized input task: {task_str}, plan: {plan_str}")
            try:
                plan_list = json.loads(plan_str) if plan_str else []
            except Exception:
                plan_list = []
            result = self.compiled_swarm_module(task=task_str, plan=plan_list).research_summary
            print(f"[DspySwarmResTool._run] Output research_summary: {result}")
            return {"research_summary": result}
        except Exception as e:
            print(f"[DspySwarmResTool._run] Error: {type(e).__name__} - {e}")
            return {"research_summary": "", "error": str(e)}

class DspySwarmLibTool(BaseTool):
    """
    CrewAI tool wrapper for DSPy Librarian module.
    Integration pattern: DSPy module is called in _run, output is returned as dict with explicit key.
    """
    name: str = "DSPy Librarian"
    description: str = "Retrieves relevant passages from internal documents for a given query. Input should be the query string. Always pass the user question as a plain string for the 'query' argument. Do not wrap it in a dictionary or add extra fields."
    compiled_swarm_module: Callable
    args_schema: Type[BaseModel] = LibToolInputSchema
    result_as_answer: bool = True  # 2025 best practice

    def _run(self, query: str) -> dict:
        try:
            # Normalize input
            query_str = query
            if isinstance(query, dict):
                if "query" in query and isinstance(query["query"], str):
                    query_str = query["query"]
                elif "description" in query and isinstance(query["description"], str):
                    query_str = query["description"]
                else:
                    query_str = str(query)
            print(f"[DspySwarmLibTool._run] Normalized input query: {query_str}")
            if not vector_index:
                print("[DspySwarmLibTool._run] No vector index loaded. Returning empty passages list.")
                return {"library_passages": json.dumps([])}
            passages_list = self.compiled_swarm_module(query=query_str).passages
            print(f"[DspySwarmLibTool._run] Output passages list: {passages_list}")
            passages_json_string = json.dumps(passages_list)
            print(f"[DspySwarmLibTool._run] Output passages JSON string: {passages_json_string}")
            return {"library_passages": passages_json_string}
        except Exception as e:
            print(f"[DspySwarmLibTool._run] Error: {type(e).__name__} - {e}")
            return {"library_passages": json.dumps([]), "error": str(e)}

class DspySwarmWrtTool(BaseTool):
    """
    CrewAI tool wrapper for DSPy Writer module.
    Integration pattern: DSPy module is called in _run, output is returned as dict with explicit key.
    """
    name: str = "DSPy Writer"
    description: str = "Crafts a final answer. Input must be a dict with three keys: 'question', 'web', and 'docs'."
    compiled_swarm_module: Callable
    args_schema: Type[BaseModel] = WrtToolInputSchema
    result_as_answer: bool = True  # 2025 best practice

    def _run(self, question: str, web: str, docs: str) -> dict:
        try:
            # Normalize input
            question_str = question
            web_str = web
            docs_str = docs
            if isinstance(question, dict):
                if "question" in question and isinstance(question["question"], str):
                    question_str = question["question"]
                elif "description" in question and isinstance(question["description"], str):
                    question_str = question["description"]
                else:
                    question_str = str(question)
            if isinstance(web, dict):
                if "web" in web and isinstance(web["web"], str):
                    web_str = web["web"]
                else:
                    web_str = str(web)
            if isinstance(docs, dict):
                if "docs" in docs and isinstance(docs["docs"], str):
                    docs_str = docs["docs"]
                else:
                    docs_str = str(docs)
            print(f"[DspySwarmWrtTool._run] Normalized input question: {question_str}, web: {web_str}, docs: {docs_str}")
            try:
                docs_list = json.loads(docs_str) if docs_str else []
            except Exception:
                docs_list = []
            result = self.compiled_swarm_module(question=question_str, web=web_str, docs=docs_list).answer
            print(f"[DspySwarmWrtTool._run] Output answer: {result}")
            return {"answer": result}
        except Exception as e:
            print(f"[DspySwarmWrtTool._run] Error: {type(e).__name__} - {e}")
            return {"answer": "", "error": str(e)}

# Instantiate the custom tools with the compiled DSPy modules
dspy_tm_tool = DspySwarmTmTool(compiled_swarm_module=compiled_swarm.tm)
dspy_res_tool = DspySwarmResTool(compiled_swarm_module=compiled_swarm.res)
dspy_lib_tool = DspySwarmLibTool(compiled_swarm_module=compiled_swarm.lib)
dspy_wrt_tool = DspySwarmWrtTool(compiled_swarm_module=compiled_swarm.wrt)
# --- End Custom CrewAI Tools ---

# --- Callback function for debugging task outputs ---
def after_task_callback(task_output):
    print(f"\n--- TASK CALLBACK: {task_output.description.splitlines()[0]} ---") # Print first line of task desc
    print(f"  Task Output object type: {type(task_output)}")
    raw_output_value = "NOT_SET"
    raw_output_type = "NOT_SET"
    if hasattr(task_output, 'raw_output'):
        raw_output_value = task_output.raw_output
        raw_output_type = type(task_output.raw_output)
    print(f"  task_output.raw_output: {raw_output_value}")
    print(f"  task_output.raw_output type: {raw_output_type}")
    print(f"  task_output.description (interpolated):\n{task_output.description}")
    print(f"--- END TASK CALLBACK ---\n")
    if raw_output_value == "NOT_SET":
        print("[WARNING] Task output is NOT_SET. The LLM may not have used the tool as required. Full task_output object:")
        print(task_output)
    if isinstance(raw_output_value, dict):
        print(f"  Keys in raw_output: {list(raw_output_value.keys())}")
        print(f"  raw_output['research_summary']: {raw_output_value.get('research_summary', 'NOT PRESENT')}")
        print(f"  raw_output['library_passages']: {raw_output_value.get('library_passages', 'NOT PRESENT')}")
        # Professional fix: detect output_key of the calling Task
        output_key = None
        # Inspect the call stack to find the Task instance (self)
        for frame_info in inspect.stack():
            local_self = frame_info.frame.f_locals.get('self', None)
            if local_self is not None and hasattr(local_self, 'output_key'):
                output_key = getattr(local_self, 'output_key', None)
                if output_key:
                    print(f"  [DEBUG] Detected output_key in Task: '{output_key}'")
                    if output_key in raw_output_value:
                        print(f"  [DEBUG] Returning only value for output_key '{output_key}'")
                        return raw_output_value[output_key]
        # If no output_key found, fall through to returning the whole dict
    else:
        print(f"  raw_output is not a dict, value: {raw_output_value}")
    return raw_output_value # Default: return the whole output if not handled above
# --- End callback function ---

# --- Agent definitions with explicit tool-use instructions ---
tm_agent = CrewAgent(
    role="Task Manager",
    goal="Plan and coordinate sub‑tasks to answer sophisticated user questions by generating a step-by-step plan. When using a tool, always use the tool name exactly as shown in the available actions list, e.g., DSPy Task Manager. Do not invent or modify tool names. When using a tool, your Action must be exactly the tool name, e.g., Action: DSPy Task Manager.",
    backstory="Expert project manager with a knack for decomposing research problems and coordinating agent workflows. You must use the 'DSPy Task Manager' tool to generate the plan.",
    llm=crew_llm,
    tools=[dspy_tm_tool], # Assign the custom tool
    verbose=True,
)

research_agent = CrewAgent(
    role="Researcher",
    goal=(
        "Find accurate, up-to-date information using the 'DSPy Researcher' tool. "
        "When using a tool, your Action must be exactly the tool name, e.g., Action: DSPy Researcher. "
        "Do not add any extra words, instructions, or formatting. "
        "The Action Input must be a dictionary with 'task' and 'plan' keys. "
        "For example: Action: DSPy Researcher\nAction Input: {'task': '<user question>', 'plan': '<plan as JSON string>'}"
    ),
    backstory=(
        "An expert researcher skilled in using web search, website content analysis, and website scraping to find factual information. "
        "You must use the 'DSPy Researcher' tool, and when you do, your Action must be exactly 'DSPy Researcher' (no extra words). "
        "The Action Input must be: {'task': '<user question>', 'plan': '<plan as JSON string>'}. "
        "For example: Action: DSPy Researcher\nAction Input: {'task': 'Who is the lead in the company iddqd in hungary?', 'plan': '[...]'}"
    ),
    llm=crew_llm,
    tools=[dspy_res_tool] + [tool for tool in [tool_manager.serper_tool, tool_manager.website_search_tool, tool_manager.scrape_tool] if tool is not None],
    verbose=True,
)

librarian_agent = CrewAgent(
    role="Librarian",
    goal=(
        "Locate relevant internal documents and quote key passages using the 'DSPy Librarian' tool. "
        "When using a tool, your Action must be exactly the tool name, e.g., Action: DSPy Librarian. "
        "Do not add any extra words, instructions, or formatting. "
        "The Action Input must be a dictionary with a 'query' key and the user question as a string. "
        "For example: Action: DSPy Librarian\nAction Input: {'query': 'Who is the representative of the hungarian company iddqd?'}"
    ),
    backstory=(
        "A meticulous librarian with expertise in retrieving information from internal knowledge bases. "
        "You must use the 'DSPy Librarian' tool, and when you do, your Action must be exactly 'DSPy Librarian' (no extra words). "
        "The Action Input must be: {'query': '<user question>'}. "
        "For example: Action: DSPy Librarian\nAction Input: {'query': 'Who is the representative of the hungarian company iddqd?'}"
    ),
    llm=crew_llm,
    tools=[dspy_lib_tool],
    allow_delegation=False,
    verbose=True,
)

writer_agent = CrewAgent(
    role="Writer",
    goal=(
        "Craft a polished, well-cited final answer using the 'DSPy Writer' tool. "
        "When using a tool, your Action must be exactly the tool name, e.g., Action: DSPy Writer. "
        "Do not add any extra words, instructions, or formatting. "
        "The Action Input must be a dictionary with 'question', 'web', and 'docs' keys. "
        "For example: Action: DSPy Writer\nAction Input: {'question': '<user question>', 'web': '<research summary>', 'docs': '<library passages>'}"
    ),
    backstory=(
        "A skilled technical writer capable of synthesizing information from various sources into a coherent and well-structured answer. "
        "You must use the 'DSPy Writer' tool, and when you do, your Action must be exactly 'DSPy Writer' (no extra words). "
        "The Action Input must be: {'question': '<user question>', 'web': '<research summary>', 'docs': '<library passages>'}. "
        "For example: Action: DSPy Writer\nAction Input: {'question': 'Who is the lead in the company iddqd in hungary?', 'web': '<research summary>', 'docs': '<library passages>'}"
    ),
    llm=crew_llm,
    tools=[dspy_wrt_tool],
    verbose=True,
)
# --- End agent definitions ---

plan_task = Task(
    description="Generate an execution plan for the user's question: '{question}'.",
    expected_output="A JSON string representing a list of steps, outlining the sub-tasks to perform. This plan will be generated by the 'DSPy Task Manager' tool.",
    agent=tm_agent,
    callback=after_task_callback, # Add callback here
    output_key="plan" # <-- Output key matches tool output dict
)

research_task = Task(
    description=(
        "The Task Manager (previous task) has generated a plan as a JSON string. "
        "Your objective is to execute this research plan to answer the user's original question: '{question}'. "
        "To do this, you MUST use the 'DSPy Researcher' tool. "
        "The 'DSPy Researcher' tool expects a single JSON string as input. This JSON string must represent a dictionary "
        "containing two keys: 'task' and 'plan'. "
        "For the 'task' key, use the original user question: '{question}'. "
        "For the 'plan' key, use the JSON string output from the previous 'plan_task'. "
        "Construct this input JSON string and pass it to the 'DSPy Researcher' tool."
    ),
    expected_output="A dictionary containing a 'research_summary' key with the consolidated information as its value.",
    agent=research_agent,
    context=[plan_task],
    callback=after_task_callback, # Add callback here
    output_key="research_summary" # <-- Output key matches tool output dict
)

library_task = Task(
    description="Check internal knowledge base for relevant passages based on the user's question: '{question}'.",
    expected_output="A dictionary containing a 'library_passages' key with a JSON string of relevant passages as its value.",
    agent=librarian_agent,
    context=[plan_task],
    callback=after_task_callback,
    output_key="library_passages", # <-- Output key matches tool output dict
    tool_input={
        "query": "{question}"
    }
)

write_task = Task(
    description=(
        "Write the final answer to the original user question. "
        "You must synthesize all gathered information into a comprehensive, well-cited response. "
        "Use the provided research summary and library passages as supporting material."
    ),
    expected_output="A comprehensive, well-cited final answer to the user's question, synthesizing all gathered information.",
    agent=writer_agent,
    context=[research_task, library_task],
    callback=after_task_callback, # Add callback here
    output_key="answer", # <-- Output key matches tool output dict
    tool_input={
        "question": "{question}",
        "web": "{research_summary}",
        "docs": "{library_passages}"
    }
)

# === CREW CONFIGURATION ===

crew = Crew(
    agents=[tm_agent, research_agent, librarian_agent, writer_agent],
    tasks=[plan_task, research_task, library_task, write_task],
    process=Process.sequential,
    verbose=True,
    memory=False,  # Disable crew memory for deterministic results
    max_retry_limit=2,  # Limit retries to prevent infinite loops
)

logger.info("CrewAI crew configured with 4 agents and 4 sequential tasks")

# === INTERACTIVE EXECUTION ===

def main():
    """Main execution function with proper error handling"""
    try:
        logger.info("=" * 60)
        logger.info("DSPy + CrewAI Multi-Agent Pipeline Ready")
        logger.info("=" * 60)
        
        # Debug task context relationships
        logger.debug("Task context configuration:")
        logger.debug(f"  plan_task: No context (root task)")
        logger.debug(f"  research_task: Context from plan_task")
        logger.debug(f"  library_task: Context from plan_task")
        logger.debug(f"  write_task: Context from research_task + library_task")
        
        while True:
            try:
                user_question = input("\nEnter your question (or 'quit' to exit): ")
                
                if user_question.lower() in ['quit', 'exit', 'q']:
                    logger.info("Exiting pipeline")
                    break
                
                if not user_question.strip():
                    print("Please enter a valid question.")
                    continue
                
                logger.info(f"Processing question: {user_question}")
                
                # Execute the crew workflow
                with error_handler("CrewAI execution"):
                    result = crew.kickoff(inputs={"question": user_question})
                
                # Display results
                print("\n" + "=" * 60)
                print("FINAL ANSWER")
                print("=" * 60)
                print(result)
                print("=" * 60)
                
                logger.info("Question processed successfully")
                
            except KeyboardInterrupt:
                logger.info("Interrupted by user")
                break
            except Exception as e:
                logger.error(f"Error processing question: {type(e).__name__}: {e}")
                print(f"An error occurred: {e}")
                
    except Exception as e:
        logger.error(f"Fatal error in main execution: {type(e).__name__}: {e}")
        print(f"Fatal error: {e}")

if __name__ == "__main__":
    main()
