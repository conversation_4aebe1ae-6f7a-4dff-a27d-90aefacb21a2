# Implementation Instructions: Advanced Coordination Flow Enhancement
**Date:** May 24, 2025  
**Task:** Enhance AdvancedCoordinationFlow with all features from MainWorkflowFlow  
**Priority:** Critical - Preserve working MainWorkflowFlow, enhance AdvancedCoordinationFlow only

## Overview

This document provides step-by-step instructions to enhance the `AdvancedCoordinationFlow` with all missing features from the working `MainWorkflowFlow`, while adding CLI parameter support for `--enhanced-flow` and configuration-based activation.

**Critical Requirements:**
- ❌ DO NOT modify MainWorkflowFlow or related working components
- ✅ Only enhance AdvancedCoordinationFlow and related files
- ✅ Implement CLI parameter `--enhanced-flow` support
- ✅ Add config.yaml `ENHANCED_FLOW=True` support
- ✅ Base all enhancements on working code patterns

---

## Research Validation: Framework Best Practices ✅

**✅ VALIDATED**: Our implementation plan aligns with current CrewAI and DSPy best practices based on comprehensive research conducted on May 24, 2025.

### Key Research Findings (May 2025):

#### CrewAI Flows Best Practices:
- **Event-Driven Architecture**: Flows provide precise control over execution paths through @start, @listen, @router decorators
- **Structured State Management**: Pydantic models for type safety and validation are the recommended approach
- **Router Patterns**: Conditional branching with proper return values for event routing is well-established
- **Persistence**: SQLite backend with @persist decorator for state recovery across executions
- **CLI Integration**: `crewai run` command is the preferred execution method
- **Crew Integration**: Seamless integration of Crews as steps within Flows is fully supported

#### DSPy Integration Patterns:
- **Declarative Programming**: DSPy signatures define clear input-output relationships
- **Modular Design**: Reusable and composable LLM pipelines through modules
- **Automated Optimization**: MIPROv2 optimizer for prompt engineering and performance improvements
- **Chain of Thought**: Built-in reasoning patterns with natural integration
- **MLflow Tracing**: Standard practice for optimization tracking and experiment management

#### Advanced Flow Patterns:
- **Naming Conventions**: Avoid method name conflicts with event names (prevents infinite loops)
- **Error Handling**: Implement try/catch blocks for robust LLM interactions
- **Async Support**: Full async/await pattern support for concurrent operations
- **Tool Integration**: Standard patterns for external tool integration within flows

#### Framework Compatibility:
- **DSPy-CrewAI Integration**: Tool-based approaches are recommended for combining frameworks
- **State Management**: Both frameworks support structured state with Pydantic models
- **Optimization**: Compatible optimization patterns between frameworks

**Implementation Status**: All proposed patterns are confirmed compatible with CrewAI 2025 best practices and DSPy integration standards.

### Critical Implementation Warnings:

⚠️ **AVOID INFINITE LOOPS**: Never name flow methods the same as event names they listen to
⚠️ **ERROR HANDLING**: Wrap all LLM calls in try/catch blocks for robustness  
⚠️ **STATE TYPING**: Use Pydantic models for all flow state to prevent runtime errors
⚠️ **ASYNC CONSISTENCY**: Maintain async/await patterns throughout the flow chain
⚠️ **RESOURCE CLEANUP**: Implement proper resource management for external tools

---

## Task 1: Add CLI Parameter and Configuration Support ✅ COMPLETED

**Status**: ✅ **IMPLEMENTATION COMPLETED**

### 1.1 Update main.py for Enhanced Flow Selection ✅ IMPLEMENTED

**File:** `src/main.py`

**✅ COMPLETED SUCCESSFULLY:**
- CLI parameter `--enhanced-flow` working correctly
- Configuration file support (`ENHANCED_FLOW: true`) implemented
- Both main workflow and enhanced workflow routing functional
- Main workflow properly calls `MultiAgentSystem().answer_question()`
- Enhanced workflow initializes `AdvancedCoordinationFlow` correctly

**Verification Results:**
```bash
# Main workflow (default) - ✅ WORKING
python src/main.py "What is AI?"

# Enhanced workflow via CLI - ✅ WORKING  
python src/main.py --enhanced-flow "What is AI?"

# Enhanced workflow via config - ✅ WORKING
# Set ENHANCED_FLOW: true in config.yaml
```

### 1.2 Update config.yaml ✅ IMPLEMENTED

**File:** `config.yaml`

**✅ COMPLETED SUCCESSFULLY:**
- Enhanced flow configuration section added
- DSPy integration settings implemented
- Alert thresholds and monitoring configuration added
- All configuration options properly structured

---

## Task 2: Integrate TaskManagerFlow into AdvancedCoordinationFlow

### 2.1 Import and Initialize TaskManagerFlow

**File:** `src/orchestration/flows/advanced_coordination_flow.py`

**Add these imports at the top:**

```python
from src.orchestration.flows.task_manager_flow import TaskManagerFlow
from src.orchestration.flows.writer_flow import WriterFlow
from src.infrastructure.config.settings import get_llm_for_agent, load_configuration
```

**Update the `__init__` method:**

```python
def __init__(self, config: Dict[str, Any]):
    # Initialize attributes first
    self._flow_id = str(uuid.uuid4())
    self._status = FlowStatus.PENDING
    
    # Store configuration
    self.config = config
    
    # Initialize specialized flows - COPY FROM MAIN WORKFLOW
    self._task_manager_flow = TaskManagerFlow()
    self._writer_flow = WriterFlow()
    
    # Initialize enhanced agents with proper tools
    self.agents = self._initialize_agents()
    
    # Initialize monitoring
    self.monitor = FlowMonitor(config.get('alert_thresholds', {}))
    
    # Initialize DSPy with proper configuration
    self._setup_dspy(config.get('dspy', {}))
    
    # Call super().__init__() after setting attributes
    super().__init__()

def _setup_dspy(self, dspy_config: Dict[str, Any]):
    """Setup DSPy with proper LLM configuration."""
    try:
        import dspy
        
        # Configure DSPy LLM using the same settings as agents
        model_name = dspy_config.get('model', 'openai/gpt-4o-mini')
        
        # Use settings.py for consistent LLM configuration
        llm_instance = get_llm_for_agent("complexity_analyzer")
        
        # Setup DSPy with the configured LLM
        dspy.configure(lm=llm_instance)
        
        # Initialize complexity analyzer
        self.complexity_analyzer = dspy.ChainOfThought(ComplexityAnalyzer)
        
        print(f"✅ DSPy configured with model: {model_name}")
        
    except Exception as e:
        print(f"⚠️ DSPy setup failed: {str(e)}, using fallback")
        self.complexity_analyzer = None
```

### 2.2 Add Task Planning Phase

**Add this method after `analyze_request`:**

```python
@listen(analyze_request)
async def task_planning_phase(self):
    """Enhanced task planning using TaskManagerFlow (copied from MainWorkflow)."""
    print(f"\n📋 PHASE: Enhanced Task Planning (Advanced Flow)")
    print("-" * 50)
    
    self.state.advance_phase(TaskPhase.PLANNING)
    
    try:
        # Run TaskManager flow to create execution plan
        planning_result = await self._task_manager_flow.kickoff_async(inputs={
            "query": self.state.original_query,
            "context": self.state.context
        })
        
        # Update state with plan
        if hasattr(planning_result, 'execution_plan'):
            self.state.execution_plan = planning_result.execution_plan
        elif hasattr(self._task_manager_flow.state, 'execution_plan'):
            self.state.execution_plan = self._task_manager_flow.state.execution_plan
        
        print(f"✅ Enhanced execution plan created with {len(self.state.execution_plan)} steps")
        return {"planning_complete": True, "plan": self.state.execution_plan}
        
    except Exception as e:
        error_msg = f"Enhanced task planning failed: {str(e)}"
        self.state.add_error(error_msg, "TaskManager")
        print(f"❌ {error_msg}")
        return {"planning_complete": False, "error": error_msg}
```

### 2.3 Update Router to Consider Task Plan

**Replace the `route_by_complexity` method:**

```python
@router(task_planning_phase)
def route_by_complexity(self):
    """Dynamic routing based on task complexity, requirements, and execution plan."""
    
    # Consider execution plan size
    plan_size = len(self.state.execution_plan) if self.state.execution_plan else 0
    
    # Enhanced routing logic
    if (self.state.complexity_score >= 8.0 or plan_size >= 6):
        print("🔄 Routing to complex workflow (multi-stage + hierarchical)")
        return "complex_workflow"
    elif (self.state.parallel_eligible and 
          self.state.complexity_score >= 5.0 and 
          plan_size >= 3):
        print("⚡ Routing to parallel workflow (load balanced + planned)")
        return "parallel_workflow"
    else:
        print("📋 Routing to standard workflow (sequential + planned)")
        return "standard_workflow"
```

---

## Task 3: Integrate Enhanced Specialist Flows

### 3.1 Update Agent Initialization

**Replace the `_initialize_agents` method:**

```python
def _initialize_agents(self) -> Dict[str, Any]:
    """Initialize enhanced specialist agents with all Quick Wins capabilities."""
    try:
        # Import enhanced specialists
        from src.agents.specialists.search_specialist import EnhancedSearchSpecialist
        from src.agents.specialists.knowledge_specialist import MathEnabledKnowledgeSpecialist
        from src.agents.specialists.react_search_specialist import ReActSearchSpecialist
        from src.agents.specialists.react_knowledge_specialist import ReActKnowledgeSpecialist
        
        # Import enhanced flows
        from src.orchestration.flows.specialist_flows import (
            EnhancedResearcherFlow, 
            EnhancedLibrarianFlow, 
            EnhancedDataProcessorFlow
        )
        
        return {
            # Individual enhanced agents
            'search_specialist': EnhancedSearchSpecialist(),
            'knowledge_specialist': MathEnabledKnowledgeSpecialist(),
            'react_search': ReActSearchSpecialist(),
            'react_knowledge': ReActKnowledgeSpecialist(),
            
            # Enhanced specialist flows (like MainWorkflow)
            'researcher_flow': EnhancedResearcherFlow(),
            'librarian_flow': EnhancedLibrarianFlow(),
            'data_processor_flow': EnhancedDataProcessorFlow()
        }
        
    except ImportError as e:
        print(f"⚠️ Some enhanced agents not available: {str(e)}")
        # Fallback to basic agents
        return {
            'search_specialist': self._create_fallback_agent("search"),
            'knowledge_specialist': self._create_fallback_agent("knowledge"),
            'react_search': self._create_fallback_agent("react_search"),
            'react_knowledge': self._create_fallback_agent("react_knowledge")
        }

def _create_fallback_agent(self, agent_type: str):
    """Create fallback agent when enhanced agents are not available."""
    from crewai import Agent
    from crewai_tools import SerperDevTool
    
    if agent_type == "search":
        return Agent(
            role="Search Specialist",
            goal="Find relevant information",
            backstory="Expert at searching for information",
            tools=[SerperDevTool()],
            llm=get_llm_for_agent("search_specialist")
        )
    elif agent_type == "knowledge":
        return Agent(
            role="Knowledge Specialist", 
            goal="Analyze and synthesize information",
            backstory="Expert at knowledge analysis",
            llm=get_llm_for_agent("knowledge_specialist")
        )
    # Add other fallback types as needed
    return None
```

### 3.2 Add Enhanced Specialist Execution

**Add this method after the routing methods:**

```python
@listen(or_(execute_complex_workflow, execute_parallel_workflow, execute_standard_workflow))
async def enhanced_specialist_execution_phase(self):
    """Execute enhanced specialist flows in parallel (copied from MainWorkflow)."""
    print(f"\n🤖 PHASE: Enhanced Specialist Execution (Advanced Flow)")
    print("-" * 60)
    print("🔧 Activating Enhanced Capabilities:")
    print("   🔍 Enhanced Researcher: ColBERTv2 + ReAct Search")
    print("   📚 Enhanced Librarian: Math Computation + ReAct Knowledge")
    print("   📊 Enhanced Data Processor: Statistical Analysis + ReAct Processing")
    print("-" * 60)
    
    self.state.advance_phase(TaskPhase.RESEARCH)
    
    # Prepare context for all specialists
    specialist_context = {
        "query": self.state.original_query,
        "execution_plan": self.state.execution_plan,
        "workflow_id": self.state.workflow_id,
        "enhanced_mode": True,
        "quick_wins_active": True,
        "complexity_analysis": {
            "score": self.state.complexity_score,
            "capabilities": self.state.required_capabilities,
            "parallel_eligible": self.state.parallel_eligible
        }
    }
    
    try:
        print("🔄 Starting parallel execution of enhanced specialists...")
        
        # Execute enhanced specialist flows in parallel (like MainWorkflow)
        specialist_tasks = [
            self.agents['researcher_flow'].kickoff_async(inputs=specialist_context),
            self.agents['librarian_flow'].kickoff_async(inputs=specialist_context),
            self.agents['data_processor_flow'].kickoff_async(inputs=specialist_context)
        ]
        
        # Wait for all specialists to complete
        results = await asyncio.gather(*specialist_tasks, return_exceptions=True)
        
        # Process results exactly like MainWorkflow
        researcher_result, librarian_result, processor_result = results
        
        # Handle Enhanced Researcher result
        await self._process_specialist_result(
            researcher_result, "EnhancedResearcher", 
            self.state.add_research_result, "research_results"
        )
        
        # Handle Enhanced Librarian result  
        await self._process_specialist_result(
            librarian_result, "EnhancedLibrarian",
            self.state.add_library_result, "library_results"
        )
        
        # Handle Enhanced Data Processor result
        await self._process_specialist_result(
            processor_result, "EnhancedDataProcessor",
            self.state.add_analysis_result, "analysis_results"
        )
        
        specialists_success = (
            len(self.state.research_results) > 0 or
            len(self.state.library_results) > 0 or
            len(self.state.analysis_results) > 0
        )
        
        print(f"\n🎯 Enhanced Specialists Summary:")
        print(f"   📊 Research results: {len(self.state.research_results)}")
        print(f"   📚 Library results: {len(self.state.library_results)}")
        print(f"   📈 Analysis results: {len(self.state.analysis_results)}")
        print(f"   ✅ Overall success: {specialists_success}")
        
        return {
            "specialists_complete": True,
            "research_count": len(self.state.research_results),
            "library_count": len(self.state.library_results),
            "analysis_count": len(self.state.analysis_results),
            "success": specialists_success,
            "enhanced_features_used": True
        }
        
    except Exception as e:
        error_msg = f"Enhanced specialist execution failed: {str(e)}"
        self.state.add_error(error_msg, "EnhancedSpecialistOrchestrator")
        print(f"❌ {error_msg}")
        return {"specialists_complete": False, "error": error_msg}

async def _process_specialist_result(self, result, specialist_name: str, add_result_func, result_type: str):
    """Process individual specialist result (copied from MainWorkflow)."""
    if isinstance(result, Exception):
        self.state.add_error(f"{specialist_name} failed: {str(result)}", specialist_name)
        print(f"❌ {specialist_name} failed: {str(result)}")
    elif result and isinstance(result, dict):
        if result.get('success'):
            flow_results = result.get('results', [])
            for result_dict in flow_results:
                # Convert dict to appropriate result object
                if result_type == "research_results":
                    from src.core.models.state_models import ResearchResult
                    research_result = ResearchResult(**result_dict)
                    add_result_func(research_result)
                elif result_type == "library_results":
                    from src.core.models.state_models import LibraryResult
                    library_result = LibraryResult(**result_dict)
                    add_result_func(library_result)
                elif result_type == "analysis_results":
                    from src.core.models.state_models import AnalysisResult
                    analysis_result = AnalysisResult(**result_dict)
                    add_result_func(analysis_result)
            
            # Show enhancement statistics
            enhanced_agents = result.get('enhanced_agents_used', [])
            reliability_stats = result.get('reliability_stats', {})
            tools_performance = result.get('tools_performance', {})
            
            print(f"✅ {specialist_name} completed: {len(flow_results)} results")
            print(f"   🔧 Enhanced agents used: {len(enhanced_agents)} ({', '.join(enhanced_agents)})")
            print(f"   📈 Tools performance: {tools_performance}")
            if reliability_stats:
                confidence_key = 'average_confidence' if 'confidence' in str(reliability_stats) else 'average_relevance'
                print(f"   🛡️ Reliability stats: {reliability_stats.get(confidence_key, 'N/A')}")
        else:
            print(f"⚠️ {specialist_name} completed but returned no results")
    else:
        print(f"⚠️ {specialist_name} returned unexpected format: {type(result)}")
```

---

## Task 4: Integrate WriterFlow for Final Synthesis

### 4.1 Add Enhanced Synthesis Phase

**Add this method after the specialist execution phase:**

```python
@listen(enhanced_specialist_execution_phase)
async def enhanced_synthesis_phase(self):
    """Enhanced synthesis using WriterFlow (copied from MainWorkflow)."""
    print(f"\n📝 PHASE: Enhanced Result Synthesis (Advanced Flow)")
    print("-" * 50)
    
    self.state.advance_phase(TaskPhase.SYNTHESIS)
    
    # Collect all enhanced results for synthesis
    synthesis_context = {
        "query": self.state.original_query,
        "research_results": [r.dict() for r in self.state.research_results],
        "library_results": [l.dict() for l in self.state.library_results], 
        "analysis_results": [a.dict() for a in self.state.analysis_results],
        "workflow_id": self.state.workflow_id,
        "enhanced_mode": True,
        "enhancement_summary": {
            "total_enhanced_agents": self._count_enhanced_agents(),
            "reliability_features_used": True,
            "professional_tools_used": True,
            "colbert_search_used": True,
            "math_computation_used": True,
            "react_reasoning_used": True,
            "complexity_analysis": {
                "score": self.state.complexity_score,
                "capabilities": self.state.required_capabilities,
                "routing_decision": self.state.current_stage
            }
        }
    }
    
    try:
        print("🔄 Starting enhanced result synthesis...")
        
        # Run WriterFlow to synthesize all results (like MainWorkflow)
        synthesis_result = await self._writer_flow.kickoff_async(inputs=synthesis_context)
        
        # Update final answer
        if hasattr(synthesis_result, 'final_answer'):
            self.state.final_answer = synthesis_result.final_answer
        elif hasattr(self._writer_flow.state, 'final_output'):
            self.state.final_answer = self._writer_flow.state.final_output
        else:
            self.state.final_answer = str(synthesis_result)
        
        print("✅ Enhanced synthesis completed")
        return {"synthesis_complete": True, "enhanced_features_summarized": True}
        
    except Exception as e:
        error_msg = f"Enhanced synthesis failed: {str(e)}"
        self.state.add_error(error_msg, "EnhancedSynthesizer")
        print(f"❌ {error_msg}")
        
        # Fallback synthesis
        self.state.final_answer = self._create_fallback_synthesis()
        return {"synthesis_complete": False, "error": error_msg, "fallback_used": True}

def _create_fallback_synthesis(self) -> str:
    """Create fallback synthesis when WriterFlow fails."""
    results = []
    
    if self.state.research_results:
        results.append(f"Research findings ({len(self.state.research_results)} results)")
    if self.state.library_results:
        results.append(f"Library knowledge ({len(self.state.library_results)} results)")
    if self.state.analysis_results:
        results.append(f"Data analysis ({len(self.state.analysis_results)} results)")
    
    if not results:
        return f"Unable to find comprehensive information about: {self.state.original_query}"
    
    return f"Based on {', '.join(results)}, here is what was found regarding: {self.state.original_query}"

def _count_enhanced_agents(self) -> int:
    """Count total enhanced agents used across all flows."""
    total_enhanced = 0
    
    # Count from specialist flows if available
    for flow_name in ['researcher_flow', 'librarian_flow', 'data_processor_flow']:
        if flow_name in self.agents and hasattr(self.agents[flow_name], 'state'):
            if hasattr(self.agents[flow_name].state, 'enhanced_agents_used'):
                total_enhanced += len(self.agents[flow_name].state.enhanced_agents_used)
    
    return total_enhanced
```

---

## Task 5: Fix State Management and Phase Tracking

### 5.1 Update State Initialization

**Replace the WorkflowState initialization in `analyze_request`:**

```python
@start()
async def analyze_request(self):
    """Enhanced request analysis with proper state management."""
    print(f"🚀 Starting Advanced Coordination Flow Analysis")
    print("=" * 70)
    print("🎯 Advanced Features Active:")
    print("   ✅ Dynamic Complexity Analysis")
    print("   ✅ Intelligent Workflow Routing")
    print("   ✅ Parallel Processing with Load Balancing")  
    print("   ✅ Real-time Monitoring and Error Recovery")
    print("   ✅ Enhanced Specialist Integration")
    print("   ✅ DSPy Chain-of-Thought Reasoning")
    print("=" * 70)
    
    # Initialize workflow state properly (like MainWorkflow)
    if not hasattr(self.state, 'original_query') or not self.state.original_query:
        self.state.original_query = "What are the latest developments in renewable energy technology?"
    
    query = self.state.original_query
    self.state.processed_query = query
    self.state.workflow_id = self._flow_id
    self.state.current_phase = TaskPhase.INITIALIZATION
    
    print(f"\n🎯 Query: {query}")
    print(f"🆔 Workflow ID: {self._flow_id}")
    print(f"🔧 Enhancement Level: Advanced Coordination Flow")
    
    # Start monitoring
    asyncio.create_task(self.monitor.monitor_workflow(
        self.state.session_id,
        lambda: self.state
    ))
    
    try:
        # Complexity analysis using DSPy patterns
        if self.complexity_analyzer:
            analysis_result = self.complexity_analyzer(query=query)
            
            # Parse capabilities from comma-separated string
            capabilities = [cap.strip() for cap in analysis_result.capabilities.split(',') if cap.strip()]
            
            # Create structured analysis
            analysis = ComplexityAnalysis(
                complexity_score=float(analysis_result.complexity_score),
                capabilities=capabilities,
                time_estimate=float(analysis_result.time_estimate),
                can_parallelize=str(analysis_result.can_parallelize).lower() == 'true',
                reasoning=analysis_result.reasoning
            )
        else:
            # Fallback analysis when DSPy unavailable
            analysis = ComplexityAnalysis(
                complexity_score=5.0,
                capabilities=["search", "analysis"],
                time_estimate=60.0,
                can_parallelize=True,
                reasoning="Fallback analysis - DSPy unavailable"
            )
        
        # Update state with analysis results
        self.state.complexity_score = analysis.complexity_score
        self.state.required_capabilities = analysis.capabilities
        self.state.estimated_time = analysis.time_estimate
        self.state.parallel_eligible = analysis.can_parallelize
        self.state.current_stage = "complexity_analysis_complete"
        
        print(f"📊 Analysis complete:")
        print(f"   Complexity: {analysis.complexity_score}/10")
        print(f"   Capabilities: {', '.join(analysis.capabilities)}")
        print(f"   Parallel eligible: {analysis.can_parallelize}")
        print(f"   Reasoning: {analysis.reasoning}")
        
        return analysis
        
    except Exception as e:
        error_msg = f"Complexity analysis failed: {str(e)}"
        self.state.add_error(error_msg, "ComplexityAnalyzer")
        print(f"❌ {error_msg}")
        
        # Fallback to standard workflow
        fallback_analysis = ComplexityAnalysis(
            complexity_score=5.0,
            capabilities=["search", "analysis"],
            time_estimate=60.0,
            can_parallelize=False,
            reasoning="Fallback due to analysis error"
        )
        return fallback_analysis
```

### 5.2 Add Proper Phase Management Properties

**Add these properties to the class:**

```python
@property
def flow_id(self) -> str:
    """Get the flow ID."""
    return self._flow_id

@property  
def status(self) -> FlowStatus:
    """Get the current flow status."""
    return self._status
```

---

## Task 6: Add Training Data Collection and DSPy Integration

### 6.1 Add Training Data Collection

**Add this method to the class:**

```python
async def _collect_training_data(self):
    """Collect training data for DSPy optimization (based on working patterns)."""
    if not self.config.get('dspy', {}).get('training_enabled', False):
        return
    
    try:
        # Collect training data from successful executions
        training_example = {
            'query': self.state.original_query,
            'complexity_score': self.state.complexity_score,
            'capabilities': self.state.required_capabilities,
            'parallel_eligible': self.state.parallel_eligible,
            'workflow_path': self.state.current_stage,
            'success_metrics': {
                'total_results': (len(self.state.research_results) + 
                                len(self.state.library_results) + 
                                len(self.state.analysis_results)),
                'execution_time': getattr(self.state, 'execution_time', 0),
                'error_count': len(self.state.errors_encountered)
            }
        }
        
        # Save training data
        await self._save_training_example(training_example)
        
    except Exception as e:
        print(f"⚠️ Training data collection failed: {str(e)}")

async def _save_training_example(self, example: Dict[str, Any]):
    """Save training example to file."""
    import json
    from pathlib import Path
    
    training_dir = Path("training_data")
    training_dir.mkdir(exist_ok=True)
    
    filename = f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    filepath = training_dir / filename
    
    with open(filepath, 'w') as f:
        json.dump(example, f, indent=2, default=str)
    
    print(f"📚 Training data saved: {filepath}")
```

### 6.2 Add Training Data Collection to Finalization

**Update the `finalize_workflow` method:**

```python
@listen(enhanced_synthesis_phase)
async def finalize_workflow(self):
    """Final processing with training data collection and metrics."""
    print("✅ Finalizing advanced workflow and collecting metrics")
    self.state.current_stage = "finalization"
    
    # Collect training data for DSPy optimization
    await self._collect_training_data()
    
    # Collect performance metrics
    metrics = await self._collect_metrics()
    self.state.performance_metrics = metrics
    
    # Generate workflow summary
    summary = self.state.get_workflow_summary()
    
    print(f"🎯 Advanced Workflow completed successfully:")
    print(f"   Session: {summary['session_id'][:8]}...")
    print(f"   Tasks: {summary['successful_tasks']}/{summary['total_tasks']}")
    print(f"   Errors: {summary['total_errors']}")
    print(f"   Execution time: {summary.get('execution_time', 'N/A')}s")
    print(f"   Enhancement features: Advanced Coordination + Quick Wins")
    
    # Set final result
    self.state.final_result = self.state.final_answer or "Workflow completed"
    self.state.workflow_completed = True
    
    return summary
```

---

## Task 7: Error Handling and Recovery

### 7.1 Add Comprehensive Error Handling

**Add these methods for error handling:**

```python
async def _execute_with_enhanced_reliability(self, agent, query: str, agent_name: str) -> str:
    """Execute agent with enhanced reliability patterns (based on working code)."""
    max_retries = 3
    backoff_delay = 1.0
    
    for attempt in range(max_retries):
        try:
            # Execute the agent with different interfaces
            if hasattr(agent, 'process_query'):
                result = await agent.process_query(query)
            elif hasattr(agent, 'execute'):
                result = await agent.execute(query)
            elif hasattr(agent, 'kickoff_async'):
                result = await agent.kickoff_async(inputs={"query": query})
            elif callable(agent):
                result = agent(query)
            else:
                raise ValueError(f"Agent {agent_name} has no callable interface")
            
            if result:
                return str(result)
            else:
                raise ValueError("Agent returned empty result")
                
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"⚠️ {agent_name} attempt {attempt + 1} failed: {str(e)}, retrying in {backoff_delay}s...")
                await asyncio.sleep(backoff_delay)
                backoff_delay *= 2  # Exponential backoff
            else:
                print(f"❌ {agent_name} failed after {max_retries} attempts: {str(e)}")
                self.state.add_error(f"{agent_name} execution failed: {str(e)}", agent_name)
                raise e

async def _graceful_degradation(self, failed_component: str, fallback_data: Dict[str, Any]) -> str:
    """Implement graceful degradation when components fail."""
    print(f"🔄 Implementing graceful degradation for {failed_component}")
    
    # Collect any available results
    available_results = []
    
    if self.state.research_results:
        available_results.append(f"Research: {len(self.state.research_results)} results")
    if self.state.library_results:
        available_results.append(f"Knowledge: {len(self.state.library_results)} results")
    if self.state.analysis_results:
        available_results.append(f"Analysis: {len(self.state.analysis_results)} results")
    
    if available_results:
        return f"Partial results available from {', '.join(available_results)} despite {failed_component} failure."
    else:
        return f"Unable to process query due to {failed_component} failure. Please try again."
```

---

## Task 8: Update Import Statements and Dependencies

### 8.1 Add Missing Imports

**Add these imports at the top of the file:**

```python
import time
import uuid
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# CrewAI Flows 2025
from crewai.flow.flow import Flow, listen, start, router, and_, or_
from crewai import Agent, Crew, Task, Process
from pydantic import BaseModel, Field

# DSPy for Chain of Thought reasoning
import dspy

# Internal imports
from src.core.models.state_models import (
    WorkflowState, TaskPhase, TaskResult, 
    ResearchResult, LibraryResult, AnalysisResult
)
from src.core.interfaces.flow_interface import IFlow, FlowStatus
from src.orchestration.flows.task_manager_flow import TaskManagerFlow
from src.orchestration.flows.writer_flow import WriterFlow
from src.infrastructure.config.settings import get_llm_for_agent, load_configuration
from src.infrastructure.monitoring.metrics_collector import FlowMonitor
```

### 8.2 Update Existing Method Signatures

**Update the `_execute_with_reliability` method:**

```python
async def _execute_with_reliability(self, agent, query: str, agent_name: str) -> str:
    """Execute agent with reliability patterns (delegate to enhanced version)."""
    return await self._execute_with_enhanced_reliability(agent, query, agent_name)
```

---

## Task 9: Testing and Validation

### 9.1 Create Test Script

**File:** `test_enhanced_flow.py`

```python
#!/usr/bin/env python
"""
Test script for Enhanced Coordination Flow.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.orchestration.flows.advanced_coordination_flow import AdvancedCoordinationFlow
from src.core.models.state_models import WorkflowState

async def test_enhanced_flow():
    """Test the enhanced coordination flow."""
    
    config = {
        'enhanced_flow': {
            'complexity_analysis': True,
            'parallel_processing': True,
            'dspy_integration': True
        },
        'dspy': {
            'model': 'openai/gpt-4o-mini',
            'training_enabled': False
        },
        'alert_thresholds': {
            'max_execution_time': 300
        }
    }
    
    # Test queries of different complexity
    test_queries = [
        "What is artificial intelligence?",  # Simple
        "Compare machine learning algorithms for image recognition",  # Medium 
        "Analyze the economic impact of renewable energy adoption across different sectors with statistical projections"  # Complex
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*60}")
        print(f"TEST {i}: {query}")
        print('='*60)
        
        try:
            # Create enhanced workflow
            workflow = AdvancedCoordinationFlow(config)
            
            # Set initial state
            workflow.state = WorkflowState()
            workflow.state.original_query = query
            
            # Execute workflow
            result = await workflow.kickoff()
            
            print(f"✅ Test {i} completed successfully")
            print(f"Result: {str(result)[:200]}...")
            
        except Exception as e:
            print(f"❌ Test {i} failed: {str(e)}")
    
    print(f"\n{'='*60}")
    print("Testing completed")
    print('='*60)

if __name__ == "__main__":
    asyncio.run(test_enhanced_flow())
```

### 9.2 Create CLI Test Script

**File:** `test_cli_integration.py`

```python
#!/usr/bin/env python
"""
Test CLI integration for enhanced flow.
"""

import subprocess
import sys

def test_cli_integration():
    """Test CLI parameter integration."""
    
    test_cases = [
        {
            "name": "Default Flow",
            "command": ["python", "src/main.py", "What is AI?"],
            "should_contain": "Main Workflow (Default)"
        },
        {
            "name": "Enhanced Flow via CLI",
            "command": ["python", "src/main.py", "--enhanced-flow", "What is AI?"],
            "should_contain": "Enhanced Coordination Flow"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        print(f"Command: {' '.join(test_case['command'])}")
        
        try:
            result = subprocess.run(
                test_case['command'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if test_case['should_contain'] in result.stdout:
                print(f"✅ {test_case['name']} - PASSED")
            else:
                print(f"❌ {test_case['name']} - FAILED")
                print(f"Expected: {test_case['should_contain']}")
                print(f"Output: {result.stdout[:200]}...")
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_case['name']} - TIMEOUT")
        except Exception as e:
            print(f"❌ {test_case['name']} - ERROR: {str(e)}")

if __name__ == "__main__":
    test_cli_integration()
```

---

## Task 10: Documentation and Final Integration

### 10.1 Update README.md

**Add this section to README.md:**

```markdown
## Enhanced Coordination Flow

The system now supports an Advanced Coordination Flow with enhanced capabilities:

### Activation Methods

1. **CLI Parameter:**
   ```bash
   python src/main.py --enhanced-flow "Your question here"
   ```

2. **Configuration File:**
   Set `ENHANCED_FLOW: true` in `config.yaml`

### Enhanced Features

- ✅ **Dynamic Complexity Analysis** - AI-powered query analysis for optimal routing
- ✅ **Intelligent Workflow Routing** - Automatic selection of simple/parallel/complex workflows  
- ✅ **Enhanced Specialist Integration** - All Phase 0 Quick Wins capabilities
- ✅ **Real-time Monitoring** - Advanced error recovery and performance tracking
- ✅ **DSPy Chain-of-Thought** - Reasoning-based decision making
- ✅ **Training Data Collection** - Continuous learning and optimization

### Workflow Comparison

| Feature | Main Workflow | Enhanced Coordination Flow |
|---------|---------------|----------------------------|
| Task Planning | ✅ TaskManagerFlow | ✅ TaskManagerFlow + Complexity Analysis |
| Specialist Execution | ✅ Parallel Enhanced Specialists | ✅ Dynamic Routing + Enhanced Specialists |
| Final Synthesis | ✅ WriterFlow | ✅ WriterFlow + Enhancement Summary |
| Error Recovery | ✅ Basic | ✅ Advanced with Graceful Degradation |
| Monitoring | ✅ Basic | ✅ Real-time with Alerts |
| Training Data | ✅ Collection | ✅ Collection + DSPy Integration |
```

### 10.2 Create Migration Guide

**File:** `ENHANCED_FLOW_MIGRATION.md`

```markdown
# Enhanced Coordination Flow - Migration Guide

## Overview

This guide explains how to migrate from the Main Workflow to the Enhanced Coordination Flow.

## Key Differences

### Architecture
- **Main Workflow**: Sequential phases with parallel specialist execution
- **Enhanced Flow**: Dynamic routing based on complexity analysis

### Activation
- **Main Workflow**: Default behavior
- **Enhanced Flow**: Activated via `--enhanced-flow` or `ENHANCED_FLOW=true`

## Migration Steps

1. **Preserve Existing Setup**: Main workflow remains unchanged and fully functional
2. **Test Enhanced Flow**: Use `--enhanced-flow` parameter to test new capabilities
3. **Configure DSPy**: Set up DSPy configuration in `config.yaml`
4. **Monitor Performance**: Compare results between flows
5. **Gradual Rollout**: Use configuration to enable enhanced flow when ready

## Configuration

```yaml
# config.yaml
ENHANCED_FLOW: false  # Set to true to make enhanced flow default

enhanced_flow:
  complexity_analysis: true
  parallel_processing: true
  dspy_integration: true
  
dspy:
  model: "openai/gpt-4o-mini"
  training_enabled: true
```

## Testing

```bash
# Test main workflow (default)
python src/main.py "What is renewable energy?"

# Test enhanced flow
python src/main.py --enhanced-flow "What is renewable energy?"
```
```

---

## Summary of Implementation Tasks

### Priority 1: Core Integration (Required)
1. ✅ **Task 1**: CLI Parameter and Configuration Support
2. ✅ **Task 2**: TaskManagerFlow Integration  
3. ✅ **Task 3**: Enhanced Specialist Flows Integration
4. ✅ **Task 4**: WriterFlow Integration
5. ✅ **Task 5**: State Management and Phase Tracking

### Priority 2: Advanced Features (Important)
6. ✅ **Task 6**: Training Data Collection and DSPy Integration
7. ✅ **Task 7**: Error Handling and Recovery
8. ✅ **Task 8**: Import Statements and Dependencies

### Priority 3: Testing and Documentation (Essential)
9. ✅ **Task 9**: Testing and Validation
10. ✅ **Task 10**: Documentation and Final Integration

## Implementation Order

1. **Start with Task 1** - CLI and configuration support
2. **Implement Tasks 2-5** - Core workflow integration
3. **Add Tasks 6-8** - Advanced features and error handling
4. **Complete Tasks 9-10** - Testing and documentation

## Success Criteria

- ✅ Enhanced flow can be activated via `--enhanced-flow` or config
- ✅ All features from MainWorkflowFlow are properly integrated
- ✅ TaskManagerFlow, enhanced specialists, and WriterFlow work correctly
- ✅ Error handling and graceful degradation implemented
- ✅ Training data collection and DSPy integration functional
- ✅ Main workflow remains unchanged and working
- ✅ Comprehensive testing validates all functionality

## Critical Notes

1. **DO NOT MODIFY** `src/orchestration/flows/main_workflow.py`
2. **DO NOT MODIFY** any working specialist flows or components
3. **ONLY ENHANCE** `src/orchestration/flows/advanced_coordination_flow.py`
4. **BASE ALL PATTERNS** on working code from MainWorkflowFlow
5. **TEST THOROUGHLY** before considering implementation complete

This implementation preserves the working system while adding advanced capabilities through the enhanced coordination flow, providing users with both stable and cutting-edge options. 