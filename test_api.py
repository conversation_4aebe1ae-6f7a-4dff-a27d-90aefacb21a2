#!/usr/bin/env python3
"""
Test script for the DSPy Multi-Agent System API.

This script tests the basic functionality of the API endpoints.
"""

import asyncio
import aiohttp
import json
import time
from pathlib import Path


class APITester:
    """Test client for the DSPy Multi-Agent System API."""
    
    def __init__(self, base_url="http://localhost:8000", api_key="dev-api-key-12345"):
        self.base_url = base_url
        self.api_key = api_key
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            headers={"Authorization": f"Bearer {self.api_key}"}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def test_health(self):
        """Test health check endpoint."""
        print("🏥 Testing health check...")
        
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                data = await response.json()
                
                if response.status == 200:
                    print(f"   ✅ Health check passed: {data['status']}")
                    print(f"   📊 Components: {len(data.get('components', {}))}")
                    return True
                else:
                    print(f"   ❌ Health check failed: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"   ❌ Health check error: {e}")
            return False
    
    async def test_simple_question(self):
        """Test simple question endpoint."""
        print("❓ Testing simple question...")
        
        try:
            payload = {
                "question": "What are the latest developments in renewable energy?",
                "session_id": "test_session_123",
                "language": "en"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/questions/simple",
                json=payload
            ) as response:
                data = await response.json()
                
                if response.status == 200:
                    workflow_id = data.get("workflow_id")
                    print(f"   ✅ Question submitted: {workflow_id}")
                    return workflow_id
                else:
                    print(f"   ❌ Question failed: {response.status} - {data}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Question error: {e}")
            return None
    
    async def test_workflow_status(self, workflow_id):
        """Test workflow status endpoint."""
        print(f"📊 Testing workflow status for {workflow_id}...")
        
        try:
            async with self.session.get(
                f"{self.base_url}/api/v1/workflows/{workflow_id}/status"
            ) as response:
                data = await response.json()
                
                if response.status == 200:
                    status = data.get("status")
                    phase = data.get("current_phase")
                    print(f"   ✅ Status retrieved: {status} ({phase})")
                    return data
                else:
                    print(f"   ❌ Status failed: {response.status} - {data}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Status error: {e}")
            return None
    
    async def test_file_upload(self):
        """Test file upload endpoint."""
        print("📁 Testing file upload...")
        
        try:
            # Create a test file
            test_content = "This is a test file for the DSPy Multi-Agent System API.\n\nIt contains some sample text for processing."
            
            data = aiohttp.FormData()
            data.add_field('session_id', 'test_session_123')
            data.add_field('file_name', 'test_document.txt')
            data.add_field('file', test_content, filename='test_document.txt', content_type='text/plain')
            
            async with self.session.post(
                f"{self.base_url}/api/v1/files/upload",
                data=data
            ) as response:
                result = await response.json()
                
                if response.status == 200:
                    file_id = result.get("file_id")
                    print(f"   ✅ File uploaded: {file_id}")
                    return file_id
                else:
                    print(f"   ❌ Upload failed: {response.status} - {result}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Upload error: {e}")
            return None
    
    async def test_session_files(self):
        """Test session files listing."""
        print("📋 Testing session files listing...")
        
        try:
            async with self.session.get(
                f"{self.base_url}/api/v1/files/test_session_123"
            ) as response:
                data = await response.json()
                
                if response.status == 200:
                    files = data.get("files", [])
                    print(f"   ✅ Files listed: {len(files)} files")
                    return files
                else:
                    print(f"   ❌ Listing failed: {response.status} - {data}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Listing error: {e}")
            return None
    
    async def test_advanced_question(self):
        """Test advanced question endpoint."""
        print("🧠 Testing advanced question...")
        
        try:
            payload = {
                "question": "Analyze the economic impact of artificial intelligence on employment",
                "session_id": "test_session_123",
                "workflow_type": "enhanced",
                "config": {
                    "enable_optimization": True,
                    "max_iterations": 3,
                    "quality_threshold": 0.8
                }
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/questions/advanced",
                json=payload
            ) as response:
                data = await response.json()
                
                if response.status == 200:
                    workflow_id = data.get("workflow_id")
                    print(f"   ✅ Advanced question submitted: {workflow_id}")
                    return workflow_id
                else:
                    print(f"   ❌ Advanced question failed: {response.status} - {data}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Advanced question error: {e}")
            return None
    
    async def monitor_workflow(self, workflow_id, max_wait=30):
        """Monitor workflow until completion."""
        print(f"⏳ Monitoring workflow {workflow_id}...")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            status_data = await self.test_workflow_status(workflow_id)
            
            if status_data:
                status = status_data.get("status")
                
                if status == "completed":
                    final_answer = status_data.get("final_answer", "")
                    print(f"   ✅ Workflow completed!")
                    if final_answer:
                        print(f"   📝 Answer preview: {final_answer[:100]}...")
                    return True
                elif status == "failed":
                    print(f"   ❌ Workflow failed")
                    return False
                elif status in ["pending", "executing"]:
                    progress = status_data.get("progress", {})
                    percentage = progress.get("completion_percentage", 0)
                    print(f"   ⏳ Progress: {percentage:.1f}%")
                
            await asyncio.sleep(2)
        
        print(f"   ⏰ Workflow monitoring timed out after {max_wait}s")
        return False


async def main():
    """Main test function."""
    print("🧪 DSPy Multi-Agent System API Test Suite")
    print("=" * 50)
    
    async with APITester() as tester:
        # Test health check
        health_ok = await tester.test_health()
        if not health_ok:
            print("❌ Health check failed - API may not be running")
            return
        
        print()
        
        # Test file upload
        file_id = await tester.test_file_upload()
        print()
        
        # Test session files listing
        files = await tester.test_session_files()
        print()
        
        # Test simple question
        simple_workflow_id = await tester.test_simple_question()
        print()
        
        # Test advanced question
        advanced_workflow_id = await tester.test_advanced_question()
        print()
        
        # Monitor workflows
        if simple_workflow_id:
            await tester.monitor_workflow(simple_workflow_id, max_wait=60)
            print()
        
        if advanced_workflow_id:
            await tester.monitor_workflow(advanced_workflow_id, max_wait=60)
            print()
    
    print("🎉 Test suite completed!")


if __name__ == "__main__":
    print("Starting API tests...")
    print("Make sure the API server is running: python run_api.py")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
