#!/usr/bin/env python3
"""Debug script to test config loading issue with evaluation.enabled"""

import yaml
from pathlib import Path
import sys
import os

# Add src to path
sys.path.insert(0, 'src')

def test_config_loading():
    """Test the config loading mechanism"""
    print("🔍 Testing Configuration Loading")
    print("=" * 50)
    
    # Test 1: Direct YAML loading
    print("Test 1: Direct YAML loading")
    config_path = Path("config.yaml")
    if config_path.exists():
        with open(config_path, 'r') as f:
            direct_config = yaml.safe_load(f)
        
        eval_section = direct_config.get('evaluation', {})
        eval_enabled = eval_section.get('enabled', False)
        
        print(f"   Raw evaluation section: {eval_section}")
        print(f"   evaluation.enabled value: {eval_enabled}")
        print(f"   evaluation.enabled type: {type(eval_enabled)}")
        print(f"   evaluation.enabled == True: {eval_enabled == True}")
        print(f"   bool(evaluation.enabled): {bool(eval_enabled)}")
        print()
    
    # Test 2: Using the load_config_file function
    print("Test 2: Using load_config_file() function")
    try:
        from main import load_config_file
        loaded_config = load_config_file()
        
        eval_section2 = loaded_config.get('evaluation', {})
        eval_enabled2 = eval_section2.get('enabled', False)
        
        print(f"   load_config_file() evaluation section: {eval_section2}")
        print(f"   load_config_file() evaluation.enabled: {eval_enabled2}")
        print(f"   load_config_file() evaluation.enabled type: {type(eval_enabled2)}")
        print(f"   load_config_file() evaluation.enabled == True: {eval_enabled2 == True}")
        print()
        
        # Compare the two results
        if eval_enabled == eval_enabled2:
            print("✅ Both methods return the same value")
        else:
            print("❌ Different values returned!")
            print(f"   Direct YAML: {eval_enabled}")
            print(f"   load_config_file(): {eval_enabled2}")
        
    except Exception as e:
        print(f"❌ Error loading with load_config_file(): {e}")
    
    # Test 3: Check if there are any environment variables interfering
    print("Test 3: Environment variables check")
    env_vars = [key for key in os.environ.keys() if 'eval' in key.lower() or 'config' in key.lower()]
    if env_vars:
        print(f"   Found potentially relevant env vars: {env_vars}")
        for var in env_vars:
            print(f"   {var}: {os.environ[var]}")
    else:
        print("   No relevant environment variables found")
    print()

if __name__ == "__main__":
    test_config_loading() 