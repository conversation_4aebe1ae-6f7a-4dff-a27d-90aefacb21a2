#!/usr/bin/env python
"""
Test script for the Enhanced Evaluation Pipeline implementation.

This script tests the core functionality of the automated evaluation system
without requiring a full workflow execution.
"""

import asyncio
import sys
from pathlib import Path
import yaml

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_config():
    """Load configuration from config.yaml."""
    config_path = Path("config.yaml")
    if config_path.exists():
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    return {}

async def test_evaluation_pipeline():
    """Test the evaluation pipeline components."""
    print("🧪 Testing Enhanced Evaluation Pipeline")
    print("=" * 60)
    
    # Load configuration
    config = load_config()
    print(f"✅ Configuration loaded: evaluation enabled = {config.get('evaluation', {}).get('enabled', False)}")
    
    # Test data
    test_question = "What are the differences between renewable energy sources?"
    test_answer = """Renewable energy sources differ in several key ways:

1. Solar Energy: Converts sunlight directly into electricity using photovoltaic panels. Advantages include abundance and decreasing costs. Disadvantages include intermittency and weather dependence.

2. Wind Energy: Uses wind turbines to generate electricity. Benefits include high efficiency and scalability. Drawbacks include noise and visual impact.

3. Hydroelectric Power: Harnesses flowing water to generate electricity. Provides consistent power output but requires suitable geographic locations.

4. Geothermal Energy: Uses earth's internal heat for power generation. Offers stable output but limited to geologically active areas.

Each source has unique environmental impacts, costs, and implementation requirements."""
    
    test_context = {
        'instructions': 'Compare and contrast different renewable energy sources',
        'available_tools': ['web_search', 'document_analysis'],
        'workflow_type': 'test'
    }
    
    test_tools_used = ['web_search']
    
    # Test 1: Import evaluation modules
    print("\n🔧 Test 1: Import Evaluation Modules")
    try:
        from src.optimization.dspy.evaluation.decomposed_evaluators import EvaluatorFactory, quick_comprehensive_eval
        from src.optimization.dspy.evaluation.evaluation_pipeline import AutomatedEvaluationPipeline, quick_evaluate
        from src.optimization.dspy.evaluation.quality_gates import QualityGateSystem, QualityThresholds, quick_quality_check
        print("✅ All evaluation modules imported successfully")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Test 2: Initialize evaluation pipeline
    print("\n🔧 Test 2: Initialize Evaluation Pipeline")
    try:
        # Disable evaluation in config for this test (avoid API calls)
        test_config = config.copy()
        test_config['evaluation'] = test_config.get('evaluation', {})
        test_config['evaluation']['enabled'] = False  # Disable for testing
        
        pipeline = AutomatedEvaluationPipeline(test_config)
        print(f"✅ Evaluation pipeline initialized (enabled: {pipeline.config.enabled})")
        
        summary = pipeline.get_evaluation_summary()
        print(f"   📊 Metrics enabled: {list(summary['metrics_enabled'].keys())}")
        print(f"   ⚖️ Weights: {summary['weights']}")
    except Exception as e:
        print(f"❌ Pipeline initialization failed: {e}")
        return False
    
    # Test 3: Initialize quality gates
    print("\n🔧 Test 3: Initialize Quality Gates") 
    try:
        thresholds = QualityThresholds.from_config(test_config)
        quality_gates = QualityGateSystem(thresholds, test_config)
        
        gate_summary = quality_gates.get_gate_summary()
        print(f"✅ Quality gates initialized (enabled: {gate_summary['enabled']})")
        print(f"   🎯 Thresholds: {gate_summary['thresholds']}")
        print(f"   📈 Quality levels: {gate_summary['quality_levels']}")
    except Exception as e:
        print(f"❌ Quality gates initialization failed: {e}")
        return False
    
    # Test 4: Test evaluation components with mock data
    print("\n🔧 Test 4: Test Evaluation Components (Mock Mode)")
    try:
        # Create mock evaluation results
        mock_evaluation_results = {
            'composite_score': 0.85,
            'relevance_score': 0.88,
            'coherence_score': 0.82,
            'instruction_following_score': 0.87,
            'tool_efficiency_score': 0.83,
            'relevance_reasoning': 'Answer directly addresses the question about renewable energy differences',
            'coherence_issues': 'No significant coherence issues found',
            'missed_requirements': 'All requirements addressed',
            'tool_suggestions': 'Good use of available tools'
        }
        
        print("✅ Mock evaluation results created:")
        print(f"   📊 Composite Score: {mock_evaluation_results['composite_score']:.3f}")
        print(f"   🎯 Relevance: {mock_evaluation_results['relevance_score']:.3f}")
        print(f"   🧠 Coherence: {mock_evaluation_results['coherence_score']:.3f}")
        
        # Test quality gates with mock data
        gate_result = await quality_gates.evaluate_quality_gates(
            mock_evaluation_results,
            {'session_id': 'test_session', 'query': test_question}
        )
        
        print(f"✅ Quality gates evaluation:")
        print(f"   ✅ Passed gates: {gate_result.passed_gates}")
        print(f"   ❌ Failed gates: {gate_result.failed_gates}")
        print(f"   🏆 Overall quality: {gate_result.overall_quality.value}")
        print(f"   🔄 Actions triggered: {gate_result.actions_triggered}")
        
    except Exception as e:
        print(f"❌ Evaluation components test failed: {e}")
        return False
    
    # Test 5: Test configuration parsing
    print("\n🔧 Test 5: Test Configuration Parsing")
    try:
        eval_config = config.get('evaluation', {})
        metrics_config = eval_config.get('metrics', {})
        gates_config = eval_config.get('quality_gates', {})
        
        print("✅ Configuration parsing successful:")
        print(f"   🎛️ Evaluation enabled: {eval_config.get('enabled', False)}")
        print(f"   📊 Metrics configured: {len(metrics_config)} metrics")
        print(f"   🚪 Quality gates enabled: {gates_config.get('enabled', False)}")
        print(f"   🔄 Escalation actions: {len(gates_config.get('escalation', {}))}")
        
    except Exception as e:
        print(f"❌ Configuration parsing failed: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 ALL EVALUATION PIPELINE TESTS PASSED!")
    print("✅ Implementation is ready for integration")
    print("=" * 60)
    
    return True

async def test_with_api_calls():
    """Test with actual API calls (requires API key)."""
    print("\n🌐 Testing with API Calls (Optional)")
    print("Note: This requires a valid OpenAI API key")
    
    import os
    if not os.getenv('OPENAI_API_KEY'):
        print("⚠️ No OPENAI_API_KEY found, skipping API tests")
        return True
    
    # This would test actual evaluation calls
    print("📡 API tests would go here...")
    return True

if __name__ == "__main__":
    async def main():
        success = await test_evaluation_pipeline()
        if success:
            await test_with_api_calls()
        return success
    
    asyncio.run(main()) 